"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4905],{

/***/ 24905:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(70572);
/* harmony import */ var _accessibility_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(76413);

var _templateObject, _templateObject2;



var TestContainer = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: 20px;\n  background-color: ", ";\n  color: ", ";\n"])), function (props) {
  var _props$theme, _props$theme2, _props$theme3;
  if ((_props$theme = props.theme) !== null && _props$theme !== void 0 && (_props$theme = _props$theme.colorPalette) !== null && _props$theme !== void 0 && _props$theme.background) return props.theme.colorPalette.background;
  if ((_props$theme2 = props.theme) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.colors) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.background) !== null && _props$theme2 !== void 0 && _props$theme2["default"]) return props.theme.colors.background["default"];
  if ((_props$theme3 = props.theme) !== null && _props$theme3 !== void 0 && _props$theme3.backgroundColor) return props.theme.backgroundColor;
  return '#FFFFFF';
}, function (props) {
  var _props$theme4, _props$theme5, _props$theme6;
  if ((_props$theme4 = props.theme) !== null && _props$theme4 !== void 0 && (_props$theme4 = _props$theme4.colorPalette) !== null && _props$theme4 !== void 0 && _props$theme4.textPrimary) return props.theme.colorPalette.textPrimary;
  if ((_props$theme5 = props.theme) !== null && _props$theme5 !== void 0 && (_props$theme5 = _props$theme5.colors) !== null && _props$theme5 !== void 0 && (_props$theme5 = _props$theme5.text) !== null && _props$theme5 !== void 0 && _props$theme5.primary) return props.theme.colors.text.primary;
  if ((_props$theme6 = props.theme) !== null && _props$theme6 !== void 0 && _props$theme6.textColor) return props.theme.textColor;
  return '#111827';
});
var TestButton = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.button(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  background-color: ", ";\n  color: white;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  margin: 10px;\n  \n  &:hover {\n    opacity: 0.8;\n  }\n"])), function (props) {
  var _props$theme7, _props$theme8, _props$theme9;
  if ((_props$theme7 = props.theme) !== null && _props$theme7 !== void 0 && (_props$theme7 = _props$theme7.colorPalette) !== null && _props$theme7 !== void 0 && _props$theme7.primary) return props.theme.colorPalette.primary;
  if ((_props$theme8 = props.theme) !== null && _props$theme8 !== void 0 && (_props$theme8 = _props$theme8.colors) !== null && _props$theme8 !== void 0 && (_props$theme8 = _props$theme8.primary) !== null && _props$theme8 !== void 0 && _props$theme8.main) return props.theme.colors.primary.main;
  if ((_props$theme9 = props.theme) !== null && _props$theme9 !== void 0 && _props$theme9.primaryColor) return props.theme.primaryColor;
  return '#2563EB';
});

/**
 * ThemeTest component
 * A simple test component to verify theme functionality
 */
var ThemeTest = function ThemeTest() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(TestContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("h2", null, "Theme Test Component"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("p", null, "This component tests if the theme is working properly."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_accessibility_SkipLink__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A, {
    targetId: "test-content"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(TestButton, {
    onClick: function onClick() {
      return alert('Theme test button clicked!');
    }
  }, "Test Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    id: "test-content"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("p", null, "This is the test content that the skip link should navigate to."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("p", null, "If you can see this page without errors, the theme fixes are working!")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeTest);

/***/ }),

/***/ 76413:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(70572);

var _templateObject;


var SkipLinkButton = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.a(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -40px;\n  left: 0;\n  background: ", ";\n  color: white;\n  padding: 8px;\n  z-index: ", ";\n  transition: top 0.3s;\n\n  &:focus {\n    top: 0;\n  }\n"])), function (props) {
  var _props$theme, _props$theme2, _props$theme3;
  // Handle different theme structures
  if ((_props$theme = props.theme) !== null && _props$theme !== void 0 && (_props$theme = _props$theme.colorPalette) !== null && _props$theme !== void 0 && _props$theme.primary) {
    return props.theme.colorPalette.primary;
  }
  if ((_props$theme2 = props.theme) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.colors) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.primary) !== null && _props$theme2 !== void 0 && _props$theme2.main) {
    return props.theme.colors.primary.main;
  }
  if ((_props$theme3 = props.theme) !== null && _props$theme3 !== void 0 && _props$theme3.primaryColor) {
    return props.theme.primaryColor;
  }
  // Fallback color
  return '#2563EB';
}, function (props) {
  var _props$theme4, _props$theme5;
  return ((_props$theme4 = props.theme) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.zIndex) === null || _props$theme4 === void 0 ? void 0 : _props$theme4.tooltip) || ((_props$theme5 = props.theme) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.zIndex) === null || _props$theme5 === void 0 ? void 0 : _props$theme5.modal) || 1000;
});

/**
 * SkipLink component
 * Provides a way for keyboard users to skip navigation and go directly to main content
 * This is an accessibility feature that is hidden until focused
 */
var SkipLink = function SkipLink(_ref) {
  var _ref$targetId = _ref.targetId,
    targetId = _ref$targetId === void 0 ? 'main-content' : _ref$targetId;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SkipLinkButton, {
    href: "#".concat(targetId)
  }, "Skip to main content");
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SkipLink);

/***/ })

}]);