"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1934],{

/***/ 38812:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   O2: () => (/* binding */ DashboardSkeleton)
/* harmony export */ });
/* unused harmony exports TableSkeleton, CardSkeleton, FormSkeleton */
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(70572);

var _templateObject;



var SkeletonCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n"])));
var TableSkeleton = function TableSkeleton(_ref) {
  var _ref$rows = _ref.rows,
    rows = _ref$rows === void 0 ? 5 : _ref$rows;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
    active: true,
    paragraph: {
      rows: 0
    }
  }), Array.from({
    length: rows
  }).map(function (_, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
      key: "table-skeleton-row-".concat(index),
      active: true,
      paragraph: {
        rows: 0
      },
      style: {
        marginBottom: 16
      }
    });
  }));
};
var CardSkeleton = function CardSkeleton(_ref2) {
  var _ref2$rows = _ref2.rows,
    rows = _ref2$rows === void 0 ? 3 : _ref2$rows;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SkeletonCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
    active: true,
    paragraph: {
      rows: rows
    }
  }));
};
var DashboardSkeleton = function DashboardSkeleton(_ref3) {
  var _ref3$cards = _ref3.cards,
    cards = _ref3$cards === void 0 ? 4 : _ref3$cards;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
      gap: 24
    }
  }, Array.from({
    length: cards
  }).map(function (_, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SkeletonCard, {
      key: "dashboard-skeleton-card-".concat(index)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
      active: true,
      paragraph: {
        rows: 2
      }
    }));
  }));
};
var FormSkeleton = function FormSkeleton(_ref4) {
  var _ref4$fields = _ref4.fields,
    fields = _ref4$fields === void 0 ? 4 : _ref4$fields;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, Array.from({
    length: fields
  }).map(function (_, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
      key: "form-skeleton-field-".concat(index)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA.Input, {
      style: {
        width: 150,
        marginBottom: 8
      },
      active: true
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA.Input, {
      style: {
        width: '100%',
        height: 40
      },
      active: true
    }));
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA.Button, {
    style: {
      width: 120,
      height: 40,
      marginTop: 16
    },
    active: true
  }));
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  TableSkeleton: TableSkeleton,
  CardSkeleton: CardSkeleton,
  DashboardSkeleton: DashboardSkeleton,
  FormSkeleton: FormSkeleton
});

/***/ }),

/***/ 66894:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   JT: () => (/* binding */ GridLayout),
/* harmony export */   LN: () => (/* binding */ PageContainer),
/* harmony export */   Lv: () => (/* binding */ EnhancedCard),
/* harmony export */   T6: () => (/* binding */ EnhancedParagraph),
/* harmony export */   jn: () => (/* binding */ PrimaryButton),
/* harmony export */   n5: () => (/* binding */ FlexContainer),
/* harmony export */   p1: () => (/* binding */ EnhancedTitle),
/* harmony export */   pK: () => (/* binding */ DashboardCard)
/* harmony export */ });
/* unused harmony exports Section, SecondaryButton, EnhancedInput, EnhancedTable */
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(70572);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10;


var Title = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Paragraph;
var Content = antd__WEBPACK_IMPORTED_MODULE_2__/* .Layout */ .PE.Content;

// Page container with proper spacing
var PageContainer = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(Content)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  \n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n"])));

// Section with proper spacing and optional background
var Section = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: 32px;\n  padding: ", ";\n  background-color: ", ";\n  border-radius: 8px;\n"])), function (props) {
  return props.padded ? '24px' : '0';
}, function (props) {
  return props.background ? props.background : 'transparent';
});

// Enhanced card with consistent styling
var EnhancedCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\n  }\n  \n  .ant-card-head {\n    border-bottom: 1px solid #f0f0f0;\n  }\n  \n  .ant-card-head-title {\n    font-weight: 600;\n  }\n"])));

// Dashboard card for metrics and stats
var DashboardCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(EnhancedCard)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 100%;\n  \n  .ant-card-body {\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    height: calc(100% - 57px); // Adjust for card header\n  }\n"])));

// Primary button with enhanced styling
var PrimaryButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 40px;\n  font-weight: 500;\n  \n  &.ant-btn-primary {\n    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);\n    \n    &:hover {\n      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n    }\n  }\n"])));

// Secondary button with enhanced styling
var SecondaryButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 40px;\n  font-weight: 500;\n"])));

// Enhanced title with proper spacing
var EnhancedTitle = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(Title)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: ", " !important;\n  color: ", ";\n"])), function (props) {
  return props.noMargin ? '0' : '24px';
}, function (props) {
  return props.color || 'inherit';
});

// Enhanced paragraph with proper spacing
var EnhancedParagraph = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(Paragraph)(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: ", " !important;\n  font-size: 16px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.65);\n"])), function (props) {
  return props.noMargin ? '0' : '16px';
});

// Enhanced input with consistent styling
var EnhancedInput = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Input */ .pd)(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 40px;\n"])));

// Enhanced table with consistent styling
var EnhancedTable = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Table */ .XI)(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  .ant-table {\n    border-radius: 8px;\n    overflow: hidden;\n  }\n  \n  .ant-table-thead > tr > th {\n    background-color: #fafafa;\n    font-weight: 600;\n  }\n"])));

// Grid layout for dashboard cards
var GridLayout = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 24px;\n  margin-bottom: 32px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n"])));

// Flex container for layout
var FlexContainer = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: ", ";\n  justify-content: ", ";\n  align-items: ", ";\n  flex-wrap: ", ";\n  gap: ", "px;\n"])), function (props) {
  return props.direction || 'row';
}, function (props) {
  return props.justify || 'flex-start';
}, function (props) {
  return props.align || 'flex-start';
}, function (props) {
  return props.wrap || 'nowrap';
}, function (props) {
  return props.gap || '0';
});
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  PageContainer: PageContainer,
  Section: Section,
  EnhancedCard: EnhancedCard,
  DashboardCard: DashboardCard,
  PrimaryButton: PrimaryButton,
  SecondaryButton: SecondaryButton,
  EnhancedTitle: EnhancedTitle,
  EnhancedParagraph: EnhancedParagraph,
  EnhancedInput: EnhancedInput,
  EnhancedTable: EnhancedTable,
  GridLayout: GridLayout,
  FlexContainer: FlexContainer
});

/***/ }),

/***/ 85331:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   tI: () => (/* binding */ setCurrentView)
/* harmony export */ });
/* unused harmony exports TOGGLE_SIDEBAR, SET_CURRENT_VIEW, TOGGLE_PREVIEW_MODE, UI_LOADING_START, UI_LOADING_COMPLETE, toggleSidebar, togglePreviewMode, startLoading, completeLoading */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UI Reducer
 *
 * This reducer handles UI state, including sidebar, current view, and preview mode.
 */

// Action types
var TOGGLE_SIDEBAR = 'TOGGLE_SIDEBAR';
var SET_CURRENT_VIEW = 'SET_CURRENT_VIEW';
var TOGGLE_PREVIEW_MODE = 'TOGGLE_PREVIEW_MODE';
var UI_LOADING_START = 'UI_LOADING_START';
var UI_LOADING_COMPLETE = 'UI_LOADING_COMPLETE';

// Initial state
var initialState = {
  sidebarOpen: true,
  currentView: 'components',
  previewMode: false,
  loading: false
};

/**
 * UI reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
var uiReducer = function uiReducer() {
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;
  switch (action.type) {
    case TOGGLE_SIDEBAR:
      return _objectSpread(_objectSpread({}, state), {}, {
        sidebarOpen: !state.sidebarOpen
      });
    case SET_CURRENT_VIEW:
      return _objectSpread(_objectSpread({}, state), {}, {
        currentView: action.payload
      });
    case TOGGLE_PREVIEW_MODE:
      return _objectSpread(_objectSpread({}, state), {}, {
        previewMode: !state.previewMode
      });
    case UI_LOADING_START:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: true
      });
    case UI_LOADING_COMPLETE:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: false
      });
    default:
      return state;
  }
};

// Action creators
var toggleSidebar = function toggleSidebar() {
  return {
    type: TOGGLE_SIDEBAR
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: SET_CURRENT_VIEW,
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: TOGGLE_PREVIEW_MODE
  };
};
var startLoading = function startLoading() {
  return {
    type: UI_LOADING_START
  };
};
var completeLoading = function completeLoading() {
  return {
    type: UI_LOADING_COMPLETE
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (uiReducer)));

/***/ }),

/***/ 91934:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_DashboardPage)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
;// ./src/theme/theme.js
var colors = {
  primary: {
    main: '#2563EB',
    light: '#DBEAFE',
    dark: '#1E40AF',
    contrastText: '#FFFFFF'
  },
  secondary: {
    main: '#10B981',
    light: '#D1FAE5',
    dark: '#047857',
    contrastText: '#FFFFFF'
  },
  background: {
    "default": '#F9FAFB',
    paper: '#FFFFFF',
    secondary: '#f0f2f5'
  },
  text: {
    primary: '#111827',
    secondary: '#4B5563',
    disabled: '#9CA3AF'
  },
  error: {
    main: '#DC2626',
    light: '#FEE2E2',
    dark: '#B91C1C'
  },
  warning: {
    main: '#FBBF24',
    light: '#FEF3C7',
    dark: '#D97706'
  },
  success: {
    main: '#10B981',
    light: '#D1FAE5',
    dark: '#047857'
  }
};
var spacing = {
  xs: '0.5rem',
  // 8px
  sm: '1rem',
  // 16px
  md: '1.5rem',
  // 24px
  lg: '2rem',
  // 32px
  xl: '2.5rem' // 40px
};
var typography = {
  fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
  fontWeights: {
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  },
  sizes: {
    xs: '0.75rem',
    // 12px
    sm: '0.875rem',
    // 14px
    base: '1rem',
    // 16px
    lg: '1.125rem',
    // 18px
    xl: '1.25rem' // 20px
  }
};
var theme = {
  colors: colors,
  spacing: spacing,
  typography: typography,
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
  },
  breakpoints: {
    xs: '0px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px'
  }
};
/* harmony default export */ const theme_theme = (theme);
;// ./src/contexts/ThemeContext.js


var ThemeContext = /*#__PURE__*/(0,react.createContext)(theme_theme);
/* harmony default export */ const contexts_ThemeContext = (ThemeContext);
;// ./src/hooks/useTheme.js



var useTheme = function useTheme() {
  var contextTheme = (0,react.useContext)(contexts_ThemeContext);
  return contextTheme || theme_theme;
};
// EXTERNAL MODULE: ./src/redux/reducers/uiReducer.js
var uiReducer = __webpack_require__(85331);
// EXTERNAL MODULE: ./src/services/WebSocketService.js
var WebSocketService = __webpack_require__(17053);
// EXTERNAL MODULE: ./src/components/common/StyledComponents.js
var StyledComponents = __webpack_require__(66894);
// EXTERNAL MODULE: ./src/components/common/SkeletonLoaders.js
var SkeletonLoaders = __webpack_require__(38812);
;// ./src/pages/DashboardPage.js



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









var TabPane = es/* Tabs */.tU.TabPane;

/**
 * DashboardPage component
 * Provides an overview of the application status and metrics
 */
var DashboardPage = function DashboardPage() {
  var _messageStats$latency;
  var theme = useTheme();
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var _useState = (0,react.useState)(true),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(WebSocketService/* default */.A.getConnectionState ? WebSocketService/* default */.A.getConnectionState() : {
      status: 'disconnected'
    }),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    connectionState = _useState4[0],
    setConnectionState = _useState4[1];
  var _useState5 = (0,react.useState)({
      sent: 0,
      received: 0,
      errors: 0,
      latency: []
    }),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    messageStats = _useState6[0],
    setMessageStats = _useState6[1];
  var _useState7 = (0,react.useState)({
      cpu: Math.random() * 30 + 10,
      memory: Math.random() * 40 + 20,
      disk: Math.random() * 20 + 5,
      uptime: Math.floor(Math.random() * 1000) + 100
    }),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    systemHealth = _useState8[0],
    setSystemHealth = _useState8[1];
  var _useState9 = (0,react.useState)('1'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    activeTab = _useState0[0],
    setActiveTab = _useState0[1];

  // Example of using theme properties safely
  var containerStyle = {
    backgroundColor: theme.colors.background["default"],
    padding: theme.spacing.md
  };
  var cardStyle = {
    backgroundColor: theme.colors.background.paper,
    borderRadius: theme.borderRadius.md,
    boxShadow: theme.shadows.sm
  };

  // Set current view in Redux store
  (0,react.useEffect)(function () {
    dispatch((0,uiReducer/* setCurrentView */.tI)('dashboard'));

    // Simulate loading
    var timer = setTimeout(function () {
      setLoading(false);
    }, 1500);
    return function () {
      return clearTimeout(timer);
    };
  }, [dispatch]);

  // WebSocket event handlers
  var handleConnect = function handleConnect() {
    setConnectionState(WebSocketService/* default */.A.getConnectionState ? WebSocketService/* default */.A.getConnectionState() : {
      status: 'disconnected'
    });
  };
  var handleDisconnect = function handleDisconnect() {
    setConnectionState(WebSocketService/* default */.A.getConnectionState ? WebSocketService/* default */.A.getConnectionState() : {
      status: 'disconnected'
    });
  };
  var handleError = function handleError() {
    setConnectionState(WebSocketService/* default */.A.getConnectionState ? WebSocketService/* default */.A.getConnectionState() : {
      status: 'disconnected'
    });
    setMessageStats(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        errors: prev.errors + 1
      });
    });
  };
  var handleMessage = function handleMessage(message) {
    setMessageStats(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        received: prev.received + 1,
        latency: [].concat((0,toConsumableArray/* default */.A)(prev.latency.slice(-50)), [{
          time: new Date().toISOString(),
          value: message.latency || 0
        }])
      });
    });
  };
  var handleSentMessage = function handleSentMessage() {
    setMessageStats(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        sent: prev.sent + 1
      });
    });
  };

  // Listen for WebSocket events
  (0,react.useEffect)(function () {
    WebSocketService/* default */.A.addListener('connect', handleConnect);
    WebSocketService/* default */.A.addListener('disconnect', handleDisconnect);
    WebSocketService/* default */.A.addListener('error', handleError);
    WebSocketService/* default */.A.addListener('message', handleMessage);
    WebSocketService/* default */.A.addListener('sent', handleSentMessage);

    // Initial state
    setConnectionState(WebSocketService/* default */.A.getConnectionState ? WebSocketService/* default */.A.getConnectionState() : {
      status: 'disconnected'
    });

    // Simulate system health updates
    var healthInterval = setInterval(function () {
      setSystemHealth(function (prev) {
        return {
          cpu: Math.min(Math.max(prev.cpu + (Math.random() - 0.5) * 10, 0), 100),
          memory: Math.min(Math.max(prev.memory + (Math.random() - 0.5) * 10, 0), 100),
          disk: Math.min(Math.max(prev.disk + (Math.random() - 0.5) * 5, 0), 100),
          uptime: prev.uptime + 1
        };
      });
    }, 5000);
    return function () {
      WebSocketService/* default */.A.off('connect', handleConnect);
      WebSocketService/* default */.A.off('disconnect', handleDisconnect);
      WebSocketService/* default */.A.off('error', handleError);
      WebSocketService/* default */.A.off('message', handleMessage);
      WebSocketService/* default */.A.off('sent', handleSentMessage);
      clearInterval(healthInterval);
    };
  }, []);

  // Get connection status badge
  var getConnectionStatusBadge = function getConnectionStatusBadge() {
    if (connectionState.connected) {
      return /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
        status: "success",
        text: "Connected"
      });
    } else if (connectionState.readyState === 0) {
      return /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
        status: "processing",
        text: "Connecting"
      });
    } else {
      return /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
        status: "error",
        text: "Disconnected"
      });
    }
  };

  // Format uptime
  var formatUptime = function formatUptime(minutes) {
    var days = Math.floor(minutes / 1440);
    var hours = Math.floor(minutes % 1440 / 60);
    var mins = minutes % 60;
    if (days > 0) {
      return "".concat(days, "d ").concat(hours, "h ").concat(mins, "m");
    } else if (hours > 0) {
      return "".concat(hours, "h ").concat(mins, "m");
    } else {
      return "".concat(mins, "m");
    }
  };

  // Get health status
  var getHealthStatus = function getHealthStatus(value) {
    if (value < 50) return 'success';
    if (value < 80) return 'warning';
    return 'exception';
  };

  // Latency chart config - will be used when we implement charts
  // const latencyChartConfig = {
  //   data: messageStats.latency,
  //   height: 200,
  //   xField: 'time',
  //   yField: 'value',
  //   point: {
  //     size: 5,
  //     shape: 'diamond',
  //   },
  //   tooltip: {
  //     formatter: (datum) => {
  //       return { name: 'Latency', value: `${datum.value} ms` };
  //     },
  //   },
  //   yAxis: {
  //     label: {
  //       formatter: (v) => `${v} ms`,
  //     },
  //   },
  //   smooth: true,
  //   lineStyle: {
  //     stroke: '#1890ff',
  //     lineWidth: 3,
  //   },
  //   areaStyle: {
  //     fill: 'l(270) 0:#ffffff 0.5:#1890ff 1:#1890ff',
  //     fillOpacity: 0.2,
  //   },
  // };

  return /*#__PURE__*/react.createElement(StyledComponents/* PageContainer */.LN, {
    style: containerStyle
  }, loading ? /*#__PURE__*/react.createElement(SkeletonLoaders/* DashboardSkeleton */.O2, null) : /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(StyledComponents/* EnhancedTitle */.p1, {
    level: 2,
    style: {
      color: theme.colors.text.primary,
      fontSize: theme.typography.sizes.xl,
      fontWeight: theme.typography.fontWeights.semibold
    }
  }, "Dashboard"), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    style: cardStyle
  }, /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null), "Overview"),
    key: "1"
  }, /*#__PURE__*/react.createElement(StyledComponents/* GridLayout */.JT, {
    columns: 3,
    columnsMd: 2,
    columnsSm: 1
  }, /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "WebSocket Status"
  }, /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
    direction: "column",
    gap: 16
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: 16
    }
  }, getConnectionStatusBadge()), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, "URL: ", connectionState.url || 'Not connected'), /*#__PURE__*/react.createElement("div", null, "Reconnect Attempts: ", connectionState.reconnectAttempts || 0)), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(StyledComponents/* PrimaryButton */.jn, {
    onClick: function onClick() {
      return WebSocketService/* default */.A.reconnect();
    },
    disabled: connectionState.connected
  }, connectionState.connected ? 'Connected' : 'Reconnect')))), /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "Message Statistics"
  }, /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
    direction: "column",
    gap: 16
  }, /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
    justify: "space-between"
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Sent",
    value: messageStats.sent,
    valueStyle: {
      color: '#3f8600'
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* ArrowUpOutlined */.lu9, null)
  }), /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Received",
    value: messageStats.received,
    valueStyle: {
      color: '#1890ff'
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* ArrowDownOutlined */.Axk, null)
  })), /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Errors",
    value: messageStats.errors,
    valueStyle: {
      color: messageStats.errors > 0 ? '#cf1322' : '#3f8600'
    },
    prefix: messageStats.errors > 0 ? /*#__PURE__*/react.createElement(icons_es/* WarningOutlined */.v7y, null) : /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null)
  }))), /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "System Health"
  }, /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
    direction: "column",
    gap: 8
  }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: 4
    }
  }, "CPU Usage"), /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: Math.round(systemHealth.cpu),
    status: getHealthStatus(systemHealth.cpu),
    size: "small"
  })), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: 4
    }
  }, "Memory Usage"), /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: Math.round(systemHealth.memory),
    status: getHealthStatus(systemHealth.memory),
    size: "small"
  })), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: 4
    }
  }, "Disk Usage"), /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: Math.round(systemHealth.disk),
    status: getHealthStatus(systemHealth.disk),
    size: "small"
  })), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Uptime",
    value: formatUptime(systemHealth.uptime),
    prefix: /*#__PURE__*/react.createElement(icons_es/* ClockCircleOutlined */.L8Y, null)
  })))), /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "Latency",
    extra: /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      icon: /*#__PURE__*/react.createElement(icons_es/* LineChartOutlined */.BdS, null),
      onClick: function onClick() {
        // Send a ping to measure latency
        WebSocketService/* default */.A.send({
          type: 'ping',
          timestamp: Date.now()
        });
      },
      disabled: !connectionState.connected
    }, "Ping"),
    style: {
      gridColumn: 'span 2'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      height: 200,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column',
      gap: '10px'
    }
  }, messageStats.latency.length > 0 ? /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", null, "Latest latency: ", ((_messageStats$latency = messageStats.latency[messageStats.latency.length - 1]) === null || _messageStats$latency === void 0 ? void 0 : _messageStats$latency.value) || 0, " ms"), /*#__PURE__*/react.createElement("div", null, "Average latency: ", messageStats.latency.length > 0 ? Math.round(messageStats.latency.reduce(function (sum, item) {
    return sum + item.value;
  }, 0) / messageStats.latency.length) : 0, " ms"), /*#__PURE__*/react.createElement("div", null, "Samples: ", messageStats.latency.length)) : /*#__PURE__*/react.createElement("span", null, "No latency data available. Click \"Ping\" to measure latency."))), /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "Recent Activity"
  }, /*#__PURE__*/react.createElement(es/* List */.B8, {
    size: "small",
    dataSource: [{
      title: 'WebSocket Connected',
      time: '2 minutes ago',
      type: 'success'
    }, {
      title: 'Message Received',
      time: '5 minutes ago',
      type: 'info'
    }, {
      title: 'Configuration Updated',
      time: '10 minutes ago',
      type: 'warning'
    }, {
      title: 'Application Started',
      time: '1 hour ago',
      type: 'success'
    }],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, null, /*#__PURE__*/react.createElement(es/* List */.B8.Item.Meta, {
        title: /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
          justify: "space-between"
        }, /*#__PURE__*/react.createElement("span", null, item.title), /*#__PURE__*/react.createElement(es/* Tag */.vw, {
          color: item.type === 'success' ? 'success' : item.type === 'warning' ? 'warning' : item.type === 'error' ? 'error' : 'blue'
        }, item.time))
      }));
    }
  }))), connectionState.lastError && /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    type: "error",
    message: "WebSocket Error",
    description: /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, connectionState.lastError.message), connectionState.lastError.timestamp && /*#__PURE__*/react.createElement("div", {
      style: {
        color: 'rgba(0, 0, 0, 0.45)',
        marginTop: 8
      }
    }, new Date(connectionState.lastError.timestamp).toLocaleString())),
    showIcon: true,
    style: {
      marginTop: 24
    }
  })), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* ApiOutlined */.bfv, null), "API Status"),
    key: "2"
  }, /*#__PURE__*/react.createElement(StyledComponents/* GridLayout */.JT, {
    columns: 1
  }, /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "API Endpoints"
  }, /*#__PURE__*/react.createElement(es/* List */.B8, {
    size: "large",
    dataSource: [{
      name: 'WebSocket API',
      status: connectionState.connected ? 'online' : 'offline',
      latency: messageStats.latency.length > 0 ? Math.round(messageStats.latency.reduce(function (sum, item) {
        return sum + item.value;
      }, 0) / messageStats.latency.length) : null,
      url: connectionState.url || 'ws://localhost:8000/ws'
    }, {
      name: 'REST API',
      status: 'online',
      latency: 45,
      url: 'http://localhost:8000/api'
    }, {
      name: 'Authentication Service',
      status: 'online',
      latency: 78,
      url: 'http://localhost:8000/auth'
    }, {
      name: 'Storage Service',
      status: 'online',
      latency: 112,
      url: 'http://localhost:8000/storage'
    }],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, {
        actions: [/*#__PURE__*/react.createElement(es/* Button */.$n, {
          key: "test",
          size: "small"
        }, "Test"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
          key: "details",
          size: "small"
        }, "Details")]
      }, /*#__PURE__*/react.createElement(es/* List */.B8.Item.Meta, {
        title: /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
          justify: "space-between"
        }, /*#__PURE__*/react.createElement("span", null, item.name), /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
          status: item.status === 'online' ? 'success' : 'error',
          text: item.status
        })),
        description: /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
          direction: "column",
          gap: 4
        }, /*#__PURE__*/react.createElement("div", null, item.url), item.latency && /*#__PURE__*/react.createElement("div", null, "Latency: ", /*#__PURE__*/react.createElement("span", {
          style: {
            color: item.latency < 100 ? '#3f8600' : item.latency < 300 ? '#faad14' : '#cf1322'
          }
        }, item.latency, " ms")))
      }));
    }
  })))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null), "Configuration"),
    key: "3"
  }, /*#__PURE__*/react.createElement(StyledComponents/* GridLayout */.JT, {
    columns: 2,
    columnsSm: 1
  }, /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "WebSocket Configuration"
  }, /*#__PURE__*/react.createElement(es/* List */.B8, {
    size: "small",
    dataSource: [{
      name: 'Reconnect Attempts',
      value: WebSocketService/* default */.A.maxReconnectAttempts
    }, {
      name: 'Message Queue Size',
      value: WebSocketService/* default */.A.messageQueueMaxSize
    }, {
      name: 'Compression',
      value: WebSocketService/* default */.A.performanceOptions.compression.enabled ? 'Enabled' : 'Disabled'
    }, {
      name: 'Compression Threshold',
      value: "".concat(WebSocketService/* default */.A.performanceOptions.compression.threshold, " bytes")
    }, {
      name: 'Batching',
      value: WebSocketService/* default */.A.performanceOptions.batchingEnabled ? 'Enabled' : 'Disabled'
    }, {
      name: 'Offline Queue',
      value: WebSocketService/* default */.A.performanceOptions.offlineQueueEnabled ? 'Enabled' : 'Disabled'
    }],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, null, /*#__PURE__*/react.createElement(es/* List */.B8.Item.Meta, {
        title: /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
          justify: "space-between"
        }, /*#__PURE__*/react.createElement("span", null, item.name), /*#__PURE__*/react.createElement("span", {
          style: {
            fontWeight: 'normal'
          }
        }, item.value))
      }));
    }
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react.createElement(StyledComponents/* PrimaryButton */.jn, null, "Edit Configuration"))), /*#__PURE__*/react.createElement(StyledComponents/* DashboardCard */.pK, {
    title: "Security Configuration"
  }, /*#__PURE__*/react.createElement(es/* List */.B8, {
    size: "small",
    dataSource: [{
      name: 'Message Validation',
      value: WebSocketService/* default */.A.securityOptions.validateMessages ? 'Enabled' : 'Disabled'
    }, {
      name: 'Message Sanitization',
      value: WebSocketService/* default */.A.securityOptions.sanitizeMessages ? 'Enabled' : 'Disabled'
    }, {
      name: 'Rate Limiting',
      value: WebSocketService/* default */.A.securityOptions.rateLimiting.enabled ? 'Enabled' : 'Disabled'
    }, {
      name: 'Max Messages Per Second',
      value: WebSocketService/* default */.A.securityOptions.rateLimiting.maxMessagesPerSecond
    }, {
      name: 'Burst Size',
      value: WebSocketService/* default */.A.securityOptions.rateLimiting.burstSize
    }, {
      name: 'Authentication',
      value: WebSocketService/* default */.A.authToken ? 'Enabled' : 'Disabled'
    }],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, null, /*#__PURE__*/react.createElement(es/* List */.B8.Item.Meta, {
        title: /*#__PURE__*/react.createElement(StyledComponents/* FlexContainer */.n5, {
          justify: "space-between"
        }, /*#__PURE__*/react.createElement("span", null, item.name), /*#__PURE__*/react.createElement("span", {
          style: {
            fontWeight: 'normal'
          }
        }, item.value))
      }));
    }
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react.createElement(StyledComponents/* PrimaryButton */.jn, null, "Edit Security Settings")))))))));
};
/* harmony default export */ const pages_DashboardPage = (DashboardPage);

/***/ })

}]);