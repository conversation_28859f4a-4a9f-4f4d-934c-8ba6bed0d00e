"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1680],{

/***/ 51680:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(71468);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(86020);
/* harmony import */ var _redux_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(81616);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }







var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var ProjectCard = (0,_design_system__WEBPACK_IMPORTED_MODULE_7__.styled)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    box-shadow: ", ";\n    transform: translateY(-2px);\n  }\n  \n  &.active {\n    border-left: 4px solid ", ";\n  }\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].spacing */ .Ay.spacing[3], _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].borderRadius */ .Ay.borderRadius.md, _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].shadows */ .Ay.shadows.sm, _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].shadows */ .Ay.shadows.md, _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].colors */ .Ay.colors.primary.main);
var ProjectHeader = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].spacing */ .Ay.spacing[2]);
var ProjectTitle = (0,_design_system__WEBPACK_IMPORTED_MODULE_7__.styled)(Title)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin: 0 !important;\n"])));
var ProjectDescription = (0,_design_system__WEBPACK_IMPORTED_MODULE_7__.styled)(Paragraph)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: ", " !important;\n  color: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].spacing */ .Ay.spacing[2], _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].colors */ .Ay.colors.neutral[600]);
var ProjectMeta = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: ", ";\n  font-size: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].colors */ .Ay.colors.neutral[500], _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].typography */ .Ay.typography.fontSize.sm);
var ProjectTags = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-top: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_8__/* ["default"].spacing */ .Ay.spacing[2]);

/**
 * ProjectManager component
 * Manages user projects
 */
var ProjectManager = function ProjectManager() {
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useDispatch */ .wA)();
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useSelector */ .d4)(function (state) {
      return state.projects;
    }),
    projects = _useSelector.projects,
    activeProject = _useSelector.activeProject;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    isModalVisible = _useState2[0],
    setIsModalVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    editingProject = _useState4[0],
    setEditingProject = _useState4[1];
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];

  // Load sample projects if none exist
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (projects.length === 0) {
      var sampleProjects = [{
        id: '1',
        name: 'E-commerce Dashboard',
        description: 'Admin dashboard for an e-commerce platform',
        tags: ['dashboard', 'e-commerce'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        components: 12,
        layouts: 5
      }, {
        id: '2',
        name: 'Blog Template',
        description: 'Responsive blog template with multiple layouts',
        tags: ['blog', 'responsive'],
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        updatedAt: new Date(Date.now() - 86400000).toISOString(),
        components: 8,
        layouts: 3
      }, {
        id: '3',
        name: 'Portfolio Site',
        description: 'Personal portfolio website template',
        tags: ['portfolio', 'personal'],
        createdAt: new Date(Date.now() - 172800000).toISOString(),
        updatedAt: new Date(Date.now() - 172800000).toISOString(),
        components: 6,
        layouts: 2
      }];
      dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_9__/* .loadProjects */ .RT)(sampleProjects));
      dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_9__/* .setActiveProject */ .eL)('1'));
    }
  }, [dispatch, projects.length]);

  // Handle project selection
  var handleSelectProject = function handleSelectProject(projectId) {
    dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_9__/* .setActiveProject */ .eL)(projectId));
  };

  // Show create/edit project modal
  var showModal = function showModal() {
    var project = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    setEditingProject(project);
    if (project) {
      form.setFieldsValue({
        name: project.name,
        description: project.description,
        tags: project.tags
      });
    } else {
      form.resetFields();
    }
    setIsModalVisible(true);
  };

  // Handle modal cancel
  var handleCancel = function handleCancel() {
    setIsModalVisible(false);
    setEditingProject(null);
    form.resetFields();
  };

  // Handle form submission
  var handleSubmit = function handleSubmit(values) {
    if (editingProject) {
      // Update existing project
      var updatedProject = _objectSpread(_objectSpread(_objectSpread({}, editingProject), values), {}, {
        updatedAt: new Date().toISOString()
      });
      dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_9__/* .updateProject */ .vr)(updatedProject));
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Project updated successfully');
    } else {
      // Create new project
      var newProject = _objectSpread(_objectSpread({
        id: Date.now().toString()
      }, values), {}, {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        components: 0,
        layouts: 0
      });
      dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_9__/* .createProject */ .gA)(newProject));
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Project created successfully');
    }
    setIsModalVisible(false);
    setEditingProject(null);
    form.resetFields();
  };

  // Handle project deletion
  var handleDeleteProject = function handleDeleteProject(projectId) {
    antd__WEBPACK_IMPORTED_MODULE_5__/* .Modal */ .aF.confirm({
      title: 'Delete Project',
      content: 'Are you sure you want to delete this project? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: function onOk() {
        dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_9__/* .deleteProject */ .xx)(projectId));
        antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Project deleted successfully');
      }
    });
  };

  // Format date
  var formatDate = function formatDate(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "project-manager"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ProjectHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 3
  }, "My Projects"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PlusOutlined */ .bW0, null),
    onClick: function onClick() {
      return showModal();
    }
  }, "New Project")), projects.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Empty */ .Sv, {
    description: "No projects found",
    image: antd__WEBPACK_IMPORTED_MODULE_5__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .List */ .B8, {
    dataSource: projects,
    renderItem: function renderItem(project) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ProjectCard, {
        className: activeProject === project.id ? 'active' : '',
        onClick: function onClick() {
          return handleSelectProject(project.id);
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ProjectHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ProjectTitle, {
        level: 4
      }, project.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Dropdown */ .ms, {
        overlay: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
          key: "edit",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EditOutlined */ .xjh, null),
          onClick: function onClick(e) {
            e.stopPropagation();
            showModal(project);
          }
        }, "Edit"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
          key: "duplicate",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CopyOutlined */ .wq3, null),
          onClick: function onClick(e) {
            e.stopPropagation();
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.info('Duplicate feature coming soon');
          }
        }, "Duplicate"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
          key: "export",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ExportOutlined */ .PZg, null),
          onClick: function onClick(e) {
            e.stopPropagation();
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.info('Export feature coming soon');
          }
        }, "Export"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Divider, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
          key: "delete",
          danger: true,
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DeleteOutlined */ .SUY, null),
          onClick: function onClick(e) {
            e.stopPropagation();
            handleDeleteProject(project.id);
          }
        }, "Delete")),
        trigger: ['click']
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
        type: "text",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .MoreOutlined */ .a7l, null),
        onClick: function onClick(e) {
          return e.stopPropagation();
        }
      }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ProjectDescription, null, project.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ProjectTags, null, project.tags.map(function (tag) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tag */ .vw, {
          key: tag,
          color: "blue",
          style: {
            marginBottom: '8px'
          }
        }, tag);
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ProjectMeta, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        type: "secondary"
      }, project.components, " components \xB7 ", project.layouts, " layouts")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        type: "secondary"
      }, "Updated ", formatDate(project.updatedAt)))));
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Modal */ .aF, {
    title: editingProject ? 'Edit Project' : 'Create Project',
    visible: isModalVisible,
    onCancel: handleCancel,
    footer: null
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV, {
    form: form,
    layout: "vertical",
    onFinish: handleSubmit
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "name",
    label: "Project Name",
    rules: [{
      required: true,
      message: 'Please enter a project name'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: "Enter project name"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "description",
    label: "Description",
    rules: [{
      required: true,
      message: 'Please enter a description'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.TextArea, {
    placeholder: "Enter project description",
    rows: 3
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "tags",
    label: "Tags"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    mode: "tags",
    placeholder: "Add tags",
    style: {
      width: '100%'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    style: {
      display: 'flex',
      justifyContent: 'flex-end'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    onClick: handleCancel
  }, "Cancel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    htmlType: "submit"
  }, editingProject ? 'Update' : 'Create'))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectManager);

/***/ })

}]);