"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7496],{

/***/ 22827:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {


// UNUSED EXPORTS: default

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/@rc-component/mutate-observer/es/index.js + 3 modules
var es = __webpack_require__(84648);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useEvent.js
var hooks_useEvent = __webpack_require__(26956);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/toList.js
var _util_toList = __webpack_require__(81168);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/internal.js + 3 modules
var internal = __webpack_require__(51113);
// EXTERNAL MODULE: ./node_modules/antd/es/watermark/context.js
var context = __webpack_require__(28557);
;// ./node_modules/antd/es/watermark/useClips.js


const useClips_FontGap = 3;
const prepareCanvas = (width, height, ratio = 1) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const realWidth = width * ratio;
  const realHeight = height * ratio;
  canvas.setAttribute('width', `${realWidth}px`);
  canvas.setAttribute('height', `${realHeight}px`);
  ctx.save();
  return [ctx, canvas, realWidth, realHeight];
};
// Get boundary of rotated text
const getRotatePos = (x, y, angle) => {
  const targetX = x * Math.cos(angle) - y * Math.sin(angle);
  const targetY = x * Math.sin(angle) + y * Math.cos(angle);
  return [targetX, targetY];
};
/**
 * Get the clips of text content.
 * This is a lazy hook function since SSR no need this
 */
const useClips_useClips = () => {
  // Get single clips
  const getClips = (content, rotate, ratio, width, height, font, gapX, gapY) => {
    // ================= Text / Image =================
    const [ctx, canvas, contentWidth, contentHeight] = prepareCanvas(width, height, ratio);
    if (content instanceof HTMLImageElement) {
      // Image
      ctx.drawImage(content, 0, 0, contentWidth, contentHeight);
    } else {
      // Text
      const {
        color,
        fontSize,
        fontStyle,
        fontWeight,
        fontFamily,
        textAlign
      } = font;
      const mergedFontSize = Number(fontSize) * ratio;
      ctx.font = `${fontStyle} normal ${fontWeight} ${mergedFontSize}px/${height}px ${fontFamily}`;
      ctx.fillStyle = color;
      ctx.textAlign = textAlign;
      ctx.textBaseline = 'top';
      const contents = toList(content);
      contents === null || contents === void 0 ? void 0 : contents.forEach((item, index) => {
        ctx.fillText(item !== null && item !== void 0 ? item : '', contentWidth / 2, index * (mergedFontSize + useClips_FontGap * ratio));
      });
    }
    // ==================== Rotate ====================
    const angle = Math.PI / 180 * Number(rotate);
    const maxSize = Math.max(width, height);
    const [rCtx, rCanvas, realMaxSize] = prepareCanvas(maxSize, maxSize, ratio);
    // Copy from `ctx` and rotate
    rCtx.translate(realMaxSize / 2, realMaxSize / 2);
    rCtx.rotate(angle);
    if (contentWidth > 0 && contentHeight > 0) {
      rCtx.drawImage(canvas, -contentWidth / 2, -contentHeight / 2);
    }
    let left = 0;
    let right = 0;
    let top = 0;
    let bottom = 0;
    const halfWidth = contentWidth / 2;
    const halfHeight = contentHeight / 2;
    const points = [[0 - halfWidth, 0 - halfHeight], [0 + halfWidth, 0 - halfHeight], [0 + halfWidth, 0 + halfHeight], [0 - halfWidth, 0 + halfHeight]];
    points.forEach(([x, y]) => {
      const [targetX, targetY] = getRotatePos(x, y, angle);
      left = Math.min(left, targetX);
      right = Math.max(right, targetX);
      top = Math.min(top, targetY);
      bottom = Math.max(bottom, targetY);
    });
    const cutLeft = left + realMaxSize / 2;
    const cutTop = top + realMaxSize / 2;
    const cutWidth = right - left;
    const cutHeight = bottom - top;
    // ================ Fill Alternate ================
    const realGapX = gapX * ratio;
    const realGapY = gapY * ratio;
    const filledWidth = (cutWidth + realGapX) * 2;
    const filledHeight = cutHeight + realGapY;
    const [fCtx, fCanvas] = prepareCanvas(filledWidth, filledHeight);
    const drawImg = (targetX = 0, targetY = 0) => {
      fCtx.drawImage(rCanvas, cutLeft, cutTop, cutWidth, cutHeight, targetX, targetY, cutWidth, cutHeight);
    };
    drawImg();
    drawImg(cutWidth + realGapX, -cutHeight / 2 - realGapY / 2);
    drawImg(cutWidth + realGapX, +cutHeight / 2 + realGapY / 2);
    return [fCanvas.toDataURL(), filledWidth / ratio, filledHeight / ratio];
  };
  return React.useCallback(getClips, []);
};
/* harmony default export */ const watermark_useClips = ((/* unused pure expression or super */ null && (useClips_useClips)));
// EXTERNAL MODULE: ./node_modules/rc-util/es/raf.js
var es_raf = __webpack_require__(25371);
;// ./node_modules/antd/es/watermark/useRafDebounce.js



/**
 * Callback will only execute last one for each raf
 */
function useRafDebounce_useRafDebounce(callback) {
  const executeRef = React.useRef(false);
  const rafRef = React.useRef(null);
  const wrapperCallback = useEvent(callback);
  return () => {
    if (executeRef.current) {
      return;
    }
    executeRef.current = true;
    wrapperCallback();
    rafRef.current = raf(() => {
      executeRef.current = false;
    });
  };
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/isEqual.js
var es_isEqual = __webpack_require__(43210);
;// ./node_modules/antd/es/watermark/useSingletonCache.js


/**
 * Singleton cache will only take latest `cacheParams` as key
 * and return the result for callback matching.
 */
function useSingletonCache_useSingletonCache() {
  const cacheRef = React.useRef([null, null]);
  const getCache = (cacheKeys, callback) => {
    const filteredKeys = cacheKeys.map(item => item instanceof HTMLElement || Number.isNaN(item) ? '' : item);
    if (!isEqual(cacheRef.current[0], filteredKeys)) {
      cacheRef.current = [filteredKeys, callback()];
    }
    return cacheRef.current[1];
  };
  return getCache;
}
;// ./node_modules/antd/es/watermark/utils.js
/** converting camel-cased strings to be lowercase and link it with Separator */
function toLowercaseSeparator(key) {
  return key.replace(/([A-Z])/g, '-$1').toLowerCase();
}
function utils_getStyleStr(style) {
  return Object.keys(style).map(key => `${toLowercaseSeparator(key)}: ${style[key]};`).join(' ');
}
/** Returns the ratio of the device's physical pixel resolution to the css pixel resolution */
function utils_getPixelRatio() {
  return window.devicePixelRatio || 1;
}
/** Whether to re-render the watermark */
const utils_reRendering = (mutation, isWatermarkEle) => {
  let flag = false;
  // Whether to delete the watermark node
  if (mutation.removedNodes.length) {
    flag = Array.from(mutation.removedNodes).some(node => isWatermarkEle(node));
  }
  // Whether the watermark dom property value has been modified
  if (mutation.type === 'attributes' && isWatermarkEle(mutation.target)) {
    flag = true;
  }
  return flag;
};
;// ./node_modules/antd/es/watermark/useWatermark.js


/**
 * Base size of the canvas, 1 for parallel layout and 2 for alternate layout
 * Only alternate layout is currently supported
 */
const BaseSize = 2;
const useWatermark_FontGap = 3;
// Prevent external hidden elements from adding accent styles
const emphasizedStyle = {
  visibility: 'visible !important'
};
function useWatermark_useWatermark(markStyle) {
  const watermarkMap = React.useRef(new Map());
  const appendWatermark = (base64Url, markWidth, container) => {
    if (container) {
      if (!watermarkMap.current.get(container)) {
        const newWatermarkEle = document.createElement('div');
        watermarkMap.current.set(container, newWatermarkEle);
      }
      const watermarkEle = watermarkMap.current.get(container);
      watermarkEle.setAttribute('style', getStyleStr(Object.assign(Object.assign(Object.assign({}, markStyle), {
        backgroundImage: `url('${base64Url}')`,
        backgroundSize: `${Math.floor(markWidth)}px`
      }), emphasizedStyle)));
      // Prevents using the browser `Hide Element` to hide watermarks
      watermarkEle.removeAttribute('class');
      watermarkEle.removeAttribute('hidden');
      if (watermarkEle.parentElement !== container) {
        container.append(watermarkEle);
      }
    }
    return watermarkMap.current.get(container);
  };
  const removeWatermark = container => {
    const watermarkEle = watermarkMap.current.get(container);
    if (watermarkEle && container) {
      container.removeChild(watermarkEle);
    }
    watermarkMap.current.delete(container);
  };
  const isWatermarkEle = ele => Array.from(watermarkMap.current.values()).includes(ele);
  return [appendWatermark, removeWatermark, isWatermarkEle];
}
;// ./node_modules/antd/es/watermark/index.js
"use client";














/**
 * Only return `next` when size changed.
 * This is only used for elements compare, not a shallow equal!
 */
function getSizeDiff(prev, next) {
  return prev.size === next.size ? prev : next;
}
const DEFAULT_GAP_X = 100;
const DEFAULT_GAP_Y = 100;
const fixedStyle = {
  position: 'relative',
  overflow: 'hidden'
};
const Watermark = props => {
  var _a, _b;
  const {
    /**
     * The antd content layer zIndex is basically below 10
     * https://github.com/ant-design/ant-design/blob/6192403b2ce517c017f9e58a32d58774921c10cd/components/style/themes/default.less#L335
     */
    zIndex = 9,
    rotate = -22,
    width,
    height,
    image,
    content,
    font = {},
    style,
    className,
    rootClassName,
    gap = [DEFAULT_GAP_X, DEFAULT_GAP_Y],
    offset,
    children,
    inherit = true
  } = props;
  const mergedStyle = Object.assign(Object.assign({}, fixedStyle), style);
  const [, token] = useToken();
  const {
    color = token.colorFill,
    fontSize = token.fontSizeLG,
    fontWeight = 'normal',
    fontStyle = 'normal',
    fontFamily = 'sans-serif',
    textAlign = 'center'
  } = font;
  const [gapX = DEFAULT_GAP_X, gapY = DEFAULT_GAP_Y] = gap;
  const gapXCenter = gapX / 2;
  const gapYCenter = gapY / 2;
  const offsetLeft = (_a = offset === null || offset === void 0 ? void 0 : offset[0]) !== null && _a !== void 0 ? _a : gapXCenter;
  const offsetTop = (_b = offset === null || offset === void 0 ? void 0 : offset[1]) !== null && _b !== void 0 ? _b : gapYCenter;
  const markStyle = React.useMemo(() => {
    const mergedMarkStyle = {
      zIndex,
      position: 'absolute',
      left: 0,
      top: 0,
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      backgroundRepeat: 'repeat'
    };
    /** Calculate the style of the offset */
    let positionLeft = offsetLeft - gapXCenter;
    let positionTop = offsetTop - gapYCenter;
    if (positionLeft > 0) {
      mergedMarkStyle.left = `${positionLeft}px`;
      mergedMarkStyle.width = `calc(100% - ${positionLeft}px)`;
      positionLeft = 0;
    }
    if (positionTop > 0) {
      mergedMarkStyle.top = `${positionTop}px`;
      mergedMarkStyle.height = `calc(100% - ${positionTop}px)`;
      positionTop = 0;
    }
    mergedMarkStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`;
    return mergedMarkStyle;
  }, [zIndex, offsetLeft, gapXCenter, offsetTop, gapYCenter]);
  const [container, setContainer] = React.useState();
  // Used for nest case like Modal, Drawer
  const [subElements, setSubElements] = React.useState(() => new Set());
  // Nest elements should also support watermark
  const targetElements = React.useMemo(() => {
    const list = container ? [container] : [];
    return [].concat(list, _toConsumableArray(Array.from(subElements)));
  }, [container, subElements]);
  // ============================ Content =============================
  /**
   * Get the width and height of the watermark. The default values are as follows
   * Image: [120, 64]; Content: It's calculated by content;
   */
  const getMarkSize = ctx => {
    let defaultWidth = 120;
    let defaultHeight = 64;
    if (!image && ctx.measureText) {
      ctx.font = `${Number(fontSize)}px ${fontFamily}`;
      const contents = toList(content);
      const sizes = contents.map(item => {
        const metrics = ctx.measureText(item);
        return [metrics.width, metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent];
      });
      defaultWidth = Math.ceil(Math.max.apply(Math, _toConsumableArray(sizes.map(size => size[0]))));
      defaultHeight = Math.ceil(Math.max.apply(Math, _toConsumableArray(sizes.map(size => size[1])))) * contents.length + (contents.length - 1) * FontGap;
    }
    return [width !== null && width !== void 0 ? width : defaultWidth, height !== null && height !== void 0 ? height : defaultHeight];
  };
  const getClips = useClips();
  const getClipsCache = useSingletonCache();
  const [watermarkInfo, setWatermarkInfo] = React.useState(null);
  // Generate new Watermark content
  const renderWatermark = () => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      const ratio = getPixelRatio();
      const [markWidth, markHeight] = getMarkSize(ctx);
      const drawCanvas = drawContent => {
        const params = [drawContent || '', rotate, ratio, markWidth, markHeight, {
          color,
          fontSize,
          fontStyle,
          fontWeight,
          fontFamily,
          textAlign
        }, gapX, gapY];
        const [nextClips, clipWidth] = getClipsCache(params, () => getClips.apply(void 0, params));
        setWatermarkInfo([nextClips, clipWidth]);
      };
      if (image) {
        const img = new Image();
        img.onload = () => {
          drawCanvas(img);
        };
        img.onerror = () => {
          drawCanvas(content);
        };
        img.crossOrigin = 'anonymous';
        img.referrerPolicy = 'no-referrer';
        img.src = image;
      } else {
        drawCanvas(content);
      }
    }
  };
  const syncWatermark = useRafDebounce(renderWatermark);
  // ============================= Effect =============================
  // Append watermark to the container
  const [appendWatermark, removeWatermark, isWatermarkEle] = useWatermark(markStyle);
  useEffect(() => {
    if (watermarkInfo) {
      targetElements.forEach(holder => {
        appendWatermark(watermarkInfo[0], watermarkInfo[1], holder);
      });
    }
  }, [watermarkInfo, targetElements]);
  // ============================ Observe =============================
  const onMutate = useEvent(mutations => {
    mutations.forEach(mutation => {
      if (reRendering(mutation, isWatermarkEle)) {
        syncWatermark();
      } else if (mutation.target === container && mutation.attributeName === 'style') {
        // We've only force container not modify.
        // Not consider nest case.
        const keyStyles = Object.keys(fixedStyle);
        for (let i = 0; i < keyStyles.length; i += 1) {
          const key = keyStyles[i];
          const oriValue = mergedStyle[key];
          const currentValue = container.style[key];
          if (oriValue && oriValue !== currentValue) {
            container.style[key] = oriValue;
          }
        }
      }
    });
  });
  useMutateObserver(targetElements, onMutate);
  useEffect(syncWatermark, [rotate, zIndex, width, height, image, content, color, fontSize, fontWeight, fontStyle, fontFamily, textAlign, gapX, gapY, offsetLeft, offsetTop]);
  // ============================ Context =============================
  const watermarkContext = React.useMemo(() => ({
    add: ele => {
      setSubElements(prev => {
        const clone = new Set(prev);
        clone.add(ele);
        return getSizeDiff(prev, clone);
      });
    },
    remove: ele => {
      removeWatermark(ele);
      setSubElements(prev => {
        const clone = new Set(prev);
        clone.delete(ele);
        return getSizeDiff(prev, clone);
      });
    }
  }), []);
  // ============================= Render =============================
  const childNode = inherit ? (/*#__PURE__*/React.createElement(WatermarkContext.Provider, {
    value: watermarkContext
  }, children)) : children;
  return /*#__PURE__*/React.createElement("div", {
    ref: setContainer,
    className: classNames(className, rootClassName),
    style: mergedStyle
  }, childNode);
};
if (false) {}
/* harmony default export */ const watermark = ((/* unused pure expression or super */ null && (Watermark)));

/***/ }),

/***/ 25640:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ es_version)
});

;// ./node_modules/antd/es/version/version.js
/* harmony default export */ const version = ('5.25.2');
;// ./node_modules/antd/es/version/index.js
"use client";

/* eslint import/no-unresolved: 0 */
// @ts-ignore

/* harmony default export */ const es_version = (version);

/***/ }),

/***/ 28557:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   f: () => (/* binding */ usePanelRef)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96540);
/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(26956);


function voidFunc() {}
const WatermarkContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({
  add: voidFunc,
  remove: voidFunc
});
function usePanelRef(panelSelector) {
  const watermark = react__WEBPACK_IMPORTED_MODULE_0__.useContext(WatermarkContext);
  const panelEleRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);
  const panelRef = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(ele => {
    if (ele) {
      const innerContentEle = panelSelector ? ele.querySelector(panelSelector) : ele;
      watermark.add(innerContentEle);
      panelEleRef.current = innerContentEle;
    } else {
      watermark.remove(panelEleRef.current);
    }
  });
  return panelRef;
}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (WatermarkContext)));

/***/ }),

/***/ 77829:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ upload)
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/react-dom/index.js
var react_dom = __webpack_require__(40961);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-upload/es/index.js + 6 modules
var es = __webpack_require__(40778);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(12533);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/warning.js
var warning = __webpack_require__(18877);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(38674);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/DisabledContext.js
var DisabledContext = __webpack_require__(98119);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/index.js
var locale = __webpack_require__(21282);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/en_US.js
var en_US = __webpack_require__(8182);
// EXTERNAL MODULE: ./node_modules/antd/es/style/index.js
var style = __webpack_require__(25905);
// EXTERNAL MODULE: ./node_modules/antd/es/style/motion/index.js + 4 modules
var motion = __webpack_require__(38328);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/internal.js + 3 modules
var internal = __webpack_require__(51113);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 32 modules
var cssinjs_es = __webpack_require__(36891);
;// ./node_modules/antd/es/upload/style/dragger.js

const genDraggerStyle = token => {
  const {
    componentCls,
    iconCls
  } = token;
  return {
    [`${componentCls}-wrapper`]: {
      [`${componentCls}-drag`]: {
        position: 'relative',
        width: '100%',
        height: '100%',
        textAlign: 'center',
        background: token.colorFillAlter,
        border: `${(0,cssinjs_es/* unit */.zA)(token.lineWidth)} dashed ${token.colorBorder}`,
        borderRadius: token.borderRadiusLG,
        cursor: 'pointer',
        transition: `border-color ${token.motionDurationSlow}`,
        [componentCls]: {
          padding: token.padding
        },
        [`${componentCls}-btn`]: {
          display: 'table',
          width: '100%',
          height: '100%',
          outline: 'none',
          borderRadius: token.borderRadiusLG,
          '&:focus-visible': {
            outline: `${(0,cssinjs_es/* unit */.zA)(token.lineWidthFocus)} solid ${token.colorPrimaryBorder}`
          }
        },
        [`${componentCls}-drag-container`]: {
          display: 'table-cell',
          verticalAlign: 'middle'
        },
        [`
          &:not(${componentCls}-disabled):hover,
          &-hover:not(${componentCls}-disabled)
        `]: {
          borderColor: token.colorPrimaryHover
        },
        [`p${componentCls}-drag-icon`]: {
          marginBottom: token.margin,
          [iconCls]: {
            color: token.colorPrimary,
            fontSize: token.uploadThumbnailSize
          }
        },
        [`p${componentCls}-text`]: {
          margin: `0 0 ${(0,cssinjs_es/* unit */.zA)(token.marginXXS)}`,
          color: token.colorTextHeading,
          fontSize: token.fontSizeLG
        },
        [`p${componentCls}-hint`]: {
          color: token.colorTextDescription,
          fontSize: token.fontSize
        },
        // ===================== Disabled =====================
        [`&${componentCls}-disabled`]: {
          [`p${componentCls}-drag-icon ${iconCls},
            p${componentCls}-text,
            p${componentCls}-hint
          `]: {
            color: token.colorTextDisabled
          }
        }
      }
    }
  };
};
/* harmony default export */ const dragger = (genDraggerStyle);
;// ./node_modules/antd/es/upload/style/list.js


const genListStyle = token => {
  const {
    componentCls,
    iconCls,
    fontSize,
    lineHeight,
    calc
  } = token;
  const itemCls = `${componentCls}-list-item`;
  const actionsCls = `${itemCls}-actions`;
  const actionCls = `${itemCls}-action`;
  return {
    [`${componentCls}-wrapper`]: {
      [`${componentCls}-list`]: Object.assign(Object.assign({}, (0,style/* clearFix */.t6)()), {
        lineHeight: token.lineHeight,
        [itemCls]: {
          position: 'relative',
          height: calc(token.lineHeight).mul(fontSize).equal(),
          marginTop: token.marginXS,
          fontSize,
          display: 'flex',
          alignItems: 'center',
          transition: `background-color ${token.motionDurationSlow}`,
          borderRadius: token.borderRadiusSM,
          '&:hover': {
            backgroundColor: token.controlItemBgHover
          },
          [`${itemCls}-name`]: Object.assign(Object.assign({}, style/* textEllipsis */.L9), {
            padding: `0 ${(0,cssinjs_es/* unit */.zA)(token.paddingXS)}`,
            lineHeight,
            flex: 'auto',
            transition: `all ${token.motionDurationSlow}`
          }),
          [actionsCls]: {
            whiteSpace: 'nowrap',
            [actionCls]: {
              opacity: 0
            },
            [iconCls]: {
              color: token.actionsColor,
              transition: `all ${token.motionDurationSlow}`
            },
            [`
              ${actionCls}:focus-visible,
              &.picture ${actionCls}
            `]: {
              opacity: 1
            }
          },
          [`${componentCls}-icon ${iconCls}`]: {
            color: token.colorIcon,
            fontSize
          },
          [`${itemCls}-progress`]: {
            position: 'absolute',
            bottom: token.calc(token.uploadProgressOffset).mul(-1).equal(),
            width: '100%',
            paddingInlineStart: calc(fontSize).add(token.paddingXS).equal(),
            fontSize,
            lineHeight: 0,
            pointerEvents: 'none',
            '> div': {
              margin: 0
            }
          }
        },
        [`${itemCls}:hover ${actionCls}`]: {
          opacity: 1
        },
        [`${itemCls}-error`]: {
          color: token.colorError,
          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {
            color: token.colorError
          },
          [actionsCls]: {
            [`${iconCls}, ${iconCls}:hover`]: {
              color: token.colorError
            },
            [actionCls]: {
              opacity: 1
            }
          }
        },
        [`${componentCls}-list-item-container`]: {
          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,
          // For smooth removing animation
          '&::before': {
            display: 'table',
            width: 0,
            height: 0,
            content: '""'
          }
        }
      })
    }
  };
};
/* harmony default export */ const list = (genListStyle);
;// ./node_modules/antd/es/upload/style/motion.js


// =========================== Motion ===========================
const genMotionStyle = token => {
  const {
    componentCls
  } = token;
  const uploadAnimateInlineIn = new cssinjs_es/* Keyframes */.Mo('uploadAnimateInlineIn', {
    from: {
      width: 0,
      height: 0,
      padding: 0,
      opacity: 0,
      margin: token.calc(token.marginXS).div(-2).equal()
    }
  });
  const uploadAnimateInlineOut = new cssinjs_es/* Keyframes */.Mo('uploadAnimateInlineOut', {
    to: {
      width: 0,
      height: 0,
      padding: 0,
      opacity: 0,
      margin: token.calc(token.marginXS).div(-2).equal()
    }
  });
  const inlineCls = `${componentCls}-animate-inline`;
  return [{
    [`${componentCls}-wrapper`]: {
      [`${inlineCls}-appear, ${inlineCls}-enter, ${inlineCls}-leave`]: {
        animationDuration: token.motionDurationSlow,
        animationTimingFunction: token.motionEaseInOutCirc,
        animationFillMode: 'forwards'
      },
      [`${inlineCls}-appear, ${inlineCls}-enter`]: {
        animationName: uploadAnimateInlineIn
      },
      [`${inlineCls}-leave`]: {
        animationName: uploadAnimateInlineOut
      }
    }
  }, {
    [`${componentCls}-wrapper`]: (0,motion/* initFadeMotion */.p9)(token)
  }, uploadAnimateInlineIn, uploadAnimateInlineOut];
};
/* harmony default export */ const style_motion = (genMotionStyle);
// EXTERNAL MODULE: ./node_modules/@ant-design/colors/es/index.js + 2 modules
var colors_es = __webpack_require__(45748);
;// ./node_modules/antd/es/upload/style/picture.js



const genPictureStyle = token => {
  const {
    componentCls,
    iconCls,
    uploadThumbnailSize,
    uploadProgressOffset,
    calc
  } = token;
  const listCls = `${componentCls}-list`;
  const itemCls = `${listCls}-item`;
  return {
    [`${componentCls}-wrapper`]: {
      // ${listCls} 增加优先级
      [`
        ${listCls}${listCls}-picture,
        ${listCls}${listCls}-picture-card,
        ${listCls}${listCls}-picture-circle
      `]: {
        [itemCls]: {
          position: 'relative',
          height: calc(uploadThumbnailSize).add(calc(token.lineWidth).mul(2)).add(calc(token.paddingXS).mul(2)).equal(),
          padding: token.paddingXS,
          border: `${(0,cssinjs_es/* unit */.zA)(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,
          borderRadius: token.borderRadiusLG,
          '&:hover': {
            background: 'transparent'
          },
          [`${itemCls}-thumbnail`]: Object.assign(Object.assign({}, style/* textEllipsis */.L9), {
            width: uploadThumbnailSize,
            height: uploadThumbnailSize,
            lineHeight: (0,cssinjs_es/* unit */.zA)(calc(uploadThumbnailSize).add(token.paddingSM).equal()),
            textAlign: 'center',
            flex: 'none',
            [iconCls]: {
              fontSize: token.fontSizeHeading2,
              color: token.colorPrimary
            },
            img: {
              display: 'block',
              width: '100%',
              height: '100%',
              overflow: 'hidden'
            }
          }),
          [`${itemCls}-progress`]: {
            bottom: uploadProgressOffset,
            width: `calc(100% - ${(0,cssinjs_es/* unit */.zA)(calc(token.paddingSM).mul(2).equal())})`,
            marginTop: 0,
            paddingInlineStart: calc(uploadThumbnailSize).add(token.paddingXS).equal()
          }
        },
        [`${itemCls}-error`]: {
          borderColor: token.colorError,
          // Adjust the color of the error icon : https://github.com/ant-design/ant-design/pull/24160
          [`${itemCls}-thumbnail ${iconCls}`]: {
            [`svg path[fill='${colors_es/* blue */.z1[0]}']`]: {
              fill: token.colorErrorBg
            },
            [`svg path[fill='${colors_es/* blue */.z1.primary}']`]: {
              fill: token.colorError
            }
          }
        },
        [`${itemCls}-uploading`]: {
          borderStyle: 'dashed',
          [`${itemCls}-name`]: {
            marginBottom: uploadProgressOffset
          }
        }
      },
      [`${listCls}${listCls}-picture-circle ${itemCls}`]: {
        [`&, &::before, ${itemCls}-thumbnail`]: {
          borderRadius: '50%'
        }
      }
    }
  };
};
const genPictureCardStyle = token => {
  const {
    componentCls,
    iconCls,
    fontSizeLG,
    colorTextLightSolid,
    calc
  } = token;
  const listCls = `${componentCls}-list`;
  const itemCls = `${listCls}-item`;
  const uploadPictureCardSize = token.uploadPicCardSize;
  return {
    [`
      ${componentCls}-wrapper${componentCls}-picture-card-wrapper,
      ${componentCls}-wrapper${componentCls}-picture-circle-wrapper
    `]: Object.assign(Object.assign({}, (0,style/* clearFix */.t6)()), {
      display: 'block',
      [`${componentCls}${componentCls}-select`]: {
        width: uploadPictureCardSize,
        height: uploadPictureCardSize,
        textAlign: 'center',
        verticalAlign: 'top',
        backgroundColor: token.colorFillAlter,
        border: `${(0,cssinjs_es/* unit */.zA)(token.lineWidth)} dashed ${token.colorBorder}`,
        borderRadius: token.borderRadiusLG,
        cursor: 'pointer',
        transition: `border-color ${token.motionDurationSlow}`,
        [`> ${componentCls}`]: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          textAlign: 'center'
        },
        [`&:not(${componentCls}-disabled):hover`]: {
          borderColor: token.colorPrimary
        }
      },
      // list
      [`${listCls}${listCls}-picture-card, ${listCls}${listCls}-picture-circle`]: {
        display: 'flex',
        flexWrap: 'wrap',
        '@supports not (gap: 1px)': {
          '& > *': {
            marginBlockEnd: token.marginXS,
            marginInlineEnd: token.marginXS
          }
        },
        '@supports (gap: 1px)': {
          gap: token.marginXS
        },
        [`${listCls}-item-container`]: {
          display: 'inline-block',
          width: uploadPictureCardSize,
          height: uploadPictureCardSize,
          verticalAlign: 'top'
        },
        '&::after': {
          display: 'none'
        },
        '&::before': {
          display: 'none'
        },
        [itemCls]: {
          height: '100%',
          margin: 0,
          '&::before': {
            position: 'absolute',
            zIndex: 1,
            width: `calc(100% - ${(0,cssinjs_es/* unit */.zA)(calc(token.paddingXS).mul(2).equal())})`,
            height: `calc(100% - ${(0,cssinjs_es/* unit */.zA)(calc(token.paddingXS).mul(2).equal())})`,
            backgroundColor: token.colorBgMask,
            opacity: 0,
            transition: `all ${token.motionDurationSlow}`,
            content: '" "'
          }
        },
        [`${itemCls}:hover`]: {
          [`&::before, ${itemCls}-actions`]: {
            opacity: 1
          }
        },
        [`${itemCls}-actions`]: {
          position: 'absolute',
          insetInlineStart: 0,
          zIndex: 10,
          width: '100%',
          whiteSpace: 'nowrap',
          textAlign: 'center',
          opacity: 0,
          transition: `all ${token.motionDurationSlow}`,
          [`
            ${iconCls}-eye,
            ${iconCls}-download,
            ${iconCls}-delete
          `]: {
            zIndex: 10,
            width: fontSizeLG,
            margin: `0 ${(0,cssinjs_es/* unit */.zA)(token.marginXXS)}`,
            fontSize: fontSizeLG,
            cursor: 'pointer',
            transition: `all ${token.motionDurationSlow}`,
            color: colorTextLightSolid,
            '&:hover': {
              color: colorTextLightSolid
            },
            svg: {
              verticalAlign: 'baseline'
            }
          }
        },
        [`${itemCls}-thumbnail, ${itemCls}-thumbnail img`]: {
          position: 'static',
          display: 'block',
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        },
        [`${itemCls}-name`]: {
          display: 'none',
          textAlign: 'center'
        },
        [`${itemCls}-file + ${itemCls}-name`]: {
          position: 'absolute',
          bottom: token.margin,
          display: 'block',
          width: `calc(100% - ${(0,cssinjs_es/* unit */.zA)(calc(token.paddingXS).mul(2).equal())})`
        },
        [`${itemCls}-uploading`]: {
          [`&${itemCls}`]: {
            backgroundColor: token.colorFillAlter
          },
          [`&::before, ${iconCls}-eye, ${iconCls}-download, ${iconCls}-delete`]: {
            display: 'none'
          }
        },
        [`${itemCls}-progress`]: {
          bottom: token.marginXL,
          width: `calc(100% - ${(0,cssinjs_es/* unit */.zA)(calc(token.paddingXS).mul(2).equal())})`,
          paddingInlineStart: 0
        }
      }
    }),
    [`${componentCls}-wrapper${componentCls}-picture-circle-wrapper`]: {
      [`${componentCls}${componentCls}-select`]: {
        borderRadius: '50%'
      }
    }
  };
};

;// ./node_modules/antd/es/upload/style/rtl.js
// =========================== Motion ===========================
const genRtlStyle = token => {
  const {
    componentCls
  } = token;
  return {
    [`${componentCls}-rtl`]: {
      direction: 'rtl'
    }
  };
};
/* harmony default export */ const rtl = (genRtlStyle);
;// ./node_modules/antd/es/upload/style/index.js








const genBaseStyle = token => {
  const {
    componentCls,
    colorTextDisabled
  } = token;
  return {
    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, (0,style/* resetComponent */.dF)(token)), {
      [componentCls]: {
        outline: 0,
        "input[type='file']": {
          cursor: 'pointer'
        }
      },
      [`${componentCls}-select`]: {
        display: 'inline-block'
      },
      [`${componentCls}-hidden`]: {
        display: 'none'
      },
      [`${componentCls}-disabled`]: {
        color: colorTextDisabled,
        cursor: 'not-allowed'
      }
    })
  };
};
const prepareComponentToken = token => ({
  actionsColor: token.colorIcon
});
// ============================== Export ==============================
/* harmony default export */ const upload_style = ((0,internal/* genStyleHooks */.OF)('Upload', token => {
  const {
    fontSizeHeading3,
    fontHeight,
    lineWidth,
    controlHeightLG,
    calc
  } = token;
  const uploadToken = (0,internal/* mergeToken */.oX)(token, {
    uploadThumbnailSize: calc(fontSizeHeading3).mul(2).equal(),
    uploadProgressOffset: calc(calc(fontHeight).div(2)).add(lineWidth).equal(),
    uploadPicCardSize: calc(controlHeightLG).mul(2.55).equal()
  });
  return [genBaseStyle(uploadToken), dragger(uploadToken), genPictureStyle(uploadToken), genPictureCardStyle(uploadToken), list(uploadToken), style_motion(uploadToken), rtl(uploadToken), (0,motion/* genCollapseMotion */.eG)(uploadToken)];
}, prepareComponentToken));
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/FileTwoTone.js
var FileTwoTone = __webpack_require__(29072);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js
var LoadingOutlined = __webpack_require__(36962);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PaperClipOutlined.js
var PaperClipOutlined = __webpack_require__(59522);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PictureTwoTone.js
var PictureTwoTone = __webpack_require__(41230);
// EXTERNAL MODULE: ./node_modules/rc-motion/es/index.js + 12 modules
var rc_motion_es = __webpack_require__(57557);
// EXTERNAL MODULE: ./node_modules/rc-util/es/omit.js
var omit = __webpack_require__(19853);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/hooks/useForceUpdate.js
var useForceUpdate = __webpack_require__(47447);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/motion.js
var _util_motion = __webpack_require__(23723);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/reactNode.js
var reactNode = __webpack_require__(40682);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 8 modules
var es_button = __webpack_require__(49103);
;// ./node_modules/antd/es/upload/utils.js

function file2Obj(file) {
  return Object.assign(Object.assign({}, file), {
    lastModified: file.lastModified,
    lastModifiedDate: file.lastModifiedDate,
    name: file.name,
    size: file.size,
    type: file.type,
    uid: file.uid,
    percent: 0,
    originFileObj: file
  });
}
/** Upload fileList. Replace file if exist or just push into it. */
function updateFileList(file, fileList) {
  const nextFileList = (0,toConsumableArray/* default */.A)(fileList);
  const fileIndex = nextFileList.findIndex(({
    uid
  }) => uid === file.uid);
  if (fileIndex === -1) {
    nextFileList.push(file);
  } else {
    nextFileList[fileIndex] = file;
  }
  return nextFileList;
}
function getFileItem(file, fileList) {
  const matchKey = file.uid !== undefined ? 'uid' : 'name';
  return fileList.filter(item => item[matchKey] === file[matchKey])[0];
}
function removeFileItem(file, fileList) {
  const matchKey = file.uid !== undefined ? 'uid' : 'name';
  const removed = fileList.filter(item => item[matchKey] !== file[matchKey]);
  if (removed.length === fileList.length) {
    return null;
  }
  return removed;
}
// ==================== Default Image Preview ====================
const extname = (url = '') => {
  const temp = url.split('/');
  const filename = temp[temp.length - 1];
  const filenameWithoutSuffix = filename.split(/#|\?/)[0];
  return (/\.[^./\\]*$/.exec(filenameWithoutSuffix) || [''])[0];
};
const isImageFileType = type => type.indexOf('image/') === 0;
const isImageUrl = file => {
  if (file.type && !file.thumbUrl) {
    return isImageFileType(file.type);
  }
  const url = file.thumbUrl || file.url || '';
  const extension = extname(url);
  if (/^data:image\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)) {
    return true;
  }
  if (/^data:/.test(url)) {
    // other file types of base64
    return false;
  }
  if (extension) {
    // other file types which have extension
    return false;
  }
  return true;
};
const MEASURE_SIZE = 200;
function previewImage(file) {
  return new Promise(resolve => {
    if (!file.type || !isImageFileType(file.type)) {
      resolve('');
      return;
    }
    const canvas = document.createElement('canvas');
    canvas.width = MEASURE_SIZE;
    canvas.height = MEASURE_SIZE;
    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;
    document.body.appendChild(canvas);
    const ctx = canvas.getContext('2d');
    const img = new Image();
    img.onload = () => {
      const {
        width,
        height
      } = img;
      let drawWidth = MEASURE_SIZE;
      let drawHeight = MEASURE_SIZE;
      let offsetX = 0;
      let offsetY = 0;
      if (width > height) {
        drawHeight = height * (MEASURE_SIZE / width);
        offsetY = -(drawHeight - drawWidth) / 2;
      } else {
        drawWidth = width * (MEASURE_SIZE / height);
        offsetX = -(drawWidth - drawHeight) / 2;
      }
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
      const dataURL = canvas.toDataURL();
      document.body.removeChild(canvas);
      window.URL.revokeObjectURL(img.src);
      resolve(dataURL);
    };
    img.crossOrigin = 'anonymous';
    if (file.type.startsWith('image/svg+xml')) {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result && typeof reader.result === 'string') {
          img.src = reader.result;
        }
      };
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('image/gif')) {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result) {
          resolve(reader.result);
        }
      };
      reader.readAsDataURL(file);
    } else {
      img.src = window.URL.createObjectURL(file);
    }
  });
}
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(59499);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DownloadOutlined.js
var DownloadOutlined = __webpack_require__(2064);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(11387);
// EXTERNAL MODULE: ./node_modules/antd/es/progress/index.js + 6 modules
var progress = __webpack_require__(6754);
// EXTERNAL MODULE: ./node_modules/antd/es/tooltip/index.js + 3 modules
var tooltip = __webpack_require__(37977);
;// ./node_modules/antd/es/upload/UploadList/ListItem.js
"use client";










const ListItem = /*#__PURE__*/react.forwardRef(({
  prefixCls,
  className,
  style,
  locale,
  listType,
  file,
  items,
  progress: progressProps,
  iconRender,
  actionIconRender,
  itemRender,
  isImgUrl,
  showPreviewIcon,
  showRemoveIcon,
  showDownloadIcon,
  previewIcon: customPreviewIcon,
  removeIcon: customRemoveIcon,
  downloadIcon: customDownloadIcon,
  extra: customExtra,
  onPreview,
  onDownload,
  onClose
}, ref) => {
  var _a, _b;
  // Status: which will ignore `removed` status
  const {
    status
  } = file;
  const [mergedStatus, setMergedStatus] = react.useState(status);
  react.useEffect(() => {
    if (status !== 'removed') {
      setMergedStatus(status);
    }
  }, [status]);
  // Delay to show the progress bar
  const [showProgress, setShowProgress] = react.useState(false);
  react.useEffect(() => {
    const timer = setTimeout(() => {
      setShowProgress(true);
    }, 300);
    return () => {
      clearTimeout(timer);
    };
  }, []);
  const iconNode = iconRender(file);
  let icon = /*#__PURE__*/react.createElement("div", {
    className: `${prefixCls}-icon`
  }, iconNode);
  if (listType === 'picture' || listType === 'picture-card' || listType === 'picture-circle') {
    if (mergedStatus === 'uploading' || !file.thumbUrl && !file.url) {
      const uploadingClassName = classnames_default()(`${prefixCls}-list-item-thumbnail`, {
        [`${prefixCls}-list-item-file`]: mergedStatus !== 'uploading'
      });
      icon = /*#__PURE__*/react.createElement("div", {
        className: uploadingClassName
      }, iconNode);
    } else {
      const thumbnail = (isImgUrl === null || isImgUrl === void 0 ? void 0 : isImgUrl(file)) ? (/*#__PURE__*/react.createElement("img", {
        src: file.thumbUrl || file.url,
        alt: file.name,
        className: `${prefixCls}-list-item-image`,
        crossOrigin: file.crossOrigin
      })) : iconNode;
      const aClassName = classnames_default()(`${prefixCls}-list-item-thumbnail`, {
        [`${prefixCls}-list-item-file`]: isImgUrl && !isImgUrl(file)
      });
      icon = /*#__PURE__*/react.createElement("a", {
        className: aClassName,
        onClick: e => onPreview(file, e),
        href: file.url || file.thumbUrl,
        target: "_blank",
        rel: "noopener noreferrer"
      }, thumbnail);
    }
  }
  const listItemClassName = classnames_default()(`${prefixCls}-list-item`, `${prefixCls}-list-item-${mergedStatus}`);
  const linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;
  const removeIcon = (typeof showRemoveIcon === 'function' ? showRemoveIcon(file) : showRemoveIcon) ? actionIconRender((typeof customRemoveIcon === 'function' ? customRemoveIcon(file) : customRemoveIcon) || (/*#__PURE__*/react.createElement(DeleteOutlined/* default */.A, null)), () => onClose(file), prefixCls, locale.removeFile,
  // acceptUploadDisabled is true, only remove icon will follow Upload disabled prop
  // https://github.com/ant-design/ant-design/issues/46171
  true) : null;
  const downloadIcon = (typeof showDownloadIcon === 'function' ? showDownloadIcon(file) : showDownloadIcon) && mergedStatus === 'done' ? actionIconRender((typeof customDownloadIcon === 'function' ? customDownloadIcon(file) : customDownloadIcon) || /*#__PURE__*/react.createElement(DownloadOutlined/* default */.A, null), () => onDownload(file), prefixCls, locale.downloadFile) : null;
  const downloadOrDelete = listType !== 'picture-card' && listType !== 'picture-circle' && (/*#__PURE__*/react.createElement("span", {
    key: "download-delete",
    className: classnames_default()(`${prefixCls}-list-item-actions`, {
      picture: listType === 'picture'
    })
  }, downloadIcon, removeIcon));
  const extraContent = typeof customExtra === 'function' ? customExtra(file) : customExtra;
  const extra = extraContent && (/*#__PURE__*/react.createElement("span", {
    className: `${prefixCls}-list-item-extra`
  }, extraContent));
  const listItemNameClass = classnames_default()(`${prefixCls}-list-item-name`);
  const fileName = file.url ? (/*#__PURE__*/react.createElement("a", Object.assign({
    key: "view",
    target: "_blank",
    rel: "noopener noreferrer",
    className: listItemNameClass,
    title: file.name
  }, linkProps, {
    href: file.url,
    onClick: e => onPreview(file, e)
  }), file.name, extra)) : (/*#__PURE__*/react.createElement("span", {
    key: "view",
    className: listItemNameClass,
    onClick: e => onPreview(file, e),
    title: file.name
  }, file.name, extra));
  const previewIcon = (typeof showPreviewIcon === 'function' ? showPreviewIcon(file) : showPreviewIcon) && (file.url || file.thumbUrl) ? (/*#__PURE__*/react.createElement("a", {
    href: file.url || file.thumbUrl,
    target: "_blank",
    rel: "noopener noreferrer",
    onClick: e => onPreview(file, e),
    title: locale.previewFile
  }, typeof customPreviewIcon === 'function' ? customPreviewIcon(file) : customPreviewIcon || /*#__PURE__*/react.createElement(EyeOutlined/* default */.A, null))) : null;
  const pictureCardActions = (listType === 'picture-card' || listType === 'picture-circle') && mergedStatus !== 'uploading' && (/*#__PURE__*/react.createElement("span", {
    className: `${prefixCls}-list-item-actions`
  }, previewIcon, mergedStatus === 'done' && downloadIcon, removeIcon));
  const {
    getPrefixCls
  } = react.useContext(config_provider/* ConfigContext */.QO);
  const rootPrefixCls = getPrefixCls();
  const dom = /*#__PURE__*/react.createElement("div", {
    className: listItemClassName
  }, icon, fileName, downloadOrDelete, pictureCardActions, showProgress && (/*#__PURE__*/react.createElement(rc_motion_es/* default */.Ay, {
    motionName: `${rootPrefixCls}-fade`,
    visible: mergedStatus === 'uploading',
    motionDeadline: 2000
  }, ({
    className: motionClassName
  }) => {
    // show loading icon if upload progress listener is disabled
    const loadingProgress = 'percent' in file ? (/*#__PURE__*/react.createElement(progress/* default */.A, Object.assign({}, progressProps, {
      type: "line",
      percent: file.percent,
      "aria-label": file['aria-label'],
      "aria-labelledby": file['aria-labelledby']
    }))) : null;
    return /*#__PURE__*/react.createElement("div", {
      className: classnames_default()(`${prefixCls}-list-item-progress`, motionClassName)
    }, loadingProgress);
  })));
  const message = file.response && typeof file.response === 'string' ? file.response : ((_a = file.error) === null || _a === void 0 ? void 0 : _a.statusText) || ((_b = file.error) === null || _b === void 0 ? void 0 : _b.message) || locale.uploadError;
  const item = mergedStatus === 'error' ? (/*#__PURE__*/react.createElement(tooltip/* default */.A, {
    title: message,
    getPopupContainer: node => node.parentNode
  }, dom)) : dom;
  return /*#__PURE__*/react.createElement("div", {
    className: classnames_default()(`${prefixCls}-list-item-container`, className),
    style: style,
    ref: ref
  }, itemRender ? itemRender(item, file, items, {
    download: onDownload.bind(null, file),
    preview: onPreview.bind(null, file),
    remove: onClose.bind(null, file)
  }) : item);
});
/* harmony default export */ const UploadList_ListItem = (ListItem);
;// ./node_modules/antd/es/upload/UploadList/index.js
"use client";

















const InternalUploadList = (props, ref) => {
  const {
    listType = 'text',
    previewFile = previewImage,
    onPreview,
    onDownload,
    onRemove,
    locale,
    iconRender,
    isImageUrl: isImgUrl = isImageUrl,
    prefixCls: customizePrefixCls,
    items = [],
    showPreviewIcon = true,
    showRemoveIcon = true,
    showDownloadIcon = false,
    removeIcon,
    previewIcon,
    downloadIcon,
    extra,
    progress = {
      size: [-1, 2],
      showInfo: false
    },
    appendAction,
    appendActionVisible = true,
    itemRender,
    disabled
  } = props;
  const forceUpdate = (0,useForceUpdate/* default */.A)();
  const [motionAppear, setMotionAppear] = react.useState(false);
  const isPictureCardOrCirle = ['picture-card', 'picture-circle'].includes(listType);
  // ============================= Effect =============================
  react.useEffect(() => {
    if (!listType.startsWith('picture')) {
      return;
    }
    (items || []).forEach(file => {
      if (!(file.originFileObj instanceof File || file.originFileObj instanceof Blob) || file.thumbUrl !== undefined) {
        return;
      }
      file.thumbUrl = '';
      previewFile === null || previewFile === void 0 ? void 0 : previewFile(file.originFileObj).then(previewDataUrl => {
        // Need append '' to avoid dead loop
        file.thumbUrl = previewDataUrl || '';
        forceUpdate();
      });
    });
  }, [listType, items, previewFile]);
  react.useEffect(() => {
    setMotionAppear(true);
  }, []);
  // ============================= Events =============================
  const onInternalPreview = (file, e) => {
    if (!onPreview) {
      return;
    }
    e === null || e === void 0 ? void 0 : e.preventDefault();
    return onPreview(file);
  };
  const onInternalDownload = file => {
    if (typeof onDownload === 'function') {
      onDownload(file);
    } else if (file.url) {
      window.open(file.url);
    }
  };
  const onInternalClose = file => {
    onRemove === null || onRemove === void 0 ? void 0 : onRemove(file);
  };
  const internalIconRender = file => {
    if (iconRender) {
      return iconRender(file, listType);
    }
    const isLoading = file.status === 'uploading';
    if (listType.startsWith('picture')) {
      const loadingIcon = listType === 'picture' ? /*#__PURE__*/react.createElement(LoadingOutlined/* default */.A, null) : locale.uploading;
      const fileIcon = (isImgUrl === null || isImgUrl === void 0 ? void 0 : isImgUrl(file)) ? /*#__PURE__*/react.createElement(PictureTwoTone/* default */.A, null) : /*#__PURE__*/react.createElement(FileTwoTone/* default */.A, null);
      return isLoading ? loadingIcon : fileIcon;
    }
    return isLoading ? /*#__PURE__*/react.createElement(LoadingOutlined/* default */.A, null) : /*#__PURE__*/react.createElement(PaperClipOutlined/* default */.A, null);
  };
  const actionIconRender = (customIcon, callback, prefixCls, title, acceptUploadDisabled) => {
    const btnProps = {
      type: 'text',
      size: 'small',
      title,
      onClick: e => {
        var _a, _b;
        callback();
        if (/*#__PURE__*/react.isValidElement(customIcon)) {
          (_b = (_a = customIcon.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, e);
        }
      },
      className: `${prefixCls}-list-item-action`,
      disabled: acceptUploadDisabled ? disabled : false
    };
    return /*#__PURE__*/react.isValidElement(customIcon) ? (/*#__PURE__*/react.createElement(es_button/* default */.Ay, Object.assign({}, btnProps, {
      icon: (0,reactNode/* cloneElement */.Ob)(customIcon, Object.assign(Object.assign({}, customIcon.props), {
        onClick: () => {}
      }))
    }))) : (/*#__PURE__*/react.createElement(es_button/* default */.Ay, Object.assign({}, btnProps), /*#__PURE__*/react.createElement("span", null, customIcon)));
  };
  // ============================== Ref ===============================
  // Test needs
  react.useImperativeHandle(ref, () => ({
    handlePreview: onInternalPreview,
    handleDownload: onInternalDownload
  }));
  const {
    getPrefixCls
  } = react.useContext(config_provider/* ConfigContext */.QO);
  // ============================= Render =============================
  const prefixCls = getPrefixCls('upload', customizePrefixCls);
  const rootPrefixCls = getPrefixCls();
  const listClassNames = classnames_default()(`${prefixCls}-list`, `${prefixCls}-list-${listType}`);
  const listItemMotion = react.useMemo(() => (0,omit/* default */.A)((0,_util_motion/* default */.A)(rootPrefixCls), ['onAppearEnd', 'onEnterEnd', 'onLeaveEnd']), [rootPrefixCls]);
  const motionConfig = Object.assign(Object.assign({}, isPictureCardOrCirle ? {} : listItemMotion), {
    motionDeadline: 2000,
    motionName: `${prefixCls}-${isPictureCardOrCirle ? 'animate-inline' : 'animate'}`,
    keys: (0,toConsumableArray/* default */.A)(items.map(file => ({
      key: file.uid,
      file
    }))),
    motionAppear
  });
  return /*#__PURE__*/react.createElement("div", {
    className: listClassNames
  }, /*#__PURE__*/react.createElement(rc_motion_es/* CSSMotionList */.aF, Object.assign({}, motionConfig, {
    component: false
  }), ({
    key,
    file,
    className: motionClassName,
    style: motionStyle
  }) => (/*#__PURE__*/react.createElement(UploadList_ListItem, {
    key: key,
    locale: locale,
    prefixCls: prefixCls,
    className: motionClassName,
    style: motionStyle,
    file: file,
    items: items,
    progress: progress,
    listType: listType,
    isImgUrl: isImgUrl,
    showPreviewIcon: showPreviewIcon,
    showRemoveIcon: showRemoveIcon,
    showDownloadIcon: showDownloadIcon,
    removeIcon: removeIcon,
    previewIcon: previewIcon,
    downloadIcon: downloadIcon,
    extra: extra,
    iconRender: internalIconRender,
    actionIconRender: actionIconRender,
    itemRender: itemRender,
    onPreview: onInternalPreview,
    onDownload: onInternalDownload,
    onClose: onInternalClose
  }))), appendAction && (/*#__PURE__*/react.createElement(rc_motion_es/* default */.Ay, Object.assign({}, motionConfig, {
    visible: appendActionVisible,
    forceRender: true
  }), ({
    className: motionClassName,
    style: motionStyle
  }) => (0,reactNode/* cloneElement */.Ob)(appendAction, oriProps => ({
    className: classnames_default()(oriProps.className, motionClassName),
    style: Object.assign(Object.assign(Object.assign({}, motionStyle), {
      // prevent the element has hover css pseudo-class that may cause animation to end prematurely.
      pointerEvents: motionClassName ? 'none' : undefined
    }), oriProps.style)
  })))));
};
const UploadList = /*#__PURE__*/react.forwardRef(InternalUploadList);
if (false) {}
/* harmony default export */ const upload_UploadList = (UploadList);
;// ./node_modules/antd/es/upload/Upload.js
"use client";


var __awaiter = undefined && undefined.__awaiter || function (thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function (resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function (resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};













const LIST_IGNORE = `__LIST_IGNORE_${Date.now()}__`;
const InternalUpload = (props, ref) => {
  const {
    fileList,
    defaultFileList,
    onRemove,
    showUploadList = true,
    listType = 'text',
    onPreview,
    onDownload,
    onChange,
    onDrop,
    previewFile,
    disabled: customDisabled,
    locale: propLocale,
    iconRender,
    isImageUrl,
    progress,
    prefixCls: customizePrefixCls,
    className,
    type = 'select',
    children,
    style,
    itemRender,
    maxCount,
    data = {},
    multiple = false,
    hasControlInside = true,
    action = '',
    accept = '',
    supportServerRender = true,
    rootClassName
  } = props;
  // ===================== Disabled =====================
  const disabled = react.useContext(DisabledContext/* default */.A);
  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;
  const [mergedFileList, setMergedFileList] = (0,useMergedState/* default */.A)(defaultFileList || [], {
    value: fileList,
    postState: list => list !== null && list !== void 0 ? list : []
  });
  const [dragState, setDragState] = react.useState('drop');
  const upload = react.useRef(null);
  const wrapRef = react.useRef(null);
  if (false) {}
  // Control mode will auto fill file uid if not provided
  react.useMemo(() => {
    const timestamp = Date.now();
    (fileList || []).forEach((file, index) => {
      if (!file.uid && !Object.isFrozen(file)) {
        file.uid = `__AUTO__${timestamp}_${index}__`;
      }
    });
  }, [fileList]);
  const onInternalChange = (file, changedFileList, event) => {
    let cloneList = (0,toConsumableArray/* default */.A)(changedFileList);
    let exceedMaxCount = false;
    // Cut to match count
    if (maxCount === 1) {
      cloneList = cloneList.slice(-1);
    } else if (maxCount) {
      exceedMaxCount = cloneList.length > maxCount;
      cloneList = cloneList.slice(0, maxCount);
    }
    // Prevent React18 auto batch since input[upload] trigger process at same time
    // which makes fileList closure problem
    // eslint-disable-next-line react-dom/no-flush-sync
    (0,react_dom.flushSync)(() => {
      setMergedFileList(cloneList);
    });
    const changeInfo = {
      file: file,
      fileList: cloneList
    };
    if (event) {
      changeInfo.event = event;
    }
    if (!exceedMaxCount || file.status === 'removed' ||
    // We should ignore event if current file is exceed `maxCount`
    cloneList.some(f => f.uid === file.uid)) {
      // eslint-disable-next-line react-dom/no-flush-sync
      (0,react_dom.flushSync)(() => {
        onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo);
      });
    }
  };
  const mergedBeforeUpload = (file, fileListArgs) => __awaiter(void 0, void 0, void 0, function* () {
    const {
      beforeUpload,
      transformFile
    } = props;
    let parsedFile = file;
    if (beforeUpload) {
      const result = yield beforeUpload(file, fileListArgs);
      if (result === false) {
        return false;
      }
      // Hack for LIST_IGNORE, we add additional info to remove from the list
      delete file[LIST_IGNORE];
      if (result === LIST_IGNORE) {
        Object.defineProperty(file, LIST_IGNORE, {
          value: true,
          configurable: true
        });
        return false;
      }
      if (typeof result === 'object' && result) {
        parsedFile = result;
      }
    }
    if (transformFile) {
      parsedFile = yield transformFile(parsedFile);
    }
    return parsedFile;
  });
  const onBatchStart = batchFileInfoList => {
    // Skip file which marked as `LIST_IGNORE`, these file will not add to file list
    const filteredFileInfoList = batchFileInfoList.filter(info => !info.file[LIST_IGNORE]);
    // Nothing to do since no file need upload
    if (!filteredFileInfoList.length) {
      return;
    }
    const objectFileList = filteredFileInfoList.map(info => file2Obj(info.file));
    // Concat new files with prev files
    let newFileList = (0,toConsumableArray/* default */.A)(mergedFileList);
    objectFileList.forEach(fileObj => {
      // Replace file if exist
      newFileList = updateFileList(fileObj, newFileList);
    });
    objectFileList.forEach((fileObj, index) => {
      // Repeat trigger `onChange` event for compatible
      let triggerFileObj = fileObj;
      if (!filteredFileInfoList[index].parsedFile) {
        // `beforeUpload` return false
        const {
          originFileObj
        } = fileObj;
        let clone;
        try {
          clone = new File([originFileObj], originFileObj.name, {
            type: originFileObj.type
          });
        } catch (_a) {
          clone = new Blob([originFileObj], {
            type: originFileObj.type
          });
          clone.name = originFileObj.name;
          clone.lastModifiedDate = new Date();
          clone.lastModified = new Date().getTime();
        }
        clone.uid = fileObj.uid;
        triggerFileObj = clone;
      } else {
        // Inject `uploading` status
        fileObj.status = 'uploading';
      }
      onInternalChange(triggerFileObj, newFileList);
    });
  };
  const onSuccess = (response, file, xhr) => {
    try {
      if (typeof response === 'string') {
        // biome-ignore lint/style/noParameterAssign: we need to modify response
        response = JSON.parse(response);
      }
    } catch (_a) {
      /* do nothing */
    }
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }
    const targetItem = file2Obj(file);
    targetItem.status = 'done';
    targetItem.percent = 100;
    targetItem.response = response;
    targetItem.xhr = xhr;
    const nextFileList = updateFileList(targetItem, mergedFileList);
    onInternalChange(targetItem, nextFileList);
  };
  const onProgress = (e, file) => {
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }
    const targetItem = file2Obj(file);
    targetItem.status = 'uploading';
    targetItem.percent = e.percent;
    const nextFileList = updateFileList(targetItem, mergedFileList);
    onInternalChange(targetItem, nextFileList, e);
  };
  const onError = (error, response, file) => {
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }
    const targetItem = file2Obj(file);
    targetItem.error = error;
    targetItem.response = response;
    targetItem.status = 'error';
    const nextFileList = updateFileList(targetItem, mergedFileList);
    onInternalChange(targetItem, nextFileList);
  };
  const handleRemove = file => {
    let currentFile;
    Promise.resolve(typeof onRemove === 'function' ? onRemove(file) : onRemove).then(ret => {
      var _a;
      // Prevent removing file
      if (ret === false) {
        return;
      }
      const removedFileList = removeFileItem(file, mergedFileList);
      if (removedFileList) {
        currentFile = Object.assign(Object.assign({}, file), {
          status: 'removed'
        });
        mergedFileList === null || mergedFileList === void 0 ? void 0 : mergedFileList.forEach(item => {
          const matchKey = currentFile.uid !== undefined ? 'uid' : 'name';
          if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {
            item.status = 'removed';
          }
        });
        (_a = upload.current) === null || _a === void 0 ? void 0 : _a.abort(currentFile);
        onInternalChange(currentFile, removedFileList);
      }
    });
  };
  const onFileDrop = e => {
    setDragState(e.type);
    if (e.type === 'drop') {
      onDrop === null || onDrop === void 0 ? void 0 : onDrop(e);
    }
  };
  // Test needs
  react.useImperativeHandle(ref, () => ({
    onBatchStart,
    onSuccess,
    onProgress,
    onError,
    fileList: mergedFileList,
    upload: upload.current,
    nativeElement: wrapRef.current
  }));
  const {
    getPrefixCls,
    direction,
    upload: ctxUpload
  } = react.useContext(config_provider/* ConfigContext */.QO);
  const prefixCls = getPrefixCls('upload', customizePrefixCls);
  const rcUploadProps = Object.assign(Object.assign({
    onBatchStart,
    onError,
    onProgress,
    onSuccess
  }, props), {
    data,
    multiple,
    action,
    accept,
    supportServerRender,
    prefixCls,
    disabled: mergedDisabled,
    beforeUpload: mergedBeforeUpload,
    onChange: undefined,
    hasControlInside
  });
  delete rcUploadProps.className;
  delete rcUploadProps.style;
  // Remove id to avoid open by label when trigger is hidden
  // !children: https://github.com/ant-design/ant-design/issues/14298
  // disabled: https://github.com/ant-design/ant-design/issues/16478
  //           https://github.com/ant-design/ant-design/issues/24197
  if (!children || mergedDisabled) {
    delete rcUploadProps.id;
  }
  const wrapperCls = `${prefixCls}-wrapper`;
  const [wrapCSSVar, hashId, cssVarCls] = upload_style(prefixCls, wrapperCls);
  const [contextLocale] = (0,locale/* useLocale */.Ym)('Upload', en_US/* default */.A.Upload);
  const {
    showRemoveIcon,
    showPreviewIcon,
    showDownloadIcon,
    removeIcon,
    previewIcon,
    downloadIcon,
    extra
  } = typeof showUploadList === 'boolean' ? {} : showUploadList;
  // use showRemoveIcon if it is specified explicitly
  const realShowRemoveIcon = typeof showRemoveIcon === 'undefined' ? !mergedDisabled : showRemoveIcon;
  const renderUploadList = (button, buttonVisible) => {
    if (!showUploadList) {
      return button;
    }
    return /*#__PURE__*/react.createElement(upload_UploadList, {
      prefixCls: prefixCls,
      listType: listType,
      items: mergedFileList,
      previewFile: previewFile,
      onPreview: onPreview,
      onDownload: onDownload,
      onRemove: handleRemove,
      showRemoveIcon: realShowRemoveIcon,
      showPreviewIcon: showPreviewIcon,
      showDownloadIcon: showDownloadIcon,
      removeIcon: removeIcon,
      previewIcon: previewIcon,
      downloadIcon: downloadIcon,
      iconRender: iconRender,
      extra: extra,
      locale: Object.assign(Object.assign({}, contextLocale), propLocale),
      isImageUrl: isImageUrl,
      progress: progress,
      appendAction: button,
      appendActionVisible: buttonVisible,
      itemRender: itemRender,
      disabled: mergedDisabled
    });
  };
  const mergedCls = classnames_default()(wrapperCls, className, rootClassName, hashId, cssVarCls, ctxUpload === null || ctxUpload === void 0 ? void 0 : ctxUpload.className, {
    [`${prefixCls}-rtl`]: direction === 'rtl',
    [`${prefixCls}-picture-card-wrapper`]: listType === 'picture-card',
    [`${prefixCls}-picture-circle-wrapper`]: listType === 'picture-circle'
  });
  const mergedStyle = Object.assign(Object.assign({}, ctxUpload === null || ctxUpload === void 0 ? void 0 : ctxUpload.style), style);
  // ======================== Render ========================
  if (type === 'drag') {
    const dragCls = classnames_default()(hashId, prefixCls, `${prefixCls}-drag`, {
      [`${prefixCls}-drag-uploading`]: mergedFileList.some(file => file.status === 'uploading'),
      [`${prefixCls}-drag-hover`]: dragState === 'dragover',
      [`${prefixCls}-disabled`]: mergedDisabled,
      [`${prefixCls}-rtl`]: direction === 'rtl'
    });
    return wrapCSSVar(/*#__PURE__*/react.createElement("span", {
      className: mergedCls,
      ref: wrapRef
    }, /*#__PURE__*/react.createElement("div", {
      className: dragCls,
      style: mergedStyle,
      onDrop: onFileDrop,
      onDragOver: onFileDrop,
      onDragLeave: onFileDrop
    }, /*#__PURE__*/react.createElement(es/* default */.A, Object.assign({}, rcUploadProps, {
      ref: upload,
      className: `${prefixCls}-btn`
    }), /*#__PURE__*/react.createElement("div", {
      className: `${prefixCls}-drag-container`
    }, children))), renderUploadList()));
  }
  const uploadBtnCls = classnames_default()(prefixCls, `${prefixCls}-select`, {
    [`${prefixCls}-disabled`]: mergedDisabled,
    [`${prefixCls}-hidden`]: !children
  });
  const uploadButton = /*#__PURE__*/react.createElement("div", {
    className: uploadBtnCls
  }, /*#__PURE__*/react.createElement(es/* default */.A, Object.assign({}, rcUploadProps, {
    ref: upload
  })));
  if (listType === 'picture-card' || listType === 'picture-circle') {
    return wrapCSSVar(/*#__PURE__*/react.createElement("span", {
      className: mergedCls,
      ref: wrapRef
    }, renderUploadList(uploadButton, !!children)));
  }
  return wrapCSSVar(/*#__PURE__*/react.createElement("span", {
    className: mergedCls,
    ref: wrapRef
  }, uploadButton, renderUploadList()));
};
const Upload = /*#__PURE__*/react.forwardRef(InternalUpload);
if (false) {}
/* harmony default export */ const upload_Upload = (Upload);
;// ./node_modules/antd/es/upload/Dragger.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};


const Dragger = /*#__PURE__*/react.forwardRef((_a, ref) => {
  var {
      style,
      height,
      hasControlInside = false
    } = _a,
    restProps = __rest(_a, ["style", "height", "hasControlInside"]);
  return /*#__PURE__*/react.createElement(upload_Upload, Object.assign({
    ref: ref,
    hasControlInside: hasControlInside
  }, restProps, {
    type: "drag",
    style: Object.assign(Object.assign({}, style), {
      height
    })
  }));
});
if (false) {}
/* harmony default export */ const upload_Dragger = (Dragger);
;// ./node_modules/antd/es/upload/index.js
"use client";



const es_upload_Upload = upload_Upload;
es_upload_Upload.Dragger = upload_Dragger;
es_upload_Upload.LIST_IGNORE = LIST_IGNORE;
/* harmony default export */ const upload = (es_upload_Upload);

/***/ })

}]);