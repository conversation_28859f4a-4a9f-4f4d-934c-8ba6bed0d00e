"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[672],{

/***/ 40672:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(86020);


var _templateObject, _templateObject2, _templateObject3, _templateObject4;





var Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_3__/* .Tabs */ .tU.TabPane;
var MonitorContainer = _design_system__WEBPACK_IMPORTED_MODULE_5__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].spacing */ .Ay.spacing[3]);
var MetricsGrid = _design_system__WEBPACK_IMPORTED_MODULE_5__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: ", ";\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].spacing */ .Ay.spacing[4], _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].spacing */ .Ay.spacing[4]);
var MetricCard = (0,_design_system__WEBPACK_IMPORTED_MODULE_5__.styled)(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  text-align: center;\n"])));
var ChartContainer = _design_system__WEBPACK_IMPORTED_MODULE_5__.styled.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  height: 300px;\n  margin-bottom: ", ";\n  background-color: ", ";\n  border-radius: ", ";\n  display: flex;\n  justify-content: center;\n  align-items: center;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].spacing */ .Ay.spacing[4], _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].colors */ .Ay.colors.neutral[100], _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].borderRadius */ .Ay.borderRadius.md);

/**
 * PerformanceMonitor component
 * Monitors and displays application performance metrics
 */
var PerformanceMonitor = function PerformanceMonitor() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      memory: {
        used: 0,
        total: 0,
        limit: 0
      },
      cpu: {
        usage: 0,
        cores: 0
      },
      network: {
        requests: 0,
        transferred: 0,
        errors: 0
      },
      rendering: {
        fps: 0,
        renderTime: 0
      },
      components: {
        count: 0,
        renderCount: 0
      }
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    metrics = _useState2[0],
    setMetrics = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('overview'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    activeTab = _useState6[0],
    setActiveTab = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState7, 2),
    error = _useState8[0],
    setError = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState9, 2),
    lastUpdated = _useState0[0],
    setLastUpdated = _useState0[1];
  var animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();
  var fpsCounterRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var lastFrameTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(performance.now());
  var frameTimesRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);

  // Simulate collecting performance metrics
  var collectMetrics = function collectMetrics() {
    try {
      var _window$performance;
      setLoading(true);

      // Simulate memory metrics
      var memory = ((_window$performance = window.performance) === null || _window$performance === void 0 ? void 0 : _window$performance.memory) || {
        usedJSHeapSize: Math.random() * 100 * 1024 * 1024,
        totalJSHeapSize: 200 * 1024 * 1024,
        jsHeapSizeLimit: 500 * 1024 * 1024
      };

      // Simulate CPU metrics
      var cpuUsage = Math.random() * 100;
      var cpuCores = navigator.hardwareConcurrency || 4;

      // Simulate network metrics
      var networkRequests = Math.floor(Math.random() * 50);
      var networkTransferred = Math.random() * 5 * 1024 * 1024;
      var networkErrors = Math.floor(Math.random() * 3);

      // Get component metrics from the DOM
      var componentCount = document.querySelectorAll('[data-component]').length || Math.floor(Math.random() * 20);
      var componentRenderCount = Math.floor(Math.random() * 100);

      // Calculate FPS
      var now = performance.now();
      var elapsed = now - lastFrameTimeRef.current;
      lastFrameTimeRef.current = now;
      frameTimesRef.current.push(elapsed);
      if (frameTimesRef.current.length > 60) {
        frameTimesRef.current.shift();
      }
      var averageFrameTime = frameTimesRef.current.reduce(function (sum, time) {
        return sum + time;
      }, 0) / frameTimesRef.current.length;
      var fps = Math.round(1000 / averageFrameTime);

      // Update metrics
      setMetrics({
        memory: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        },
        cpu: {
          usage: cpuUsage,
          cores: cpuCores
        },
        network: {
          requests: networkRequests,
          transferred: networkTransferred,
          errors: networkErrors
        },
        rendering: {
          fps: fps,
          renderTime: averageFrameTime
        },
        components: {
          count: componentCount,
          renderCount: componentRenderCount
        }
      });
      setLastUpdated(new Date());
      setLoading(false);
    } catch (error) {
      console.error('Error collecting metrics:', error);
      setError('Failed to collect performance metrics');
      setLoading(false);
    }
  };

  // Start collecting metrics on mount
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    collectMetrics();

    // Set up animation frame loop for FPS calculation
    var _updateFPS = function updateFPS() {
      fpsCounterRef.current++;
      animationFrameRef.current = requestAnimationFrame(_updateFPS);
    };
    animationFrameRef.current = requestAnimationFrame(_updateFPS);

    // Set up interval to update metrics
    var intervalId = setInterval(function () {
      collectMetrics();
    }, 2000);
    return function () {
      clearInterval(intervalId);
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, []);

  // Format bytes to human-readable format
  var formatBytes = function formatBytes(bytes) {
    var decimals = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;
    if (bytes === 0) return '0 Bytes';
    var k = 1024;
    var dm = decimals < 0 ? 0 : decimals;
    var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  // Get status color based on value
  var getStatusColor = function getStatusColor(value, thresholds) {
    var inverse = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    if (inverse) {
      // For metrics where lower values are worse (like FPS)
      if (value <= thresholds.danger) return _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].colors */ .Ay.colors.error.main;
      if (value <= thresholds.warning) return _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].colors */ .Ay.colors.warning.main;
      return _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].colors */ .Ay.colors.success.main;
    } else {
      // For metrics where higher values are worse (like CPU, memory)
      if (value >= thresholds.danger) return _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].colors */ .Ay.colors.error.main;
      if (value >= thresholds.warning) return _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].colors */ .Ay.colors.warning.main;
      return _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].colors */ .Ay.colors.success.main;
    }
  };

  // Network requests data
  var networkData = [{
    key: '1',
    url: '/api/components',
    method: 'GET',
    status: 200,
    time: '120ms',
    size: '5.2KB'
  }, {
    key: '2',
    url: '/api/layouts',
    method: 'GET',
    status: 200,
    time: '85ms',
    size: '3.8KB'
  }, {
    key: '3',
    url: '/api/themes',
    method: 'GET',
    status: 200,
    time: '95ms',
    size: '2.1KB'
  }, {
    key: '4',
    url: '/api/user',
    method: 'GET',
    status: 200,
    time: '110ms',
    size: '1.5KB'
  }, {
    key: '5',
    url: '/api/settings',
    method: 'GET',
    status: 404,
    time: '75ms',
    size: '0.5KB'
  }];

  // Network requests columns
  var networkColumns = [{
    title: 'URL',
    dataIndex: 'url',
    key: 'url'
  }, {
    title: 'Method',
    dataIndex: 'method',
    key: 'method'
  }, {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: function render(status) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: status >= 400 ? 'danger' : status >= 300 ? 'warning' : 'success',
        strong: true
      }, status);
    }
  }, {
    title: 'Time',
    dataIndex: 'time',
    key: 'time'
  }, {
    title: 'Size',
    dataIndex: 'size',
    key: 'size'
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MonitorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
    level: 4
  }, "Performance Monitor"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Paragraph, null, "Monitor and analyze the performance of your application."), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    message: "Error",
    description: error,
    type: "error",
    showIcon: true,
    closable: true,
    onClose: function onClose() {
      return setError(null);
    },
    style: {
      marginBottom: _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].spacing */ .Ay.spacing[4]
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    style: {
      marginBottom: _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].spacing */ .Ay.spacing[4]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ReloadOutlined */ .KF4, null),
    onClick: collectMetrics,
    loading: loading
  }, "Refresh Metrics"), lastUpdated && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: "secondary"
  }, "Last updated: ", lastUpdated.toLocaleTimeString())), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TabPane, {
    tab: "Overview",
    key: "overview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricsGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Memory Usage",
    value: formatBytes(metrics.memory.used),
    suffix: " / ".concat(formatBytes(metrics.memory.total))
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Progress */ .ke, {
    percent: Math.round(metrics.memory.used / metrics.memory.total * 100),
    status: metrics.memory.used / metrics.memory.total > 0.9 ? 'exception' : metrics.memory.used / metrics.memory.total > 0.7 ? 'warning' : 'normal',
    strokeColor: getStatusColor(metrics.memory.used / metrics.memory.total * 100, {
      warning: 70,
      danger: 90
    })
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "CPU Usage",
    value: Math.round(metrics.cpu.usage),
    suffix: "%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Progress */ .ke, {
    percent: Math.round(metrics.cpu.usage),
    status: metrics.cpu.usage > 90 ? 'exception' : metrics.cpu.usage > 70 ? 'warning' : 'normal',
    strokeColor: getStatusColor(metrics.cpu.usage, {
      warning: 70,
      danger: 90
    })
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "FPS",
    value: metrics.rendering.fps,
    suffix: "fps"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Progress */ .ke, {
    percent: Math.min(100, Math.round(metrics.rendering.fps / 60 * 100)),
    status: metrics.rendering.fps < 30 ? 'exception' : metrics.rendering.fps < 50 ? 'warning' : 'normal',
    strokeColor: getStatusColor(metrics.rendering.fps, {
      warning: 50,
      danger: 30
    }, true)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Network Requests",
    value: metrics.network.requests
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: _design_system_theme__WEBPACK_IMPORTED_MODULE_6__/* ["default"].spacing */ .Ay.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: metrics.network.errors > 0 ? 'danger' : 'success'
  }, metrics.network.errors, " errors"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: "secondary"
  }, formatBytes(metrics.network.transferred), " transferred")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ChartContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: "secondary"
  }, "Performance charts will be available in a future update"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TabPane, {
    tab: "Network",
    key: "network"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Table */ .XI, {
    dataSource: networkData,
    columns: networkColumns,
    pagination: false,
    size: "small"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TabPane, {
    tab: "Components",
    key: "components"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricsGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Component Count",
    value: metrics.components.count
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Render Count",
    value: metrics.components.renderCount
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Average Render Time",
    value: Math.round(metrics.rendering.renderTime),
    suffix: "ms"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    message: "Component Performance",
    description: "Detailed component performance metrics will be available in a future update.",
    type: "info",
    showIcon: true
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformanceMonitor);

/***/ })

}]);