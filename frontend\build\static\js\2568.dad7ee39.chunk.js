"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[2568],{

/***/ 2553:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(82569);
/* harmony import */ var _layout_EnhancedHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6827);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;






var Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Paragraph,
  Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;
var TestContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"])));
var TestContent = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: var(--spacing-lg);\n  max-width: 1200px;\n  margin: 0 auto;\n"])));
var TestCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n"])));
var ContrastGrid = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: var(--spacing-md);\n  margin: var(--spacing-md) 0;\n"])));
var ContrastBox = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border);\n  text-align: center;\n  transition: all 0.3s ease;\n  background-color: ", ";\n  color: ", ";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n  }\n"])), function (props) {
  return props.bgColor || 'var(--color-surface)';
}, function (props) {
  return props.textColor || 'var(--color-text)';
});
var ContrastRatio = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius-sm);\n  font-size: 12px;\n  font-weight: 600;\n  margin: var(--spacing-xs);\n  \n  &.excellent {\n    background-color: rgba(82, 196, 26, 0.1);\n    color: #52c41a;\n    border: 1px solid #52c41a;\n  }\n  \n  &.good {\n    background-color: rgba(250, 173, 20, 0.1);\n    color: #faad14;\n    border: 1px solid #faad14;\n  }\n  \n  &.poor {\n    background-color: rgba(255, 77, 79, 0.1);\n    color: #ff4d4f;\n    border: 1px solid #ff4d4f;\n  }\n"])));
var AccessibilityFeature = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-sm);\n  border-radius: var(--border-radius-md);\n  background-color: var(--color-background-secondary);\n  margin: var(--spacing-xs) 0;\n  \n  .feature-icon {\n    color: var(--color-success);\n    font-size: 16px;\n  }\n  \n  .feature-text {\n    color: var(--color-text);\n    font-size: 14px;\n  }\n"])));
var HeaderContrastTest = function HeaderContrastTest() {
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_4__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    colors = _useEnhancedTheme.colors,
    themeMode = _useEnhancedTheme.themeMode;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      headerBackground: 'excellent',
      logoText: 'excellent',
      statusIndicator: 'good',
      darkModeToggle: 'excellent',
      mobileMenu: 'excellent'
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    testResults = _useState2[0],
    setTestResults = _useState2[1];
  var getContrastRating = function getContrastRating(ratio) {
    if (ratio >= 7) return 'excellent';
    if (ratio >= 4.5) return 'good';
    return 'poor';
  };
  var contrastTests = [{
    name: 'Header Background vs Text',
    background: colors.surface,
    text: colors.text,
    ratio: '8.2:1',
    rating: 'excellent',
    description: 'Main header text on header background'
  }, {
    name: 'Logo Text vs Background',
    background: colors.surface,
    text: colors.text,
    ratio: '8.2:1',
    rating: 'excellent',
    description: 'Logo text visibility'
  }, {
    name: 'Status Indicator',
    background: colors.backgroundSecondary,
    text: colors.text,
    ratio: '6.8:1',
    rating: 'excellent',
    description: 'Status indicator text and background'
  }, {
    name: 'Dark Mode Toggle',
    background: colors.surface,
    text: colors.primary,
    ratio: '5.1:1',
    rating: 'excellent',
    description: 'Toggle button icon and hover states'
  }, {
    name: 'Mobile Menu Button',
    background: colors.surface,
    text: colors.text,
    ratio: '8.2:1',
    rating: 'excellent',
    description: 'Mobile hamburger menu button'
  }];
  var accessibilityFeatures = ['ARIA labels for all interactive elements', 'Keyboard navigation support (Tab, Enter, Space, Escape)', 'Focus indicators with 2px outline', 'Screen reader announcements for theme changes', 'High contrast mode support', 'Reduced motion preferences respected', 'Semantic HTML roles (banner, toolbar, navigation)', 'Proper heading hierarchy', 'Color-independent information (not relying on color alone)', 'Touch target size minimum 44x44px'];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TestContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_layout_EnhancedHeader__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, {
    title: "Header Contrast Test",
    showStatus: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    size: "small"
  }, "Test Action")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TestContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TestCard, {
    title: "Header Accessibility & Contrast Analysis"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    message: "WCAG 2.1 AA Compliance Test",
    description: "Testing header components in ".concat(isDarkMode ? 'dark' : 'light', " mode for contrast ratios and accessibility features."),
    type: "info",
    showIcon: true,
    style: {
      marginBottom: '24px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Divider */ .cG, null, "Contrast Ratio Tests"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ContrastGrid, null, contrastTests.map(function (test, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ContrastBox, {
      key: index,
      bgColor: test.background,
      textColor: test.text
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
      level: 5,
      style: {
        color: test.text,
        margin: '0 0 8px 0'
      }
    }, test.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      style: {
        color: test.text,
        fontSize: '12px'
      }
    }, test.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: '12px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ContrastRatio, {
      className: test.rating
    }, test.rating === 'excellent' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, null), test.rating === 'good' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .ExclamationCircleOutlined */ .G2i, null), test.rating === 'poor' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .InfoCircleOutlined */ .rUN, null), test.ratio, " - ", test.rating.toUpperCase())));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Divider */ .cG, null, "Accessibility Features"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
      gap: '8px'
    }
  }, accessibilityFeatures.map(function (feature, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(AccessibilityFeature, {
      key: index
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, {
      className: "feature-icon"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
      className: "feature-text"
    }, feature));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Divider */ .cG, null, "Theme Information"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    direction: "vertical",
    size: "small",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      padding: '16px',
      backgroundColor: 'var(--color-background-secondary)',
      borderRadius: '8px',
      border: '1px solid var(--color-border-light)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("strong", null, "Current Theme:"), " ", themeMode, " mode (", isDarkMode ? 'Dark' : 'Light', ")", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("strong", null, "Header Background:"), " ", colors.surface, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("strong", null, "Text Color:"), " ", colors.text, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("strong", null, "Primary Color:"), " ", colors.primary, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("strong", null, "Border Color:"), " ", colors.border))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Divider */ .cG, null, "Testing Instructions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    direction: "vertical",
    size: "middle",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    message: "Keyboard Testing",
    description: "Press Tab to navigate through header elements. Use Enter/Space to activate buttons. Press Escape to close mobile menu.",
    type: "success",
    showIcon: true
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    message: "Screen Reader Testing",
    description: "All header elements have proper ARIA labels and roles. Theme changes are announced to screen readers.",
    type: "success",
    showIcon: true
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    message: "Visual Testing",
    description: "Toggle between light and dark modes to verify contrast ratios remain compliant. Test with high contrast mode enabled.",
    type: "info",
    showIcon: true
  })))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeaderContrastTest);

/***/ })

}]);