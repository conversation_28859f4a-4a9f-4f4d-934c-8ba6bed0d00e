"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[2378],{

/***/ 14884:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  u6: () => (/* reexport */ SHOW_ALL),
  vj: () => (/* reexport */ SHOW_CHILD),
  FA: () => (/* reexport */ SHOW_PARENT),
  nF: () => (/* reexport */ es_TreeNode),
  Ay: () => (/* binding */ rc_tree_select_es)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(89379);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/rc-select/es/index.js + 29 modules
var es = __webpack_require__(1397);
// EXTERNAL MODULE: ./node_modules/rc-select/es/hooks/useId.js
var useId = __webpack_require__(3979);
// EXTERNAL MODULE: ./node_modules/rc-tree/es/utils/conductUtil.js
var conductUtil = __webpack_require__(38820);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(12533);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var es_warning = __webpack_require__(68210);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
;// ./node_modules/rc-tree-select/es/hooks/useCache.js


/**
 * This function will try to call requestIdleCallback if available to save performance.
 * No need `getLabel` here since already fetch on `rawLabeledValue`.
 */
/* harmony default export */ const useCache = (function (values) {
  var cacheRef = react.useRef({
    valueLabels: new Map()
  });
  return react.useMemo(function () {
    var valueLabels = cacheRef.current.valueLabels;
    var valueLabelsCache = new Map();
    var filledValues = values.map(function (item) {
      var value = item.value,
        label = item.label;
      var mergedLabel = label !== null && label !== void 0 ? label : valueLabels.get(value);

      // Save in cache
      valueLabelsCache.set(value, mergedLabel);
      return (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, item), {}, {
        label: mergedLabel
      });
    });
    cacheRef.current.valueLabels = valueLabelsCache;
    return [filledValues];
  }, [values]);
});
;// ./node_modules/rc-tree-select/es/hooks/useCheckedKeys.js



var useCheckedKeys = function useCheckedKeys(rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {
  return react.useMemo(function () {
    var extractValues = function extractValues(values) {
      return values.map(function (_ref) {
        var value = _ref.value;
        return value;
      });
    };
    var checkedKeys = extractValues(rawLabeledValues);
    var halfCheckedKeys = extractValues(rawHalfCheckedValues);
    var missingValues = checkedKeys.filter(function (key) {
      return !keyEntities[key];
    });
    var finalCheckedKeys = checkedKeys;
    var finalHalfCheckedKeys = halfCheckedKeys;
    if (treeConduction) {
      var conductResult = (0,conductUtil/* conductCheck */.p)(checkedKeys, true, keyEntities);
      finalCheckedKeys = conductResult.checkedKeys;
      finalHalfCheckedKeys = conductResult.halfCheckedKeys;
    }
    return [Array.from(new Set([].concat((0,toConsumableArray/* default */.A)(missingValues), (0,toConsumableArray/* default */.A)(finalCheckedKeys)))), finalHalfCheckedKeys];
  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);
};
/* harmony default export */ const hooks_useCheckedKeys = (useCheckedKeys);
// EXTERNAL MODULE: ./node_modules/rc-tree/es/utils/treeUtil.js
var treeUtil = __webpack_require__(7974);
;// ./node_modules/rc-tree-select/es/utils/valueUtil.js
var valueUtil_toArray = function toArray(value) {
  return Array.isArray(value) ? value : value !== undefined ? [value] : [];
};
var fillFieldNames = function fillFieldNames(fieldNames) {
  var _ref = fieldNames || {},
    label = _ref.label,
    value = _ref.value,
    children = _ref.children;
  return {
    _title: label ? [label] : ['title', 'label'],
    value: value || 'value',
    key: value || 'value',
    children: children || 'children'
  };
};
var isCheckDisabled = function isCheckDisabled(node) {
  return !node || node.disabled || node.disableCheckbox || node.checkable === false;
};
var getAllKeys = function getAllKeys(treeData, fieldNames) {
  var keys = [];
  var dig = function dig(list) {
    list.forEach(function (item) {
      var children = item[fieldNames.children];
      if (children) {
        keys.push(item[fieldNames.value]);
        dig(children);
      }
    });
  };
  dig(treeData);
  return keys;
};
var isNil = function isNil(val) {
  return val === null || val === undefined;
};
;// ./node_modules/rc-tree-select/es/hooks/useDataEntities.js





/* harmony default export */ const useDataEntities = (function (treeData, fieldNames) {
  return react.useMemo(function () {
    var collection = (0,treeUtil/* convertDataToEntities */.cG)(treeData, {
      fieldNames: fieldNames,
      initWrapper: function initWrapper(wrapper) {
        return (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, wrapper), {}, {
          valueEntities: new Map()
        });
      },
      processEntity: function processEntity(entity, wrapper) {
        var val = entity.node[fieldNames.value];

        // Check if exist same value
        if (false) { var key; }
        wrapper.valueEntities.set(val, entity);
      }
    });
    return collection;
  }, [treeData, fieldNames]);
});
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Children/toArray.js
var Children_toArray = __webpack_require__(82546);
;// ./node_modules/rc-tree-select/es/TreeNode.js
/* istanbul ignore file */

/** This is a placeholder, not real render in dom */
var TreeNode = function TreeNode() {
  return null;
};
/* harmony default export */ const es_TreeNode = (TreeNode);
;// ./node_modules/rc-tree-select/es/utils/legacyUtil.js


var _excluded = ["children", "value"];




function convertChildrenToData(nodes) {
  return (0,Children_toArray/* default */.A)(nodes).map(function (node) {
    if (! /*#__PURE__*/react.isValidElement(node) || !node.type) {
      return null;
    }
    var _ref = node,
      key = _ref.key,
      _ref$props = _ref.props,
      children = _ref$props.children,
      value = _ref$props.value,
      restProps = (0,objectWithoutProperties/* default */.A)(_ref$props, _excluded);
    var data = (0,objectSpread2/* default */.A)({
      key: key,
      value: value
    }, restProps);
    var childData = convertChildrenToData(children);
    if (childData.length) {
      data.children = childData;
    }
    return data;
  }).filter(function (data) {
    return data;
  });
}
function fillLegacyProps(dataNode) {
  if (!dataNode) {
    return dataNode;
  }
  var cloneNode = (0,objectSpread2/* default */.A)({}, dataNode);
  if (!('props' in cloneNode)) {
    Object.defineProperty(cloneNode, 'props', {
      get: function get() {
        (0,es_warning/* default */.Ay)(false, 'New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.');
        return cloneNode;
      }
    });
  }
  return cloneNode;
}
function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition, fieldNames) {
  var triggerNode = null;
  var nodeList = null;
  function generateMap() {
    function dig(list) {
      var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0';
      var parentIncluded = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      return list.map(function (option, index) {
        var pos = "".concat(level, "-").concat(index);
        var value = option[fieldNames.value];
        var included = checkedValues.includes(value);
        var children = dig(option[fieldNames.children] || [], pos, included);
        var node = /*#__PURE__*/react.createElement(es_TreeNode, option, children.map(function (child) {
          return child.node;
        }));

        // Link with trigger node
        if (triggerValue === value) {
          triggerNode = node;
        }
        if (included) {
          var checkedNode = {
            pos: pos,
            node: node,
            children: children
          };
          if (!parentIncluded) {
            nodeList.push(checkedNode);
          }
          return checkedNode;
        }
        return null;
      }).filter(function (node) {
        return node;
      });
    }
    if (!nodeList) {
      nodeList = [];
      dig(treeData);

      // Sort to keep the checked node length
      nodeList.sort(function (_ref2, _ref3) {
        var val1 = _ref2.node.props.value;
        var val2 = _ref3.node.props.value;
        var index1 = checkedValues.indexOf(val1);
        var index2 = checkedValues.indexOf(val2);
        return index1 - index2;
      });
    }
  }
  Object.defineProperty(extra, 'triggerNode', {
    get: function get() {
      (0,es_warning/* default */.Ay)(false, '`triggerNode` is deprecated. Please consider decoupling data with node.');
      generateMap();
      return triggerNode;
    }
  });
  Object.defineProperty(extra, 'allCheckedNodes', {
    get: function get() {
      (0,es_warning/* default */.Ay)(false, '`allCheckedNodes` is deprecated. Please consider decoupling data with node.');
      generateMap();
      if (showPosition) {
        return nodeList;
      }
      return nodeList.map(function (_ref4) {
        var node = _ref4.node;
        return node;
      });
    }
  });
}
;// ./node_modules/rc-tree-select/es/hooks/useFilterTreeData.js




var useFilterTreeData = function useFilterTreeData(treeData, searchValue, options) {
  var fieldNames = options.fieldNames,
    treeNodeFilterProp = options.treeNodeFilterProp,
    filterTreeNode = options.filterTreeNode;
  var fieldChildren = fieldNames.children;
  return react.useMemo(function () {
    if (!searchValue || filterTreeNode === false) {
      return treeData;
    }
    var filterOptionFunc = typeof filterTreeNode === 'function' ? filterTreeNode : function (_, dataNode) {
      return String(dataNode[treeNodeFilterProp]).toUpperCase().includes(searchValue.toUpperCase());
    };
    var filterTreeNodes = function filterTreeNodes(nodes) {
      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      return nodes.reduce(function (filtered, node) {
        var children = node[fieldChildren];
        var isMatch = keepAll || filterOptionFunc(searchValue, fillLegacyProps(node));
        var filteredChildren = filterTreeNodes(children || [], isMatch);
        if (isMatch || filteredChildren.length) {
          filtered.push((0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, node), {}, (0,defineProperty/* default */.A)({
            isLeaf: undefined
          }, fieldChildren, filteredChildren)));
        }
        return filtered;
      }, []);
    };
    return filterTreeNodes(treeData);
  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);
};
/* harmony default export */ const hooks_useFilterTreeData = (useFilterTreeData);
;// ./node_modules/rc-tree-select/es/hooks/useRefFunc.js


/**
 * Same as `React.useCallback` but always return a memoized function
 * but redirect to real function.
 */
function useRefFunc(callback) {
  var funcRef = react.useRef();
  funcRef.current = callback;
  var cacheFn = react.useCallback(function () {
    return funcRef.current.apply(funcRef, arguments);
  }, []);
  return cacheFn;
}
;// ./node_modules/rc-tree-select/es/hooks/useTreeData.js




function buildTreeStructure(nodes, config) {
  var id = config.id,
    pId = config.pId,
    rootPId = config.rootPId;
  var nodeMap = new Map();
  var rootNodes = [];
  nodes.forEach(function (node) {
    var nodeKey = node[id];
    var clonedNode = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, node), {}, {
      key: node.key || nodeKey
    });
    nodeMap.set(nodeKey, clonedNode);
  });
  nodeMap.forEach(function (node) {
    var parentKey = node[pId];
    var parent = nodeMap.get(parentKey);
    if (parent) {
      parent.children = parent.children || [];
      parent.children.push(node);
    } else if (parentKey === rootPId || rootPId === null) {
      rootNodes.push(node);
    }
  });
  return rootNodes;
}

/**
 * 将 `treeData` 或 `children` 转换为格式化的 `treeData`。
 * 如果 `treeData` 或 `children` 没有变化，则不会重新计算。
 */
function useTreeData(treeData, children, simpleMode) {
  return react.useMemo(function () {
    if (treeData) {
      if (simpleMode) {
        var config = (0,objectSpread2/* default */.A)({
          id: 'id',
          pId: 'pId',
          rootPId: null
        }, (0,esm_typeof/* default */.A)(simpleMode) === 'object' ? simpleMode : {});
        return buildTreeStructure(treeData, config);
      }
      return treeData;
    }
    return convertChildrenToData(children);
  }, [children, simpleMode, treeData]);
}
;// ./node_modules/rc-tree-select/es/LegacyContext.js

var LegacySelectContext = /*#__PURE__*/react.createContext(null);
/* harmony default export */ const LegacyContext = (LegacySelectContext);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(24765);
// EXTERNAL MODULE: ./node_modules/rc-tree/es/index.js + 6 modules
var rc_tree_es = __webpack_require__(1444);
// EXTERNAL MODULE: ./node_modules/rc-util/es/KeyCode.js
var KeyCode = __webpack_require__(16928);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMemo.js
var useMemo = __webpack_require__(28104);
;// ./node_modules/rc-tree-select/es/TreeSelectContext.js

var TreeSelectContext = /*#__PURE__*/react.createContext(null);
/* harmony default export */ const es_TreeSelectContext = (TreeSelectContext);
// EXTERNAL MODULE: ./node_modules/rc-util/es/index.js
var rc_util_es = __webpack_require__(81470);
;// ./node_modules/rc-tree-select/es/OptionList.js














var HIDDEN_STYLE = {
  width: 0,
  height: 0,
  display: 'flex',
  overflow: 'hidden',
  opacity: 0,
  border: 0,
  padding: 0,
  margin: 0
};
var OptionList = function OptionList(_, ref) {
  var _useBaseProps = (0,es/* useBaseProps */.Vm)(),
    prefixCls = _useBaseProps.prefixCls,
    multiple = _useBaseProps.multiple,
    searchValue = _useBaseProps.searchValue,
    toggleOpen = _useBaseProps.toggleOpen,
    open = _useBaseProps.open,
    notFoundContent = _useBaseProps.notFoundContent;
  var _React$useContext = react.useContext(es_TreeSelectContext),
    virtual = _React$useContext.virtual,
    listHeight = _React$useContext.listHeight,
    listItemHeight = _React$useContext.listItemHeight,
    listItemScrollOffset = _React$useContext.listItemScrollOffset,
    treeData = _React$useContext.treeData,
    fieldNames = _React$useContext.fieldNames,
    onSelect = _React$useContext.onSelect,
    dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth,
    treeExpandAction = _React$useContext.treeExpandAction,
    treeTitleRender = _React$useContext.treeTitleRender,
    onPopupScroll = _React$useContext.onPopupScroll,
    leftMaxCount = _React$useContext.leftMaxCount,
    leafCountOnly = _React$useContext.leafCountOnly,
    valueEntities = _React$useContext.valueEntities;
  var _React$useContext2 = react.useContext(LegacyContext),
    checkable = _React$useContext2.checkable,
    checkedKeys = _React$useContext2.checkedKeys,
    halfCheckedKeys = _React$useContext2.halfCheckedKeys,
    treeExpandedKeys = _React$useContext2.treeExpandedKeys,
    treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll,
    treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys,
    onTreeExpand = _React$useContext2.onTreeExpand,
    treeIcon = _React$useContext2.treeIcon,
    showTreeIcon = _React$useContext2.showTreeIcon,
    switcherIcon = _React$useContext2.switcherIcon,
    treeLine = _React$useContext2.treeLine,
    treeNodeFilterProp = _React$useContext2.treeNodeFilterProp,
    loadData = _React$useContext2.loadData,
    treeLoadedKeys = _React$useContext2.treeLoadedKeys,
    treeMotion = _React$useContext2.treeMotion,
    onTreeLoad = _React$useContext2.onTreeLoad,
    keyEntities = _React$useContext2.keyEntities;
  var treeRef = react.useRef();
  var memoTreeData = (0,useMemo/* default */.A)(function () {
    return treeData;
  },
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [open, treeData], function (prev, next) {
    return next[0] && prev[1] !== next[1];
  });

  // ========================== Values ==========================
  var mergedCheckedKeys = react.useMemo(function () {
    if (!checkable) {
      return null;
    }
    return {
      checked: checkedKeys,
      halfChecked: halfCheckedKeys
    };
  }, [checkable, checkedKeys, halfCheckedKeys]);

  // ========================== Scroll ==========================
  react.useEffect(function () {
    // Single mode should scroll to current key
    if (open && !multiple && checkedKeys.length) {
      var _treeRef$current;
      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 || _treeRef$current.scrollTo({
        key: checkedKeys[0]
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  // ========================== Events ==========================
  var onListMouseDown = function onListMouseDown(event) {
    event.preventDefault();
  };
  var onInternalSelect = function onInternalSelect(__, info) {
    var node = info.node;
    if (checkable && isCheckDisabled(node)) {
      return;
    }
    onSelect(node.key, {
      selected: !checkedKeys.includes(node.key)
    });
    if (!multiple) {
      toggleOpen(false);
    }
  };

  // =========================== Keys ===========================
  var _React$useState = react.useState(treeDefaultExpandedKeys),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    expandedKeys = _React$useState2[0],
    setExpandedKeys = _React$useState2[1];
  var _React$useState3 = react.useState(null),
    _React$useState4 = (0,slicedToArray/* default */.A)(_React$useState3, 2),
    searchExpandedKeys = _React$useState4[0],
    setSearchExpandedKeys = _React$useState4[1];
  var mergedExpandedKeys = react.useMemo(function () {
    if (treeExpandedKeys) {
      return (0,toConsumableArray/* default */.A)(treeExpandedKeys);
    }
    return searchValue ? searchExpandedKeys : expandedKeys;
  }, [expandedKeys, searchExpandedKeys, treeExpandedKeys, searchValue]);
  var onInternalExpand = function onInternalExpand(keys) {
    setExpandedKeys(keys);
    setSearchExpandedKeys(keys);
    if (onTreeExpand) {
      onTreeExpand(keys);
    }
  };

  // ========================== Search ==========================
  var lowerSearchValue = String(searchValue).toLowerCase();
  var filterTreeNode = function filterTreeNode(treeNode) {
    if (!lowerSearchValue) {
      return false;
    }
    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);
  };
  react.useEffect(function () {
    if (searchValue) {
      setSearchExpandedKeys(getAllKeys(treeData, fieldNames));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue]);

  // ========================= Disabled =========================
  // Cache disabled states in React state to ensure re-render when cache updates
  var _React$useState5 = react.useState(function () {
      return new Map();
    }),
    _React$useState6 = (0,slicedToArray/* default */.A)(_React$useState5, 2),
    disabledCache = _React$useState6[0],
    setDisabledCache = _React$useState6[1];
  react.useEffect(function () {
    if (leftMaxCount) {
      setDisabledCache(new Map());
    }
  }, [leftMaxCount]);
  function getDisabledWithCache(node) {
    var value = node[fieldNames.value];
    if (!disabledCache.has(value)) {
      var entity = valueEntities.get(value);
      var isLeaf = (entity.children || []).length === 0;
      if (!isLeaf) {
        var checkableChildren = entity.children.filter(function (childTreeNode) {
          return !childTreeNode.node.disabled && !childTreeNode.node.disableCheckbox && !checkedKeys.includes(childTreeNode.node[fieldNames.value]);
        });
        var checkableChildrenCount = checkableChildren.length;
        disabledCache.set(value, checkableChildrenCount > leftMaxCount);
      } else {
        disabledCache.set(value, false);
      }
    }
    return disabledCache.get(value);
  }
  var nodeDisabled = (0,rc_util_es/* useEvent */._q)(function (node) {
    var nodeValue = node[fieldNames.value];
    if (checkedKeys.includes(nodeValue)) {
      return false;
    }
    if (leftMaxCount === null) {
      return false;
    }
    if (leftMaxCount <= 0) {
      return true;
    }

    // This is a low performance calculation
    if (leafCountOnly && leftMaxCount) {
      return getDisabledWithCache(node);
    }
    return false;
  });

  // ========================== Get First Selectable Node ==========================
  var getFirstMatchingNode = function getFirstMatchingNode(nodes) {
    var _iterator = (0,createForOfIteratorHelper/* default */.A)(nodes),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var node = _step.value;
        if (node.disabled || node.selectable === false) {
          continue;
        }
        if (searchValue) {
          if (filterTreeNode(node)) {
            return node;
          }
        } else {
          return node;
        }
        if (node[fieldNames.children]) {
          var matchInChildren = getFirstMatchingNode(node[fieldNames.children]);
          if (matchInChildren) {
            return matchInChildren;
          }
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    return null;
  };

  // ========================== Active ==========================
  var _React$useState7 = react.useState(null),
    _React$useState8 = (0,slicedToArray/* default */.A)(_React$useState7, 2),
    activeKey = _React$useState8[0],
    setActiveKey = _React$useState8[1];
  var activeEntity = keyEntities[activeKey];
  react.useEffect(function () {
    if (!open) {
      return;
    }
    var nextActiveKey = null;
    var getFirstNode = function getFirstNode() {
      var firstNode = getFirstMatchingNode(memoTreeData);
      return firstNode ? firstNode[fieldNames.value] : null;
    };

    // single mode active first checked node
    if (!multiple && checkedKeys.length && !searchValue) {
      nextActiveKey = checkedKeys[0];
    } else {
      nextActiveKey = getFirstNode();
    }
    setActiveKey(nextActiveKey);
  }, [open, searchValue]);

  // ========================= Keyboard =========================
  react.useImperativeHandle(ref, function () {
    var _treeRef$current2;
    return {
      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,
      onKeyDown: function onKeyDown(event) {
        var _treeRef$current3;
        var which = event.which;
        switch (which) {
          // >>> Arrow keys
          case KeyCode/* default */.A.UP:
          case KeyCode/* default */.A.DOWN:
          case KeyCode/* default */.A.LEFT:
          case KeyCode/* default */.A.RIGHT:
            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 || _treeRef$current3.onKeyDown(event);
            break;

          // >>> Select item
          case KeyCode/* default */.A.ENTER:
            {
              if (activeEntity) {
                var isNodeDisabled = nodeDisabled(activeEntity.node);
                var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {},
                  selectable = _ref.selectable,
                  value = _ref.value,
                  disabled = _ref.disabled;
                if (selectable !== false && !disabled && !isNodeDisabled) {
                  onInternalSelect(null, {
                    node: {
                      key: activeKey
                    },
                    selected: !checkedKeys.includes(value)
                  });
                }
              }
              break;
            }

          // >>> Close
          case KeyCode/* default */.A.ESC:
            {
              toggleOpen(false);
            }
        }
      },
      onKeyUp: function onKeyUp() {}
    };
  });
  var hasLoadDataFn = (0,useMemo/* default */.A)(function () {
    return searchValue ? false : true;
  }, [searchValue, treeExpandedKeys || expandedKeys], function (_ref2, _ref3) {
    var _ref4 = (0,slicedToArray/* default */.A)(_ref2, 1),
      preSearchValue = _ref4[0];
    var _ref5 = (0,slicedToArray/* default */.A)(_ref3, 2),
      nextSearchValue = _ref5[0],
      nextExcludeSearchExpandedKeys = _ref5[1];
    return preSearchValue !== nextSearchValue && !!(nextSearchValue || nextExcludeSearchExpandedKeys);
  });
  var syncLoadData = hasLoadDataFn ? loadData : null;

  // ========================== Render ==========================
  if (memoTreeData.length === 0) {
    return /*#__PURE__*/react.createElement("div", {
      role: "listbox",
      className: "".concat(prefixCls, "-empty"),
      onMouseDown: onListMouseDown
    }, notFoundContent);
  }
  var treeProps = {
    fieldNames: fieldNames
  };
  if (treeLoadedKeys) {
    treeProps.loadedKeys = treeLoadedKeys;
  }
  if (mergedExpandedKeys) {
    treeProps.expandedKeys = mergedExpandedKeys;
  }
  return /*#__PURE__*/react.createElement("div", {
    onMouseDown: onListMouseDown
  }, activeEntity && open && /*#__PURE__*/react.createElement("span", {
    style: HIDDEN_STYLE,
    "aria-live": "assertive"
  }, activeEntity.node.value), /*#__PURE__*/react.createElement(rc_tree_es/* UnstableContext */.QB.Provider, {
    value: {
      nodeDisabled: nodeDisabled
    }
  }, /*#__PURE__*/react.createElement(rc_tree_es/* default */.Ay, (0,esm_extends/* default */.A)({
    ref: treeRef,
    focusable: false,
    prefixCls: "".concat(prefixCls, "-tree"),
    treeData: memoTreeData,
    height: listHeight,
    itemHeight: listItemHeight,
    itemScrollOffset: listItemScrollOffset,
    virtual: virtual !== false && dropdownMatchSelectWidth !== false,
    multiple: multiple,
    icon: treeIcon,
    showIcon: showTreeIcon,
    switcherIcon: switcherIcon,
    showLine: treeLine,
    loadData: syncLoadData,
    motion: treeMotion,
    activeKey: activeKey
    // We handle keys by out instead tree self
    ,
    checkable: checkable,
    checkStrictly: true,
    checkedKeys: mergedCheckedKeys,
    selectedKeys: !checkable ? checkedKeys : [],
    defaultExpandAll: treeDefaultExpandAll,
    titleRender: treeTitleRender
  }, treeProps, {
    // Proxy event out
    onActiveChange: setActiveKey,
    onSelect: onInternalSelect,
    onCheck: onInternalSelect,
    onExpand: onInternalExpand,
    onLoad: onTreeLoad,
    filterTreeNode: filterTreeNode,
    expandAction: treeExpandAction,
    onScroll: onPopupScroll
  }))));
};
var RefOptionList = /*#__PURE__*/react.forwardRef(OptionList);
if (false) {}
/* harmony default export */ const es_OptionList = (RefOptionList);
;// ./node_modules/rc-tree-select/es/utils/strategyUtil.js

var SHOW_ALL = 'SHOW_ALL';
var SHOW_PARENT = 'SHOW_PARENT';
var SHOW_CHILD = 'SHOW_CHILD';
function formatStrategyValues(values, strategy, keyEntities, fieldNames) {
  var valueSet = new Set(values);
  if (strategy === SHOW_CHILD) {
    return values.filter(function (key) {
      var entity = keyEntities[key];
      return !entity || !entity.children || !entity.children.some(function (_ref) {
        var node = _ref.node;
        return valueSet.has(node[fieldNames.value]);
      }) || !entity.children.every(function (_ref2) {
        var node = _ref2.node;
        return isCheckDisabled(node) || valueSet.has(node[fieldNames.value]);
      });
    });
  }
  if (strategy === SHOW_PARENT) {
    return values.filter(function (key) {
      var entity = keyEntities[key];
      var parent = entity ? entity.parent : null;
      return !parent || isCheckDisabled(parent.node) || !valueSet.has(parent.key);
    });
  }
  return values;
}
;// ./node_modules/rc-tree-select/es/utils/warningPropsUtil.js



function warningProps(props) {
  var searchPlaceholder = props.searchPlaceholder,
    treeCheckStrictly = props.treeCheckStrictly,
    treeCheckable = props.treeCheckable,
    labelInValue = props.labelInValue,
    value = props.value,
    multiple = props.multiple,
    showCheckedStrategy = props.showCheckedStrategy,
    maxCount = props.maxCount;
  warning(!searchPlaceholder, '`searchPlaceholder` has been removed.');
  if (treeCheckStrictly && labelInValue === false) {
    warning(false, '`treeCheckStrictly` will force set `labelInValue` to `true`.');
  }
  if (labelInValue || treeCheckStrictly) {
    warning(toArray(value).every(function (val) {
      return val && _typeof(val) === 'object' && 'value' in val;
    }), 'Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.');
  }
  if (treeCheckStrictly || multiple || treeCheckable) {
    warning(!value || Array.isArray(value), '`value` should be an array when `TreeSelect` is checkable or multiple.');
  } else {
    warning(!Array.isArray(value), '`value` should not be array when `TreeSelect` is single mode.');
  }
  if (maxCount && (showCheckedStrategy === 'SHOW_ALL' && !treeCheckStrictly || showCheckedStrategy === 'SHOW_PARENT')) {
    warning(false, '`maxCount` not work with `showCheckedStrategy=SHOW_ALL` (when `treeCheckStrictly=false`) or `showCheckedStrategy=SHOW_PARENT`.');
  }
}
/* harmony default export */ const warningPropsUtil = ((/* unused pure expression or super */ null && (warningProps)));
;// ./node_modules/rc-tree-select/es/TreeSelect.js






var TreeSelect_excluded = ["id", "prefixCls", "value", "defaultValue", "onChange", "onSelect", "onDeselect", "searchValue", "inputValue", "onSearch", "autoClearSearchValue", "filterTreeNode", "treeNodeFilterProp", "showCheckedStrategy", "treeNodeLabelProp", "multiple", "treeCheckable", "treeCheckStrictly", "labelInValue", "maxCount", "fieldNames", "treeDataSimpleMode", "treeData", "children", "loadData", "treeLoadedKeys", "onTreeLoad", "treeDefaultExpandAll", "treeExpandedKeys", "treeDefaultExpandedKeys", "onTreeExpand", "treeExpandAction", "virtual", "listHeight", "listItemHeight", "listItemScrollOffset", "onDropdownVisibleChange", "dropdownMatchSelectWidth", "treeLine", "treeIcon", "showTreeIcon", "switcherIcon", "treeMotion", "treeTitleRender", "onPopupScroll"];




















function isRawValue(value) {
  return !value || (0,esm_typeof/* default */.A)(value) !== 'object';
}
var TreeSelect = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var id = props.id,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-tree-select' : _props$prefixCls,
    value = props.value,
    defaultValue = props.defaultValue,
    onChange = props.onChange,
    onSelect = props.onSelect,
    onDeselect = props.onDeselect,
    searchValue = props.searchValue,
    inputValue = props.inputValue,
    onSearch = props.onSearch,
    _props$autoClearSearc = props.autoClearSearchValue,
    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,
    filterTreeNode = props.filterTreeNode,
    _props$treeNodeFilter = props.treeNodeFilterProp,
    treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter,
    showCheckedStrategy = props.showCheckedStrategy,
    treeNodeLabelProp = props.treeNodeLabelProp,
    multiple = props.multiple,
    treeCheckable = props.treeCheckable,
    treeCheckStrictly = props.treeCheckStrictly,
    labelInValue = props.labelInValue,
    maxCount = props.maxCount,
    fieldNames = props.fieldNames,
    treeDataSimpleMode = props.treeDataSimpleMode,
    treeData = props.treeData,
    children = props.children,
    loadData = props.loadData,
    treeLoadedKeys = props.treeLoadedKeys,
    onTreeLoad = props.onTreeLoad,
    treeDefaultExpandAll = props.treeDefaultExpandAll,
    treeExpandedKeys = props.treeExpandedKeys,
    treeDefaultExpandedKeys = props.treeDefaultExpandedKeys,
    onTreeExpand = props.onTreeExpand,
    treeExpandAction = props.treeExpandAction,
    virtual = props.virtual,
    _props$listHeight = props.listHeight,
    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,
    _props$listItemHeight = props.listItemHeight,
    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,
    _props$listItemScroll = props.listItemScrollOffset,
    listItemScrollOffset = _props$listItemScroll === void 0 ? 0 : _props$listItemScroll,
    onDropdownVisibleChange = props.onDropdownVisibleChange,
    _props$dropdownMatchS = props.dropdownMatchSelectWidth,
    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,
    treeLine = props.treeLine,
    treeIcon = props.treeIcon,
    showTreeIcon = props.showTreeIcon,
    switcherIcon = props.switcherIcon,
    treeMotion = props.treeMotion,
    treeTitleRender = props.treeTitleRender,
    onPopupScroll = props.onPopupScroll,
    restProps = (0,objectWithoutProperties/* default */.A)(props, TreeSelect_excluded);
  var mergedId = (0,useId/* default */.Ay)(id);
  var treeConduction = treeCheckable && !treeCheckStrictly;
  var mergedCheckable = treeCheckable || treeCheckStrictly;
  var mergedLabelInValue = treeCheckStrictly || labelInValue;
  var mergedMultiple = mergedCheckable || multiple;
  var _useMergedState = (0,useMergedState/* default */.A)(defaultValue, {
      value: value
    }),
    _useMergedState2 = (0,slicedToArray/* default */.A)(_useMergedState, 2),
    internalValue = _useMergedState2[0],
    setInternalValue = _useMergedState2[1];

  // `multiple` && `!treeCheckable` should be show all
  var mergedShowCheckedStrategy = react.useMemo(function () {
    if (!treeCheckable) {
      return SHOW_ALL;
    }
    return showCheckedStrategy || SHOW_CHILD;
  }, [showCheckedStrategy, treeCheckable]);

  // ========================== Warning ===========================
  if (false) {}

  // ========================= FieldNames =========================
  var mergedFieldNames = react.useMemo(function () {
    return fillFieldNames(fieldNames);
  }, /* eslint-disable react-hooks/exhaustive-deps */
  [JSON.stringify(fieldNames)]
  /* eslint-enable react-hooks/exhaustive-deps */);

  // =========================== Search ===========================
  var _useMergedState3 = (0,useMergedState/* default */.A)('', {
      value: searchValue !== undefined ? searchValue : inputValue,
      postState: function postState(search) {
        return search || '';
      }
    }),
    _useMergedState4 = (0,slicedToArray/* default */.A)(_useMergedState3, 2),
    mergedSearchValue = _useMergedState4[0],
    setSearchValue = _useMergedState4[1];
  var onInternalSearch = function onInternalSearch(searchText) {
    setSearchValue(searchText);
    onSearch === null || onSearch === void 0 || onSearch(searchText);
  };

  // ============================ Data ============================
  // `useTreeData` only do convert of `children` or `simpleMode`.
  // Else will return origin `treeData` for perf consideration.
  // Do not do anything to loop the data.
  var mergedTreeData = useTreeData(treeData, children, treeDataSimpleMode);
  var _useDataEntities = useDataEntities(mergedTreeData, mergedFieldNames),
    keyEntities = _useDataEntities.keyEntities,
    valueEntities = _useDataEntities.valueEntities;

  /** Get `missingRawValues` which not exist in the tree yet */
  var splitRawValues = react.useCallback(function (newRawValues) {
    var missingRawValues = [];
    var existRawValues = [];

    // Keep missing value in the cache
    newRawValues.forEach(function (val) {
      if (valueEntities.has(val)) {
        existRawValues.push(val);
      } else {
        missingRawValues.push(val);
      }
    });
    return {
      missingRawValues: missingRawValues,
      existRawValues: existRawValues
    };
  }, [valueEntities]);

  // Filtered Tree
  var filteredTreeData = hooks_useFilterTreeData(mergedTreeData, mergedSearchValue, {
    fieldNames: mergedFieldNames,
    treeNodeFilterProp: treeNodeFilterProp,
    filterTreeNode: filterTreeNode
  });

  // =========================== Label ============================
  var getLabel = react.useCallback(function (item) {
    if (item) {
      if (treeNodeLabelProp) {
        return item[treeNodeLabelProp];
      }

      // Loop from fieldNames
      var titleList = mergedFieldNames._title;
      for (var i = 0; i < titleList.length; i += 1) {
        var title = item[titleList[i]];
        if (title !== undefined) {
          return title;
        }
      }
    }
  }, [mergedFieldNames, treeNodeLabelProp]);

  // ========================= Wrap Value =========================
  var toLabeledValues = react.useCallback(function (draftValues) {
    var values = valueUtil_toArray(draftValues);
    return values.map(function (val) {
      if (isRawValue(val)) {
        return {
          value: val
        };
      }
      return val;
    });
  }, []);
  var convert2LabelValues = react.useCallback(function (draftValues) {
    var values = toLabeledValues(draftValues);
    return values.map(function (item) {
      var rawLabel = item.label;
      var rawValue = item.value,
        rawHalfChecked = item.halfChecked;
      var rawDisabled;
      var entity = valueEntities.get(rawValue);

      // Fill missing label & status
      if (entity) {
        var _rawLabel;
        rawLabel = treeTitleRender ? treeTitleRender(entity.node) : (_rawLabel = rawLabel) !== null && _rawLabel !== void 0 ? _rawLabel : getLabel(entity.node);
        rawDisabled = entity.node.disabled;
      } else if (rawLabel === undefined) {
        // We try to find in current `labelInValue` value
        var labelInValueItem = toLabeledValues(internalValue).find(function (labeledItem) {
          return labeledItem.value === rawValue;
        });
        rawLabel = labelInValueItem.label;
      }
      return {
        label: rawLabel,
        value: rawValue,
        halfChecked: rawHalfChecked,
        disabled: rawDisabled
      };
    });
  }, [valueEntities, getLabel, toLabeledValues, internalValue]);

  // =========================== Values ===========================
  var rawMixedLabeledValues = react.useMemo(function () {
    return toLabeledValues(internalValue === null ? [] : internalValue);
  }, [toLabeledValues, internalValue]);

  // Split value into full check and half check
  var _React$useMemo = react.useMemo(function () {
      var fullCheckValues = [];
      var halfCheckValues = [];
      rawMixedLabeledValues.forEach(function (item) {
        if (item.halfChecked) {
          halfCheckValues.push(item);
        } else {
          fullCheckValues.push(item);
        }
      });
      return [fullCheckValues, halfCheckValues];
    }, [rawMixedLabeledValues]),
    _React$useMemo2 = (0,slicedToArray/* default */.A)(_React$useMemo, 2),
    rawLabeledValues = _React$useMemo2[0],
    rawHalfLabeledValues = _React$useMemo2[1];

  // const [mergedValues] = useCache(rawLabeledValues);
  var rawValues = react.useMemo(function () {
    return rawLabeledValues.map(function (item) {
      return item.value;
    });
  }, [rawLabeledValues]);

  // Convert value to key. Will fill missed keys for conduct check.
  var _useCheckedKeys = hooks_useCheckedKeys(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities),
    _useCheckedKeys2 = (0,slicedToArray/* default */.A)(_useCheckedKeys, 2),
    rawCheckedValues = _useCheckedKeys2[0],
    rawHalfCheckedValues = _useCheckedKeys2[1];

  // Convert rawCheckedKeys to check strategy related values
  var displayValues = react.useMemo(function () {
    // Collect keys which need to show
    var displayKeys = formatStrategyValues(rawCheckedValues, mergedShowCheckedStrategy, keyEntities, mergedFieldNames);

    // Convert to value and filled with label
    var values = displayKeys.map(function (key) {
      var _keyEntities$key$node, _keyEntities$key;
      return (_keyEntities$key$node = (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 || (_keyEntities$key = _keyEntities$key.node) === null || _keyEntities$key === void 0 ? void 0 : _keyEntities$key[mergedFieldNames.value]) !== null && _keyEntities$key$node !== void 0 ? _keyEntities$key$node : key;
    });

    // Back fill with origin label
    var labeledValues = values.map(function (val) {
      var targetItem = rawLabeledValues.find(function (item) {
        return item.value === val;
      });
      var label = labelInValue ? targetItem === null || targetItem === void 0 ? void 0 : targetItem.label : treeTitleRender === null || treeTitleRender === void 0 ? void 0 : treeTitleRender(targetItem);
      return {
        value: val,
        label: label
      };
    });
    var rawDisplayValues = convert2LabelValues(labeledValues);
    var firstVal = rawDisplayValues[0];
    if (!mergedMultiple && firstVal && isNil(firstVal.value) && isNil(firstVal.label)) {
      return [];
    }
    return rawDisplayValues.map(function (item) {
      var _item$label;
      return (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, item), {}, {
        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value
      });
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mergedFieldNames, mergedMultiple, rawCheckedValues, rawLabeledValues, convert2LabelValues, mergedShowCheckedStrategy, keyEntities]);
  var _useCache = useCache(displayValues),
    _useCache2 = (0,slicedToArray/* default */.A)(_useCache, 1),
    cachedDisplayValues = _useCache2[0];

  // ========================== MaxCount ==========================
  var mergedMaxCount = react.useMemo(function () {
    if (mergedMultiple && (mergedShowCheckedStrategy === 'SHOW_CHILD' || treeCheckStrictly || !treeCheckable)) {
      return maxCount;
    }
    return null;
  }, [maxCount, mergedMultiple, treeCheckStrictly, mergedShowCheckedStrategy, treeCheckable]);

  // =========================== Change ===========================
  var triggerChange = useRefFunc(function (newRawValues, extra, source) {
    var formattedKeyList = formatStrategyValues(newRawValues, mergedShowCheckedStrategy, keyEntities, mergedFieldNames);

    // Not allow pass with `maxCount`
    if (mergedMaxCount && formattedKeyList.length > mergedMaxCount) {
      return;
    }
    var labeledValues = convert2LabelValues(newRawValues);
    setInternalValue(labeledValues);

    // Clean up if needed
    if (autoClearSearchValue) {
      setSearchValue('');
    }

    // Generate rest parameters is costly, so only do it when necessary
    if (onChange) {
      var eventValues = newRawValues;
      if (treeConduction) {
        eventValues = formattedKeyList.map(function (key) {
          var entity = valueEntities.get(key);
          return entity ? entity.node[mergedFieldNames.value] : key;
        });
      }
      var _ref = extra || {
          triggerValue: undefined,
          selected: undefined
        },
        triggerValue = _ref.triggerValue,
        selected = _ref.selected;
      var returnRawValues = eventValues;

      // We need fill half check back
      if (treeCheckStrictly) {
        var halfValues = rawHalfLabeledValues.filter(function (item) {
          return !eventValues.includes(item.value);
        });
        returnRawValues = [].concat((0,toConsumableArray/* default */.A)(returnRawValues), (0,toConsumableArray/* default */.A)(halfValues));
      }
      var returnLabeledValues = convert2LabelValues(returnRawValues);
      var additionalInfo = {
        // [Legacy] Always return as array contains label & value
        preValue: rawLabeledValues,
        triggerValue: triggerValue
      };

      // [Legacy] Fill legacy data if user query.
      // This is expansive that we only fill when user query
      // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx
      var showPosition = true;
      if (treeCheckStrictly || source === 'selection' && !selected) {
        showPosition = false;
      }
      fillAdditionalInfo(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition, mergedFieldNames);
      if (mergedCheckable) {
        additionalInfo.checked = selected;
      } else {
        additionalInfo.selected = selected;
      }
      var returnValues = mergedLabelInValue ? returnLabeledValues : returnLabeledValues.map(function (item) {
        return item.value;
      });
      onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : returnLabeledValues.map(function (item) {
        return item.label;
      }), additionalInfo);
    }
  });

  // ========================== Options ===========================
  /** Trigger by option list */
  var onOptionSelect = react.useCallback(function (selectedKey, _ref2) {
    var _node$mergedFieldName;
    var selected = _ref2.selected,
      source = _ref2.source;
    var entity = keyEntities[selectedKey];
    var node = entity === null || entity === void 0 ? void 0 : entity.node;
    var selectedValue = (_node$mergedFieldName = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value]) !== null && _node$mergedFieldName !== void 0 ? _node$mergedFieldName : selectedKey;

    // Never be falsy but keep it safe
    if (!mergedMultiple) {
      // Single mode always set value
      triggerChange([selectedValue], {
        selected: true,
        triggerValue: selectedValue
      }, 'option');
    } else {
      var newRawValues = selected ? [].concat((0,toConsumableArray/* default */.A)(rawValues), [selectedValue]) : rawCheckedValues.filter(function (v) {
        return v !== selectedValue;
      });

      // Add keys if tree conduction
      if (treeConduction) {
        // Should keep missing values
        var _splitRawValues = splitRawValues(newRawValues),
          missingRawValues = _splitRawValues.missingRawValues,
          existRawValues = _splitRawValues.existRawValues;
        var keyList = existRawValues.map(function (val) {
          return valueEntities.get(val).key;
        });

        // Conduction by selected or not
        var checkedKeys;
        if (selected) {
          var _conductCheck = (0,conductUtil/* conductCheck */.p)(keyList, true, keyEntities);
          checkedKeys = _conductCheck.checkedKeys;
        } else {
          var _conductCheck2 = (0,conductUtil/* conductCheck */.p)(keyList, {
            checked: false,
            halfCheckedKeys: rawHalfCheckedValues
          }, keyEntities);
          checkedKeys = _conductCheck2.checkedKeys;
        }

        // Fill back of keys
        newRawValues = [].concat((0,toConsumableArray/* default */.A)(missingRawValues), (0,toConsumableArray/* default */.A)(checkedKeys.map(function (key) {
          return keyEntities[key].node[mergedFieldNames.value];
        })));
      }
      triggerChange(newRawValues, {
        selected: selected,
        triggerValue: selectedValue
      }, source || 'option');
    }

    // Trigger select event
    if (selected || !mergedMultiple) {
      onSelect === null || onSelect === void 0 || onSelect(selectedValue, fillLegacyProps(node));
    } else {
      onDeselect === null || onDeselect === void 0 || onDeselect(selectedValue, fillLegacyProps(node));
    }
  }, [splitRawValues, valueEntities, keyEntities, mergedFieldNames, mergedMultiple, rawValues, triggerChange, treeConduction, onSelect, onDeselect, rawCheckedValues, rawHalfCheckedValues, maxCount]);

  // ========================== Dropdown ==========================
  var onInternalDropdownVisibleChange = react.useCallback(function (open) {
    if (onDropdownVisibleChange) {
      var legacyParam = {};
      Object.defineProperty(legacyParam, 'documentClickClose', {
        get: function get() {
          (0,es_warning/* default */.Ay)(false, 'Second param of `onDropdownVisibleChange` has been removed.');
          return false;
        }
      });
      onDropdownVisibleChange(open, legacyParam);
    }
  }, [onDropdownVisibleChange]);

  // ====================== Display Change ========================
  var onDisplayValuesChange = useRefFunc(function (newValues, info) {
    var newRawValues = newValues.map(function (item) {
      return item.value;
    });
    if (info.type === 'clear') {
      triggerChange(newRawValues, {}, 'selection');
      return;
    }

    // TreeSelect only have multiple mode which means display change only has remove
    if (info.values.length) {
      onOptionSelect(info.values[0].value, {
        selected: false,
        source: 'selection'
      });
    }
  });

  // ========================== Context ===========================
  var treeSelectContext = react.useMemo(function () {
    return {
      virtual: virtual,
      dropdownMatchSelectWidth: dropdownMatchSelectWidth,
      listHeight: listHeight,
      listItemHeight: listItemHeight,
      listItemScrollOffset: listItemScrollOffset,
      treeData: filteredTreeData,
      fieldNames: mergedFieldNames,
      onSelect: onOptionSelect,
      treeExpandAction: treeExpandAction,
      treeTitleRender: treeTitleRender,
      onPopupScroll: onPopupScroll,
      leftMaxCount: maxCount === undefined ? null : maxCount - cachedDisplayValues.length,
      leafCountOnly: mergedShowCheckedStrategy === 'SHOW_CHILD' && !treeCheckStrictly && !!treeCheckable,
      valueEntities: valueEntities
    };
  }, [virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, listItemScrollOffset, filteredTreeData, mergedFieldNames, onOptionSelect, treeExpandAction, treeTitleRender, onPopupScroll, maxCount, cachedDisplayValues.length, mergedShowCheckedStrategy, treeCheckStrictly, treeCheckable, valueEntities]);

  // ======================= Legacy Context =======================
  var legacyContext = react.useMemo(function () {
    return {
      checkable: mergedCheckable,
      loadData: loadData,
      treeLoadedKeys: treeLoadedKeys,
      onTreeLoad: onTreeLoad,
      checkedKeys: rawCheckedValues,
      halfCheckedKeys: rawHalfCheckedValues,
      treeDefaultExpandAll: treeDefaultExpandAll,
      treeExpandedKeys: treeExpandedKeys,
      treeDefaultExpandedKeys: treeDefaultExpandedKeys,
      onTreeExpand: onTreeExpand,
      treeIcon: treeIcon,
      treeMotion: treeMotion,
      showTreeIcon: showTreeIcon,
      switcherIcon: switcherIcon,
      treeLine: treeLine,
      treeNodeFilterProp: treeNodeFilterProp,
      keyEntities: keyEntities
    };
  }, [mergedCheckable, loadData, treeLoadedKeys, onTreeLoad, rawCheckedValues, rawHalfCheckedValues, treeDefaultExpandAll, treeExpandedKeys, treeDefaultExpandedKeys, onTreeExpand, treeIcon, treeMotion, showTreeIcon, switcherIcon, treeLine, treeNodeFilterProp, keyEntities]);

  // =========================== Render ===========================
  return /*#__PURE__*/react.createElement(es_TreeSelectContext.Provider, {
    value: treeSelectContext
  }, /*#__PURE__*/react.createElement(LegacyContext.Provider, {
    value: legacyContext
  }, /*#__PURE__*/react.createElement(es/* BaseSelect */.g3, (0,esm_extends/* default */.A)({
    ref: ref
  }, restProps, {
    // >>> MISC
    id: mergedId,
    prefixCls: prefixCls,
    mode: mergedMultiple ? 'multiple' : undefined
    // >>> Display Value
    ,
    displayValues: cachedDisplayValues,
    onDisplayValuesChange: onDisplayValuesChange
    // >>> Search
    ,
    searchValue: mergedSearchValue,
    onSearch: onInternalSearch
    // >>> Options
    ,
    OptionList: es_OptionList,
    emptyOptions: !mergedTreeData.length,
    onDropdownVisibleChange: onInternalDropdownVisibleChange,
    dropdownMatchSelectWidth: dropdownMatchSelectWidth
  }))));
});

// Assign name for Debug
if (false) {}
var GenericTreeSelect = TreeSelect;
GenericTreeSelect.TreeNode = es_TreeNode;
GenericTreeSelect.SHOW_ALL = SHOW_ALL;
GenericTreeSelect.SHOW_PARENT = SHOW_PARENT;
GenericTreeSelect.SHOW_CHILD = SHOW_CHILD;
/* harmony default export */ const es_TreeSelect = (GenericTreeSelect);
;// ./node_modules/rc-tree-select/es/index.js




/* harmony default export */ const rc_tree_select_es = (es_TreeSelect);

/***/ }),

/***/ 80427:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  z: () => (/* reexport */ Popup),
  A: () => (/* binding */ rc_tooltip_es)
});

// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
;// ./node_modules/rc-tooltip/es/Popup.js


function Popup(props) {
  var children = props.children,
    prefixCls = props.prefixCls,
    id = props.id,
    innerStyle = props.overlayInnerStyle,
    bodyClassName = props.bodyClassName,
    className = props.className,
    style = props.style;
  return /*#__PURE__*/react.createElement("div", {
    className: classnames_default()("".concat(prefixCls, "-content"), className),
    style: style
  }, /*#__PURE__*/react.createElement("div", {
    className: classnames_default()("".concat(prefixCls, "-inner"), bodyClassName),
    id: id,
    role: "tooltip",
    style: innerStyle
  }, typeof children === 'function' ? children() : children));
}
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(89379);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@rc-component/trigger/es/index.js + 11 modules
var es = __webpack_require__(62427);
;// ./node_modules/rc-tooltip/es/placements.js
var autoAdjustOverflowTopBottom = {
  shiftX: 64,
  adjustY: 1
};
var autoAdjustOverflowLeftRight = {
  adjustX: 1,
  shiftY: true
};
var targetOffset = [0, 0];
var placements = {
  left: {
    points: ['cr', 'cl'],
    overflow: autoAdjustOverflowLeftRight,
    offset: [-4, 0],
    targetOffset: targetOffset
  },
  right: {
    points: ['cl', 'cr'],
    overflow: autoAdjustOverflowLeftRight,
    offset: [4, 0],
    targetOffset: targetOffset
  },
  top: {
    points: ['bc', 'tc'],
    overflow: autoAdjustOverflowTopBottom,
    offset: [0, -4],
    targetOffset: targetOffset
  },
  bottom: {
    points: ['tc', 'bc'],
    overflow: autoAdjustOverflowTopBottom,
    offset: [0, 4],
    targetOffset: targetOffset
  },
  topLeft: {
    points: ['bl', 'tl'],
    overflow: autoAdjustOverflowTopBottom,
    offset: [0, -4],
    targetOffset: targetOffset
  },
  leftTop: {
    points: ['tr', 'tl'],
    overflow: autoAdjustOverflowLeftRight,
    offset: [-4, 0],
    targetOffset: targetOffset
  },
  topRight: {
    points: ['br', 'tr'],
    overflow: autoAdjustOverflowTopBottom,
    offset: [0, -4],
    targetOffset: targetOffset
  },
  rightTop: {
    points: ['tl', 'tr'],
    overflow: autoAdjustOverflowLeftRight,
    offset: [4, 0],
    targetOffset: targetOffset
  },
  bottomRight: {
    points: ['tr', 'br'],
    overflow: autoAdjustOverflowTopBottom,
    offset: [0, 4],
    targetOffset: targetOffset
  },
  rightBottom: {
    points: ['bl', 'br'],
    overflow: autoAdjustOverflowLeftRight,
    offset: [4, 0],
    targetOffset: targetOffset
  },
  bottomLeft: {
    points: ['tl', 'bl'],
    overflow: autoAdjustOverflowTopBottom,
    offset: [0, 4],
    targetOffset: targetOffset
  },
  leftBottom: {
    points: ['br', 'bl'],
    overflow: autoAdjustOverflowLeftRight,
    offset: [-4, 0],
    targetOffset: targetOffset
  }
};
/* harmony default export */ const es_placements = ((/* unused pure expression or super */ null && (placements)));
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useId.js
var useId = __webpack_require__(56855);
;// ./node_modules/rc-tooltip/es/Tooltip.js



var _excluded = ["overlayClassName", "trigger", "mouseEnterDelay", "mouseLeaveDelay", "overlayStyle", "prefixCls", "children", "onVisibleChange", "afterVisibleChange", "transitionName", "animation", "motion", "placement", "align", "destroyTooltipOnHide", "defaultVisible", "getTooltipContainer", "overlayInnerStyle", "arrowContent", "overlay", "id", "showArrow", "classNames", "styles"];







var Tooltip = function Tooltip(props, ref) {
  var overlayClassName = props.overlayClassName,
    _props$trigger = props.trigger,
    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,
    _props$mouseEnterDela = props.mouseEnterDelay,
    mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0 : _props$mouseEnterDela,
    _props$mouseLeaveDela = props.mouseLeaveDelay,
    mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,
    overlayStyle = props.overlayStyle,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-tooltip' : _props$prefixCls,
    children = props.children,
    onVisibleChange = props.onVisibleChange,
    afterVisibleChange = props.afterVisibleChange,
    transitionName = props.transitionName,
    animation = props.animation,
    motion = props.motion,
    _props$placement = props.placement,
    placement = _props$placement === void 0 ? 'right' : _props$placement,
    _props$align = props.align,
    align = _props$align === void 0 ? {} : _props$align,
    _props$destroyTooltip = props.destroyTooltipOnHide,
    destroyTooltipOnHide = _props$destroyTooltip === void 0 ? false : _props$destroyTooltip,
    defaultVisible = props.defaultVisible,
    getTooltipContainer = props.getTooltipContainer,
    overlayInnerStyle = props.overlayInnerStyle,
    arrowContent = props.arrowContent,
    overlay = props.overlay,
    id = props.id,
    _props$showArrow = props.showArrow,
    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,
    tooltipClassNames = props.classNames,
    tooltipStyles = props.styles,
    restProps = (0,objectWithoutProperties/* default */.A)(props, _excluded);
  var mergedId = (0,useId/* default */.A)(id);
  var triggerRef = (0,react.useRef)(null);
  (0,react.useImperativeHandle)(ref, function () {
    return triggerRef.current;
  });
  var extraProps = (0,objectSpread2/* default */.A)({}, restProps);
  if ('visible' in props) {
    extraProps.popupVisible = props.visible;
  }
  var getPopupElement = function getPopupElement() {
    return /*#__PURE__*/react.createElement(Popup, {
      key: "content",
      prefixCls: prefixCls,
      id: mergedId,
      bodyClassName: tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.body,
      overlayInnerStyle: (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, overlayInnerStyle), tooltipStyles === null || tooltipStyles === void 0 ? void 0 : tooltipStyles.body)
    }, overlay);
  };
  var getChildren = function getChildren() {
    var child = react.Children.only(children);
    var originalProps = (child === null || child === void 0 ? void 0 : child.props) || {};
    var childProps = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, originalProps), {}, {
      'aria-describedby': overlay ? mergedId : null
    });
    return /*#__PURE__*/react.cloneElement(children, childProps);
  };
  return /*#__PURE__*/react.createElement(es/* default */.A, (0,esm_extends/* default */.A)({
    popupClassName: classnames_default()(overlayClassName, tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.root),
    prefixCls: prefixCls,
    popup: getPopupElement,
    action: trigger,
    builtinPlacements: placements,
    popupPlacement: placement,
    ref: triggerRef,
    popupAlign: align,
    getPopupContainer: getTooltipContainer,
    onPopupVisibleChange: onVisibleChange,
    afterPopupVisibleChange: afterVisibleChange,
    popupTransitionName: transitionName,
    popupAnimation: animation,
    popupMotion: motion,
    defaultPopupVisible: defaultVisible,
    autoDestroy: destroyTooltipOnHide,
    mouseLeaveDelay: mouseLeaveDelay,
    popupStyle: (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, overlayStyle), tooltipStyles === null || tooltipStyles === void 0 ? void 0 : tooltipStyles.root),
    mouseEnterDelay: mouseEnterDelay,
    arrow: showArrow
  }, extraProps), getChildren());
};
/* harmony default export */ const es_Tooltip = (/*#__PURE__*/(0,react.forwardRef)(Tooltip));
;// ./node_modules/rc-tooltip/es/index.js



/* harmony default export */ const rc_tooltip_es = (es_Tooltip);

/***/ })

}]);