"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4309],{

/***/ 4309:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ enhanced_ComponentBuilder)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var es = __webpack_require__(35346);
// EXTERNAL MODULE: ./src/redux/minimal-store.js
var minimal_store = __webpack_require__(34816);
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
// EXTERNAL MODULE: ./src/design-system/theme.js
var theme = __webpack_require__(86020);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var antd_es = __webpack_require__(1807);
;// ./src/components/enhanced/property-editor/NumberInput.js




var _excluded = ["value", "onChange", "min", "max", "step", "unit", "units", "showSlider", "showUnit", "placeholder", "tooltip", "precision"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4;




var Text = antd_es/* Typography */.o5.Text;
var NumberInputContainer = design_system.styled.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var InputGroup = design_system.styled.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"])));
var SliderContainer = design_system.styled.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  flex: 1;\n  margin-left: 8px;\n"])));
var UnitSelector = design_system.styled.select(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 4px 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background: white;\n  font-size: 12px;\n  min-width: 50px;\n"])));

/**
 * Enhanced number input with constraints, step controls, and optional slider
 */
var NumberInput = function NumberInput(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$min = _ref.min,
    min = _ref$min === void 0 ? 0 : _ref$min,
    _ref$max = _ref.max,
    max = _ref$max === void 0 ? 100 : _ref$max,
    _ref$step = _ref.step,
    step = _ref$step === void 0 ? 1 : _ref$step,
    _ref$unit = _ref.unit,
    unit = _ref$unit === void 0 ? 'px' : _ref$unit,
    _ref$units = _ref.units,
    units = _ref$units === void 0 ? ['px', '%', 'rem', 'em', 'vh', 'vw'] : _ref$units,
    _ref$showSlider = _ref.showSlider,
    showSlider = _ref$showSlider === void 0 ? false : _ref$showSlider,
    _ref$showUnit = _ref.showUnit,
    showUnit = _ref$showUnit === void 0 ? true : _ref$showUnit,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? 'Enter value' : _ref$placeholder,
    tooltip = _ref.tooltip,
    _ref$precision = _ref.precision,
    precision = _ref$precision === void 0 ? 0 : _ref$precision,
    props = (0,objectWithoutProperties/* default */.A)(_ref, _excluded);
  var _useState = (0,react.useState)(0),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    numericValue = _useState2[0],
    setNumericValue = _useState2[1];
  var _useState3 = (0,react.useState)(unit),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    selectedUnit = _useState4[0],
    setSelectedUnit = _useState4[1];

  // Parse value on mount and when value changes
  (0,react.useEffect)(function () {
    if (value) {
      var parsed = parseValue(value);
      setNumericValue(parsed.number);
      setSelectedUnit(parsed.unit);
    }
  }, [value]);

  // Parse a value string like "10px" into number and unit
  var parseValue = function parseValue(val) {
    if (typeof val === 'number') {
      return {
        number: val,
        unit: selectedUnit
      };
    }
    if (typeof val === 'string') {
      var match = val.match(/^(-?\d*\.?\d+)(.*)$/);
      if (match) {
        return {
          number: parseFloat(match[1]),
          unit: match[2] || selectedUnit
        };
      }
    }
    return {
      number: 0,
      unit: selectedUnit
    };
  };

  // Format value for output
  var formatValue = function formatValue(num, unitStr) {
    if (showUnit && unitStr) {
      return "".concat(num).concat(unitStr);
    }
    return num;
  };

  // Handle numeric value change
  var handleNumberChange = function handleNumberChange(newValue) {
    if (newValue !== null && newValue !== undefined) {
      setNumericValue(newValue);
      var formattedValue = formatValue(newValue, selectedUnit);
      onChange === null || onChange === void 0 || onChange(formattedValue);
    }
  };

  // Handle unit change
  var handleUnitChange = function handleUnitChange(e) {
    var newUnit = e.target.value;
    setSelectedUnit(newUnit);
    var formattedValue = formatValue(numericValue, newUnit);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  return /*#__PURE__*/react.createElement(NumberInputContainer, null, /*#__PURE__*/react.createElement(InputGroup, null, /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, (0,esm_extends/* default */.A)({
    value: numericValue,
    onChange: handleNumberChange,
    min: min,
    max: max,
    step: step,
    precision: precision,
    placeholder: placeholder,
    style: {
      flex: 1
    }
  }, props)), showUnit && /*#__PURE__*/react.createElement(UnitSelector, {
    value: selectedUnit,
    onChange: handleUnitChange
  }, units.map(function (u) {
    return /*#__PURE__*/react.createElement("option", {
      key: u,
      value: u
    }, u);
  })), tooltip && /*#__PURE__*/react.createElement(antd_es/* Tooltip */.m_, {
    title: tooltip
  }, /*#__PURE__*/react.createElement(es/* InfoCircleOutlined */.rUN, {
    style: {
      color: '#8c8c8c'
    }
  }))), showSlider && /*#__PURE__*/react.createElement(SliderContainer, null, /*#__PURE__*/react.createElement(antd_es/* Slider */.Ap, {
    value: numericValue,
    onChange: handleNumberChange,
    min: min,
    max: max,
    step: step,
    tooltip: {
      formatter: function formatter(val) {
        return "".concat(val).concat(selectedUnit);
      }
    }
  })), (min !== undefined || max !== undefined) && /*#__PURE__*/react.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '12px'
    }
  }, "Range: ", min, " - ", max));
};
/* harmony default export */ const property_editor_NumberInput = (NumberInput);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
;// ./src/components/enhanced/property-editor/ColorInput.js





var ColorInput_excluded = ["value", "onChange", "showPresets", "showModeToggle", "presets", "placeholder"];
var ColorInput_templateObject, ColorInput_templateObject2, ColorInput_templateObject3, ColorInput_templateObject4, _templateObject5, _templateObject6;





var ColorInput_Text = antd_es/* Typography */.o5.Text;
var ColorInputContainer = design_system.styled.div(ColorInput_templateObject || (ColorInput_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var ColorPreview = design_system.styled.div(ColorInput_templateObject2 || (ColorInput_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 32px;\n  height: 32px;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ", ";\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg, #ccc 25%, transparent 25%), \n                linear-gradient(-45deg, #ccc 25%, transparent 25%), \n                linear-gradient(45deg, transparent 75%, #ccc 75%), \n                linear-gradient(-45deg, transparent 75%, #ccc 75%);\n    background-size: 8px 8px;\n    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;\n    z-index: -1;\n  }\n"])), function (props) {
  return props.color || '#ffffff';
});
var ColorModeToggle = design_system.styled.div(ColorInput_templateObject3 || (ColorInput_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 4px;\n  margin-bottom: 8px;\n"])));
var ModeButton = (0,design_system.styled)(antd_es/* Button */.$n)(ColorInput_templateObject4 || (ColorInput_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"])));
var PresetGrid = design_system.styled.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: grid;\n  grid-template-columns: repeat(8, 1fr);\n  gap: 4px;\n  margin-top: 8px;\n"])));
var PresetColor = design_system.styled.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 24px;\n  height: 24px;\n  border-radius: 2px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  background: ", ";\n  \n  &:hover {\n    border-color: #1890ff;\n    transform: scale(1.1);\n  }\n"])), function (props) {
  return props.color;
});

/**
 * Enhanced color picker with multiple format support and presets
 */
var ColorInput = function ColorInput(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPresets = _ref.showPresets,
    showPresets = _ref$showPresets === void 0 ? true : _ref$showPresets,
    _ref$showModeToggle = _ref.showModeToggle,
    showModeToggle = _ref$showModeToggle === void 0 ? true : _ref$showModeToggle,
    _ref$presets = _ref.presets,
    presets = _ref$presets === void 0 ? [] : _ref$presets,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? 'Enter color' : _ref$placeholder,
    props = (0,objectWithoutProperties/* default */.A)(_ref, ColorInput_excluded);
  var _useState = (0,react.useState)('hex'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    colorMode = _useState2[0],
    setColorMode = _useState2[1];
  var _useState3 = (0,react.useState)(value || '#ffffff'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    colorValue = _useState4[0],
    setColorValue = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    inputValue = _useState6[0],
    setInputValue = _useState6[1];

  // Default color presets
  var defaultPresets = ['#ffffff', '#f5f5f5', '#d9d9d9', '#bfbfbf', '#8c8c8c', '#595959', '#262626', '#000000', '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d', '#bae637', '#73d13d', '#40a9ff', '#597ef7', '#9254de', '#f759ab', '#ff85c0', '#ffc069'].concat((0,toConsumableArray/* default */.A)(theme/* default.colors */.Ay.colors.primary ? [theme/* default.colors */.Ay.colors.primary[500]] : []), (0,toConsumableArray/* default */.A)(theme/* default.colors */.Ay.colors.secondary ? [theme/* default.colors */.Ay.colors.secondary[500]] : []), (0,toConsumableArray/* default */.A)(presets));
  (0,react.useEffect)(function () {
    if (value) {
      setColorValue(value);
      setInputValue(formatColorForMode(value, colorMode));
    }
  }, [value, colorMode]);

  // Convert color to different formats
  var formatColorForMode = function formatColorForMode(color, mode) {
    if (!color) return '';
    try {
      // Simple format conversion (could be enhanced with a color library)
      switch (mode) {
        case 'hex':
          return color.startsWith('#') ? color : "#".concat(color);
        case 'rgb':
          return convertToRgb(color);
        case 'hsl':
          return convertToHsl(color);
        default:
          return color;
      }
    } catch (error) {
      return color;
    }
  };

  // Simple hex to RGB conversion
  var convertToRgb = function convertToRgb(hex) {
    if (!hex.startsWith('#')) return hex;
    var r = parseInt(hex.slice(1, 3), 16);
    var g = parseInt(hex.slice(3, 5), 16);
    var b = parseInt(hex.slice(5, 7), 16);
    return "rgb(".concat(r, ", ").concat(g, ", ").concat(b, ")");
  };

  // Simple hex to HSL conversion (simplified)
  var convertToHsl = function convertToHsl(hex) {
    if (!hex.startsWith('#')) return hex;
    // This is a simplified conversion - in a real app you'd use a color library
    return "hsl(0, 0%, 50%)"; // Placeholder
  };
  var handleColorChange = function handleColorChange(color) {
    var colorString = color.toHexString();
    setColorValue(colorString);
    setInputValue(formatColorForMode(colorString, colorMode));
    onChange === null || onChange === void 0 || onChange(colorString);
  };
  var handleInputChange = function handleInputChange(e) {
    var newValue = e.target.value;
    setInputValue(newValue);

    // Validate and update color if valid
    if (isValidColor(newValue)) {
      setColorValue(newValue);
      onChange === null || onChange === void 0 || onChange(newValue);
    }
  };
  var handlePresetClick = function handlePresetClick(color) {
    setColorValue(color);
    setInputValue(formatColorForMode(color, colorMode));
    onChange === null || onChange === void 0 || onChange(color);
  };
  var isValidColor = function isValidColor(color) {
    // Simple color validation
    var hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    var rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/;
    var hslRegex = /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/;
    return hexRegex.test(color) || rgbRegex.test(color) || hslRegex.test(color);
  };
  var colorPickerContent = /*#__PURE__*/react.createElement("div", {
    style: {
      width: 280
    }
  }, showModeToggle && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(ColorModeToggle, null, /*#__PURE__*/react.createElement(ModeButton, {
    type: colorMode === 'hex' ? 'primary' : 'default',
    size: "small",
    onClick: function onClick() {
      return setColorMode('hex');
    }
  }, "HEX"), /*#__PURE__*/react.createElement(ModeButton, {
    type: colorMode === 'rgb' ? 'primary' : 'default',
    size: "small",
    onClick: function onClick() {
      return setColorMode('rgb');
    }
  }, "RGB"), /*#__PURE__*/react.createElement(ModeButton, {
    type: colorMode === 'hsl' ? 'primary' : 'default',
    size: "small",
    onClick: function onClick() {
      return setColorMode('hsl');
    }
  }, "HSL")), /*#__PURE__*/react.createElement(antd_es/* Divider */.cG, {
    style: {
      margin: '8px 0'
    }
  })), /*#__PURE__*/react.createElement(antd_es/* ColorPicker */.sk, (0,esm_extends/* default */.A)({
    value: colorValue,
    onChange: handleColorChange,
    showText: true,
    size: "large"
  }, props)), showPresets && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(antd_es/* Divider */.cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react.createElement(ColorInput_Text, {
    strong: true,
    style: {
      fontSize: '12px'
    }
  }, "Color Presets"), /*#__PURE__*/react.createElement(PresetGrid, null, defaultPresets.slice(0, 24).map(function (preset, index) {
    return /*#__PURE__*/react.createElement(PresetColor, {
      key: index,
      color: preset,
      onClick: function onClick() {
        return handlePresetClick(preset);
      },
      title: preset
    });
  }))));
  return /*#__PURE__*/react.createElement(ColorInputContainer, null, /*#__PURE__*/react.createElement(antd_es/* Space */.$x.Compact, {
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Popover */.AM, {
    content: colorPickerContent,
    trigger: "click",
    placement: "bottomLeft"
  }, /*#__PURE__*/react.createElement(ColorPreview, {
    color: colorValue
  }, /*#__PURE__*/react.createElement(es/* BgColorsOutlined */.Ebl, {
    style: {
      color: 'rgba(0,0,0,0.3)'
    }
  }))), /*#__PURE__*/react.createElement(antd_es/* Input */.pd, {
    value: inputValue,
    onChange: handleInputChange,
    placeholder: placeholder,
    style: {
      flex: 1
    }
  })));
};
/* harmony default export */ const property_editor_ColorInput = (ColorInput);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
;// ./src/components/enhanced/property-editor/SpacingEditor.js





var SpacingEditor_excluded = ["value", "onChange", "type", "showVisual", "showPresets", "unit"];
var SpacingEditor_templateObject, SpacingEditor_templateObject2, SpacingEditor_templateObject3, SpacingEditor_templateObject4, SpacingEditor_templateObject5, SpacingEditor_templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var SpacingEditor_Text = antd_es/* Typography */.o5.Text;
var SpacingContainer = design_system.styled.div(SpacingEditor_templateObject || (SpacingEditor_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var SpacingVisual = design_system.styled.div(SpacingEditor_templateObject2 || (SpacingEditor_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  width: 120px;\n  height: 120px;\n  margin: 16px auto;\n  background: #f5f5f5;\n  border: 2px dashed #d9d9d9;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"])));
var SpacingBox = design_system.styled.div(SpacingEditor_templateObject3 || (SpacingEditor_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 60px;\n  height: 60px;\n  background: #1890ff;\n  opacity: 0.3;\n  border-radius: 4px;\n  position: relative;\n"])));
var SpacingInput = design_system.styled.div(SpacingEditor_templateObject4 || (SpacingEditor_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"])));
var TopInput = (0,design_system.styled)(SpacingInput)(SpacingEditor_templateObject5 || (SpacingEditor_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  top: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"])));
var RightInput = (0,design_system.styled)(SpacingInput)(SpacingEditor_templateObject6 || (SpacingEditor_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  right: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"])));
var BottomInput = (0,design_system.styled)(SpacingInput)(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  bottom: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"])));
var LeftInput = (0,design_system.styled)(SpacingInput)(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  left: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"])));
var ControlsRow = design_system.styled.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"])));
var QuickPresets = design_system.styled.div(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 4px;\n  margin-top: 8px;\n"])));
var PresetButton = (0,design_system.styled)(antd_es/* Button */.$n)(_templateObject1 || (_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"])));

/**
 * Visual spacing editor for margin and padding properties
 */
var SpacingEditor = function SpacingEditor(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$type = _ref.type,
    type = _ref$type === void 0 ? 'margin' : _ref$type,
    _ref$showVisual = _ref.showVisual,
    showVisual = _ref$showVisual === void 0 ? true : _ref$showVisual,
    _ref$showPresets = _ref.showPresets,
    showPresets = _ref$showPresets === void 0 ? true : _ref$showPresets,
    _ref$unit = _ref.unit,
    unit = _ref$unit === void 0 ? 'px' : _ref$unit,
    props = (0,objectWithoutProperties/* default */.A)(_ref, SpacingEditor_excluded);
  var _useState = (0,react.useState)({
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    }),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    values = _useState2[0],
    setValues = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    isLinked = _useState4[0],
    setIsLinked = _useState4[1];

  // Parse spacing value on mount and when value changes
  (0,react.useEffect)(function () {
    if (value) {
      var parsed = parseSpacingValue(value);
      setValues(parsed);
    }
  }, [value]);

  // Parse spacing value like "10px 20px" or "10px 20px 30px 40px"
  var parseSpacingValue = function parseSpacingValue(val) {
    if (!val) return {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    };
    if ((0,esm_typeof/* default */.A)(val) === 'object') {
      return {
        top: parseFloat(val.top) || 0,
        right: parseFloat(val.right) || 0,
        bottom: parseFloat(val.bottom) || 0,
        left: parseFloat(val.left) || 0
      };
    }
    var parts = val.toString().split(/\s+/).map(function (p) {
      return parseFloat(p.replace(/[^\d.-]/g, '')) || 0;
    });
    switch (parts.length) {
      case 1:
        return {
          top: parts[0],
          right: parts[0],
          bottom: parts[0],
          left: parts[0]
        };
      case 2:
        return {
          top: parts[0],
          right: parts[1],
          bottom: parts[0],
          left: parts[1]
        };
      case 3:
        return {
          top: parts[0],
          right: parts[1],
          bottom: parts[2],
          left: parts[1]
        };
      case 4:
        return {
          top: parts[0],
          right: parts[1],
          bottom: parts[2],
          left: parts[3]
        };
      default:
        return {
          top: 0,
          right: 0,
          bottom: 0,
          left: 0
        };
    }
  };

  // Format values for output
  var formatSpacingValue = function formatSpacingValue(vals) {
    var top = vals.top,
      right = vals.right,
      bottom = vals.bottom,
      left = vals.left;

    // If all values are the same, return single value
    if (top === right && right === bottom && bottom === left) {
      return "".concat(top).concat(unit);
    }

    // If top/bottom and left/right are the same
    if (top === bottom && left === right) {
      return "".concat(top).concat(unit, " ").concat(right).concat(unit);
    }

    // Return all four values
    return "".concat(top).concat(unit, " ").concat(right).concat(unit, " ").concat(bottom).concat(unit, " ").concat(left).concat(unit);
  };

  // Handle individual value change
  var handleValueChange = function handleValueChange(side, newValue) {
    var newValues = _objectSpread({}, values);
    if (isLinked) {
      // Update all sides when linked
      newValues.top = newValue;
      newValues.right = newValue;
      newValues.bottom = newValue;
      newValues.left = newValue;
    } else {
      // Update only the specific side
      newValues[side] = newValue;
    }
    setValues(newValues);
    onChange === null || onChange === void 0 || onChange(formatSpacingValue(newValues));
  };

  // Handle preset click
  var handlePresetClick = function handlePresetClick(preset) {
    var newValues = parseSpacingValue(preset);
    setValues(newValues);
    onChange === null || onChange === void 0 || onChange(formatSpacingValue(newValues));
  };

  // Toggle linked state
  var toggleLinked = function toggleLinked() {
    setIsLinked(!isLinked);
  };
  var presets = ['0px', '4px', '8px', '12px', '16px', '24px', '32px', '8px 16px', '16px 24px'];
  return /*#__PURE__*/react.createElement(SpacingContainer, null, /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(SpacingEditor_Text, {
    strong: true,
    style: {
      fontSize: '12px'
    }
  }, type === 'margin' ? 'Margin' : 'Padding'), /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    type: isLinked ? 'primary' : 'default',
    size: "small",
    icon: isLinked ? /*#__PURE__*/react.createElement(es/* LinkOutlined */.t7c, null) : /*#__PURE__*/react.createElement(es/* UnlockOutlined */.Rrh, null),
    onClick: toggleLinked,
    title: isLinked ? 'Unlink sides' : 'Link all sides'
  })), showVisual && /*#__PURE__*/react.createElement(SpacingVisual, null, /*#__PURE__*/react.createElement(TopInput, null, /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    size: "small",
    value: values.top,
    onChange: function onChange(val) {
      return handleValueChange('top', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(RightInput, null, /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    size: "small",
    value: values.right,
    onChange: function onChange(val) {
      return handleValueChange('right', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(BottomInput, null, /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    size: "small",
    value: values.bottom,
    onChange: function onChange(val) {
      return handleValueChange('bottom', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(LeftInput, null, /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    size: "small",
    value: values.left,
    onChange: function onChange(val) {
      return handleValueChange('left', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(SpacingBox, null)), /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(SpacingEditor_Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Top:"), /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    value: values.top,
    onChange: function onChange(val) {
      return handleValueChange('top', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  })), /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(SpacingEditor_Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Right:"), /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    value: values.right,
    onChange: function onChange(val) {
      return handleValueChange('right', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  })), /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(SpacingEditor_Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Bottom:"), /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    value: values.bottom,
    onChange: function onChange(val) {
      return handleValueChange('bottom', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  })), /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(SpacingEditor_Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Left:"), /*#__PURE__*/react.createElement(antd_es/* InputNumber */.YI, {
    value: values.left,
    onChange: function onChange(val) {
      return handleValueChange('left', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  }))), showPresets && /*#__PURE__*/react.createElement(QuickPresets, null, /*#__PURE__*/react.createElement(SpacingEditor_Text, {
    style: {
      fontSize: '12px',
      marginRight: '8px'
    }
  }, "Presets:"), presets.map(function (preset, index) {
    return /*#__PURE__*/react.createElement(PresetButton, {
      key: index,
      onClick: function onClick() {
        return handlePresetClick(preset);
      },
      title: "Apply ".concat(preset)
    }, preset);
  })));
};
/* harmony default export */ const property_editor_SpacingEditor = (SpacingEditor);
;// ./src/components/enhanced/property-editor/BorderEditor.js




var BorderEditor_excluded = ["value", "onChange", "showPreview"];
var BorderEditor_templateObject, BorderEditor_templateObject2, BorderEditor_templateObject3, BorderEditor_templateObject4;






var BorderEditor_Text = antd_es/* Typography */.o5.Text;
var Option = antd_es/* Select */.l6.Option;
var BorderContainer = design_system.styled.div(BorderEditor_templateObject || (BorderEditor_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var BorderPreview = design_system.styled.div(BorderEditor_templateObject2 || (BorderEditor_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n  height: 60px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n  border: ", ";\n"])), function (props) {
  return props.borderStyle || '1px solid #d9d9d9';
});
var PropertyRow = design_system.styled.div(BorderEditor_templateObject3 || (BorderEditor_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"])));
var PropertyLabel = (0,design_system.styled)(BorderEditor_Text)(BorderEditor_templateObject4 || (BorderEditor_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  min-width: 60px;\n  font-size: 12px;\n  font-weight: 500;\n"])));

/**
 * Visual border editor with style, width, and color controls
 */
var BorderEditor = function BorderEditor(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    props = (0,objectWithoutProperties/* default */.A)(_ref, BorderEditor_excluded);
  var _useState = (0,react.useState)('solid'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    borderStyle = _useState2[0],
    setBorderStyle = _useState2[1];
  var _useState3 = (0,react.useState)('1px'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    borderWidth = _useState4[0],
    setBorderWidth = _useState4[1];
  var _useState5 = (0,react.useState)('#d9d9d9'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    borderColor = _useState6[0],
    setBorderColor = _useState6[1];

  // Parse border value on mount and when value changes
  (0,react.useEffect)(function () {
    if (value) {
      var parsed = parseBorderValue(value);
      setBorderStyle(parsed.style);
      setBorderWidth(parsed.width);
      setBorderColor(parsed.color);
    }
  }, [value]);

  // Parse border value like "1px solid #000" or object
  var parseBorderValue = function parseBorderValue(val) {
    if (!val) return {
      style: 'solid',
      width: '1px',
      color: '#d9d9d9'
    };
    if ((0,esm_typeof/* default */.A)(val) === 'object') {
      return {
        style: val.style || 'solid',
        width: val.width || '1px',
        color: val.color || '#d9d9d9'
      };
    }
    if (typeof val === 'string') {
      // Parse string like "1px solid #000"
      var parts = val.split(/\s+/);
      var width = '1px';
      var style = 'solid';
      var color = '#d9d9d9';
      parts.forEach(function (part) {
        if (part.match(/^\d+(\.\d+)?(px|em|rem|%)$/)) {
          width = part;
        } else if (['none', 'solid', 'dashed', 'dotted', 'double', 'groove', 'ridge', 'inset', 'outset'].includes(part)) {
          style = part;
        } else if (part.startsWith('#') || part.startsWith('rgb') || part.startsWith('hsl') || isNamedColor(part)) {
          color = part;
        }
      });
      return {
        style: style,
        width: width,
        color: color
      };
    }
    return {
      style: 'solid',
      width: '1px',
      color: '#d9d9d9'
    };
  };

  // Check if a string is a named color
  var isNamedColor = function isNamedColor(color) {
    var namedColors = ['black', 'white', 'red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink', 'brown', 'gray', 'grey', 'transparent'];
    return namedColors.includes(color.toLowerCase());
  };

  // Format border value for output
  var formatBorderValue = function formatBorderValue(style, width, color) {
    if (style === 'none') {
      return 'none';
    }
    return "".concat(width, " ").concat(style, " ").concat(color);
  };

  // Handle style change
  var handleStyleChange = function handleStyleChange(newStyle) {
    setBorderStyle(newStyle);
    var formattedValue = formatBorderValue(newStyle, borderWidth, borderColor);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };

  // Handle width change
  var handleWidthChange = function handleWidthChange(newWidth) {
    setBorderWidth(newWidth);
    var formattedValue = formatBorderValue(borderStyle, newWidth, borderColor);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };

  // Handle color change
  var handleColorChange = function handleColorChange(newColor) {
    setBorderColor(newColor);
    var formattedValue = formatBorderValue(borderStyle, borderWidth, newColor);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  var borderStyles = [{
    value: 'none',
    label: 'None'
  }, {
    value: 'solid',
    label: 'Solid'
  }, {
    value: 'dashed',
    label: 'Dashed'
  }, {
    value: 'dotted',
    label: 'Dotted'
  }, {
    value: 'double',
    label: 'Double'
  }, {
    value: 'groove',
    label: 'Groove'
  }, {
    value: 'ridge',
    label: 'Ridge'
  }, {
    value: 'inset',
    label: 'Inset'
  }, {
    value: 'outset',
    label: 'Outset'
  }];
  var currentBorderStyle = formatBorderValue(borderStyle, borderWidth, borderColor);
  return /*#__PURE__*/react.createElement(BorderContainer, null, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Style:"), /*#__PURE__*/react.createElement(antd_es/* Select */.l6, {
    value: borderStyle,
    onChange: handleStyleChange,
    style: {
      flex: 1
    },
    size: "small"
  }, borderStyles.map(function (style) {
    return /*#__PURE__*/react.createElement(Option, {
      key: style.value,
      value: style.value
    }, style.label);
  }))), borderStyle !== 'none' && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Width:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_NumberInput, {
    value: borderWidth,
    onChange: handleWidthChange,
    min: 0,
    max: 20,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Color:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_ColorInput, {
    value: borderColor,
    onChange: handleColorChange,
    placeholder: "Border color"
  })))), showPreview && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(antd_es/* Divider */.cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react.createElement(BorderEditor_Text, {
    style: {
      fontSize: '12px',
      marginBottom: '4px'
    }
  }, "Preview:"), /*#__PURE__*/react.createElement(BorderPreview, {
    borderStyle: currentBorderStyle
  }, /*#__PURE__*/react.createElement(es/* BorderOutlined */.bnM, {
    style: {
      fontSize: '24px',
      color: '#8c8c8c'
    }
  })), /*#__PURE__*/react.createElement(BorderEditor_Text, {
    type: "secondary",
    style: {
      fontSize: '11px',
      textAlign: 'center'
    }
  }, currentBorderStyle))));
};
/* harmony default export */ const property_editor_BorderEditor = (BorderEditor);
;// ./src/components/enhanced/property-editor/ShadowEditor.js




var ShadowEditor_excluded = ["value", "onChange", "showPreview"];
var ShadowEditor_templateObject, ShadowEditor_templateObject2, ShadowEditor_templateObject3, ShadowEditor_templateObject4, ShadowEditor_templateObject5;





var ShadowEditor_Text = antd_es/* Typography */.o5.Text;
var ShadowContainer = design_system.styled.div(ShadowEditor_templateObject || (ShadowEditor_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var ShadowPreview = design_system.styled.div(ShadowEditor_templateObject2 || (ShadowEditor_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n  height: 80px;\n  margin: 12px 0;\n  background: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  box-shadow: ", ";\n  border: 1px solid #f0f0f0;\n"])), function (props) {
  return props.shadowStyle || 'none';
});
var ShadowEditor_PropertyRow = design_system.styled.div(ShadowEditor_templateObject3 || (ShadowEditor_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"])));
var ShadowEditor_PropertyLabel = (0,design_system.styled)(ShadowEditor_Text)(ShadowEditor_templateObject4 || (ShadowEditor_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"])));
var PreviewBox = design_system.styled.div(ShadowEditor_templateObject5 || (ShadowEditor_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 60px;\n  height: 40px;\n  background: #1890ff;\n  border-radius: 4px;\n  opacity: 0.8;\n"])));

/**
 * Visual shadow editor with offset, blur, spread, and color controls
 */
var ShadowEditor = function ShadowEditor(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    props = (0,objectWithoutProperties/* default */.A)(_ref, ShadowEditor_excluded);
  var _useState = (0,react.useState)('0px'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    offsetX = _useState2[0],
    setOffsetX = _useState2[1];
  var _useState3 = (0,react.useState)('2px'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    offsetY = _useState4[0],
    setOffsetY = _useState4[1];
  var _useState5 = (0,react.useState)('4px'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    blurRadius = _useState6[0],
    setBlurRadius = _useState6[1];
  var _useState7 = (0,react.useState)('0px'),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    spreadRadius = _useState8[0],
    setSpreadRadius = _useState8[1];
  var _useState9 = (0,react.useState)('rgba(0, 0, 0, 0.1)'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    shadowColor = _useState0[0],
    setShadowColor = _useState0[1];
  var _useState1 = (0,react.useState)(false),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    inset = _useState10[0],
    setInset = _useState10[1];

  // Parse shadow value on mount and when value changes
  (0,react.useEffect)(function () {
    if (value) {
      var parsed = parseShadowValue(value);
      setOffsetX(parsed.offsetX);
      setOffsetY(parsed.offsetY);
      setBlurRadius(parsed.blurRadius);
      setSpreadRadius(parsed.spreadRadius);
      setShadowColor(parsed.color);
      setInset(parsed.inset);
    }
  }, [value]);

  // Parse shadow value like "2px 4px 8px rgba(0,0,0,0.1)" or object
  var parseShadowValue = function parseShadowValue(val) {
    if (!val || val === 'none') {
      return {
        offsetX: '0px',
        offsetY: '2px',
        blurRadius: '4px',
        spreadRadius: '0px',
        color: 'rgba(0, 0, 0, 0.1)',
        inset: false
      };
    }
    if ((0,esm_typeof/* default */.A)(val) === 'object') {
      return {
        offsetX: val.offsetX || '0px',
        offsetY: val.offsetY || '2px',
        blurRadius: val.blurRadius || '4px',
        spreadRadius: val.spreadRadius || '0px',
        color: val.color || 'rgba(0, 0, 0, 0.1)',
        inset: val.inset || false
      };
    }
    if (typeof val === 'string') {
      // Parse string like "inset 2px 4px 8px 2px rgba(0,0,0,0.1)"
      var shadowString = val.trim();
      var isInset = false;
      if (shadowString.startsWith('inset ')) {
        isInset = true;
        shadowString = shadowString.replace('inset ', '');
      }

      // Extract color (rgba, rgb, hex, or named color)
      var color = 'rgba(0, 0, 0, 0.1)';
      var colorMatch = shadowString.match(/(rgba?\([^)]+\)|hsla?\([^)]+\)|#[a-fA-F0-9]{3,8}|\b\w+\b)$/);
      if (colorMatch) {
        color = colorMatch[1];
        shadowString = shadowString.replace(colorMatch[1], '').trim();
      }

      // Parse remaining values (offsetX offsetY blurRadius spreadRadius)
      var values = shadowString.split(/\s+/).filter(function (v) {
        return v;
      });
      return {
        offsetX: values[0] || '0px',
        offsetY: values[1] || '2px',
        blurRadius: values[2] || '4px',
        spreadRadius: values[3] || '0px',
        color: color,
        inset: isInset
      };
    }
    return {
      offsetX: '0px',
      offsetY: '2px',
      blurRadius: '4px',
      spreadRadius: '0px',
      color: 'rgba(0, 0, 0, 0.1)',
      inset: false
    };
  };

  // Format shadow value for output
  var formatShadowValue = function formatShadowValue(x, y, blur, spread, color, isInset) {
    var parts = [x, y, blur, spread, color];
    var shadowValue = parts.join(' ');
    return isInset ? "inset ".concat(shadowValue) : shadowValue;
  };

  // Handle value changes
  var handleValueChange = function handleValueChange(property, newValue) {
    var newOffsetX = offsetX;
    var newOffsetY = offsetY;
    var newBlurRadius = blurRadius;
    var newSpreadRadius = spreadRadius;
    var newShadowColor = shadowColor;
    var newInset = inset;
    switch (property) {
      case 'offsetX':
        newOffsetX = newValue;
        setOffsetX(newValue);
        break;
      case 'offsetY':
        newOffsetY = newValue;
        setOffsetY(newValue);
        break;
      case 'blurRadius':
        newBlurRadius = newValue;
        setBlurRadius(newValue);
        break;
      case 'spreadRadius':
        newSpreadRadius = newValue;
        setSpreadRadius(newValue);
        break;
      case 'color':
        newShadowColor = newValue;
        setShadowColor(newValue);
        break;
      case 'inset':
        newInset = newValue;
        setInset(newValue);
        break;
    }
    var formattedValue = formatShadowValue(newOffsetX, newOffsetY, newBlurRadius, newSpreadRadius, newShadowColor, newInset);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  var currentShadowStyle = formatShadowValue(offsetX, offsetY, blurRadius, spreadRadius, shadowColor, inset);
  return /*#__PURE__*/react.createElement(ShadowContainer, null, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(ShadowEditor_PropertyRow, null, /*#__PURE__*/react.createElement(ShadowEditor_PropertyLabel, null, "Inset:"), /*#__PURE__*/react.createElement(antd_es/* Switch */.dO, {
    checked: inset,
    onChange: function onChange(checked) {
      return handleValueChange('inset', checked);
    },
    size: "small"
  })), /*#__PURE__*/react.createElement(ShadowEditor_PropertyRow, null, /*#__PURE__*/react.createElement(ShadowEditor_PropertyLabel, null, "Offset X:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_NumberInput, {
    value: offsetX,
    onChange: function onChange(val) {
      return handleValueChange('offsetX', val);
    },
    min: -50,
    max: 50,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(ShadowEditor_PropertyRow, null, /*#__PURE__*/react.createElement(ShadowEditor_PropertyLabel, null, "Offset Y:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_NumberInput, {
    value: offsetY,
    onChange: function onChange(val) {
      return handleValueChange('offsetY', val);
    },
    min: -50,
    max: 50,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(ShadowEditor_PropertyRow, null, /*#__PURE__*/react.createElement(ShadowEditor_PropertyLabel, null, "Blur:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_NumberInput, {
    value: blurRadius,
    onChange: function onChange(val) {
      return handleValueChange('blurRadius', val);
    },
    min: 0,
    max: 100,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(ShadowEditor_PropertyRow, null, /*#__PURE__*/react.createElement(ShadowEditor_PropertyLabel, null, "Spread:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_NumberInput, {
    value: spreadRadius,
    onChange: function onChange(val) {
      return handleValueChange('spreadRadius', val);
    },
    min: -50,
    max: 50,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(ShadowEditor_PropertyRow, null, /*#__PURE__*/react.createElement(ShadowEditor_PropertyLabel, null, "Color:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_ColorInput, {
    value: shadowColor,
    onChange: function onChange(val) {
      return handleValueChange('color', val);
    },
    placeholder: "Shadow color"
  }))), showPreview && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(antd_es/* Divider */.cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react.createElement(ShadowEditor_Text, {
    style: {
      fontSize: '12px',
      marginBottom: '4px'
    }
  }, "Preview:"), /*#__PURE__*/react.createElement(ShadowPreview, {
    shadowStyle: currentShadowStyle
  }, /*#__PURE__*/react.createElement(PreviewBox, null)), /*#__PURE__*/react.createElement(ShadowEditor_Text, {
    type: "secondary",
    style: {
      fontSize: '11px',
      textAlign: 'center'
    }
  }, currentShadowStyle))));
};
/* harmony default export */ const property_editor_ShadowEditor = (ShadowEditor);
;// ./src/components/enhanced/property-editor/FontSelector.js




var FontSelector_excluded = ["value", "onChange", "showPreview"];
var FontSelector_templateObject, FontSelector_templateObject2, FontSelector_templateObject3, FontSelector_templateObject4;





var FontSelector_Text = antd_es/* Typography */.o5.Text;
var FontSelector_Option = antd_es/* Select */.l6.Option;
var FontContainer = design_system.styled.div(FontSelector_templateObject || (FontSelector_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var FontPreview = design_system.styled.div(FontSelector_templateObject2 || (FontSelector_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n  padding: 16px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  font-family: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  line-height: ", ";\n  text-align: center;\n"])), function (props) {
  return props.fontFamily || 'inherit';
}, function (props) {
  return props.fontSize || '16px';
}, function (props) {
  return props.fontWeight || 'normal';
}, function (props) {
  return props.lineHeight || '1.5';
});
var FontSelector_PropertyRow = design_system.styled.div(FontSelector_templateObject3 || (FontSelector_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"])));
var FontSelector_PropertyLabel = (0,design_system.styled)(FontSelector_Text)(FontSelector_templateObject4 || (FontSelector_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"])));

/**
 * Font selector with family, size, weight, and line height controls
 */
var FontSelector = function FontSelector(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    props = (0,objectWithoutProperties/* default */.A)(_ref, FontSelector_excluded);
  var _useState = (0,react.useState)('inherit'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    fontFamily = _useState2[0],
    setFontFamily = _useState2[1];
  var _useState3 = (0,react.useState)('16px'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    fontSize = _useState4[0],
    setFontSize = _useState4[1];
  var _useState5 = (0,react.useState)('normal'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    fontWeight = _useState6[0],
    setFontWeight = _useState6[1];
  var _useState7 = (0,react.useState)('1.5'),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    lineHeight = _useState8[0],
    setLineHeight = _useState8[1];

  // Parse font value on mount and when value changes
  (0,react.useEffect)(function () {
    if (value) {
      var parsed = parseFontValue(value);
      setFontFamily(parsed.family);
      setFontSize(parsed.size);
      setFontWeight(parsed.weight);
      setLineHeight(parsed.lineHeight);
    }
  }, [value]);

  // Parse font value (object or individual properties)
  var parseFontValue = function parseFontValue(val) {
    if (!val) {
      return {
        family: 'inherit',
        size: '16px',
        weight: 'normal',
        lineHeight: '1.5'
      };
    }
    if ((0,esm_typeof/* default */.A)(val) === 'object') {
      return {
        family: val.fontFamily || val.family || 'inherit',
        size: val.fontSize || val.size || '16px',
        weight: val.fontWeight || val.weight || 'normal',
        lineHeight: val.lineHeight || '1.5'
      };
    }

    // If it's a string, assume it's just the font family
    return {
      family: val,
      size: fontSize,
      weight: fontWeight,
      lineHeight: lineHeight
    };
  };

  // Format font value for output
  var formatFontValue = function formatFontValue(family, size, weight, height) {
    return {
      fontFamily: family,
      fontSize: size,
      fontWeight: weight,
      lineHeight: height
    };
  };

  // Handle value changes
  var handleValueChange = function handleValueChange(property, newValue) {
    var newFamily = fontFamily;
    var newSize = fontSize;
    var newWeight = fontWeight;
    var newLineHeight = lineHeight;
    switch (property) {
      case 'family':
        newFamily = newValue;
        setFontFamily(newValue);
        break;
      case 'size':
        newSize = newValue;
        setFontSize(newValue);
        break;
      case 'weight':
        newWeight = newValue;
        setFontWeight(newValue);
        break;
      case 'lineHeight':
        newLineHeight = newValue;
        setLineHeight(newValue);
        break;
    }
    var formattedValue = formatFontValue(newFamily, newSize, newWeight, newLineHeight);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  var fontFamilies = [{
    value: 'inherit',
    label: 'Inherit'
  }, {
    value: 'Arial, sans-serif',
    label: 'Arial'
  }, {
    value: 'Helvetica, Arial, sans-serif',
    label: 'Helvetica'
  }, {
    value: '"Times New Roman", Times, serif',
    label: 'Times New Roman'
  }, {
    value: 'Georgia, serif',
    label: 'Georgia'
  }, {
    value: '"Courier New", Courier, monospace',
    label: 'Courier New'
  }, {
    value: 'Verdana, Geneva, sans-serif',
    label: 'Verdana'
  }, {
    value: '"Trebuchet MS", Helvetica, sans-serif',
    label: 'Trebuchet MS'
  }, {
    value: '"Lucida Sans Unicode", "Lucida Grande", sans-serif',
    label: 'Lucida Sans'
  }, {
    value: 'Impact, Charcoal, sans-serif',
    label: 'Impact'
  }, {
    value: '"Comic Sans MS", cursive',
    label: 'Comic Sans MS'
  }, {
    value: '"Palatino Linotype", "Book Antiqua", Palatino, serif',
    label: 'Palatino'
  }, {
    value: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif',
    label: 'Inter'
  }, {
    value: '"Roboto", sans-serif',
    label: 'Roboto'
  }, {
    value: '"Open Sans", sans-serif',
    label: 'Open Sans'
  }, {
    value: '"Lato", sans-serif',
    label: 'Lato'
  }, {
    value: '"Montserrat", sans-serif',
    label: 'Montserrat'
  }, {
    value: '"Source Sans Pro", sans-serif',
    label: 'Source Sans Pro'
  }];
  var fontWeights = [{
    value: '100',
    label: 'Thin (100)'
  }, {
    value: '200',
    label: 'Extra Light (200)'
  }, {
    value: '300',
    label: 'Light (300)'
  }, {
    value: 'normal',
    label: 'Normal (400)'
  }, {
    value: '500',
    label: 'Medium (500)'
  }, {
    value: '600',
    label: 'Semi Bold (600)'
  }, {
    value: 'bold',
    label: 'Bold (700)'
  }, {
    value: '800',
    label: 'Extra Bold (800)'
  }, {
    value: '900',
    label: 'Black (900)'
  }];
  return /*#__PURE__*/react.createElement(FontContainer, null, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(FontSelector_PropertyRow, null, /*#__PURE__*/react.createElement(FontSelector_PropertyLabel, null, "Family:"), /*#__PURE__*/react.createElement(antd_es/* Select */.l6, {
    value: fontFamily,
    onChange: function onChange(val) {
      return handleValueChange('family', val);
    },
    style: {
      flex: 1
    },
    size: "small",
    showSearch: true,
    placeholder: "Select font family"
  }, fontFamilies.map(function (font) {
    return /*#__PURE__*/react.createElement(FontSelector_Option, {
      key: font.value,
      value: font.value
    }, /*#__PURE__*/react.createElement("span", {
      style: {
        fontFamily: font.value
      }
    }, font.label));
  }))), /*#__PURE__*/react.createElement(FontSelector_PropertyRow, null, /*#__PURE__*/react.createElement(FontSelector_PropertyLabel, null, "Size:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_NumberInput, {
    value: fontSize,
    onChange: function onChange(val) {
      return handleValueChange('size', val);
    },
    min: 8,
    max: 72,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem', '%'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(FontSelector_PropertyRow, null, /*#__PURE__*/react.createElement(FontSelector_PropertyLabel, null, "Weight:"), /*#__PURE__*/react.createElement(antd_es/* Select */.l6, {
    value: fontWeight,
    onChange: function onChange(val) {
      return handleValueChange('weight', val);
    },
    style: {
      flex: 1
    },
    size: "small"
  }, fontWeights.map(function (weight) {
    return /*#__PURE__*/react.createElement(FontSelector_Option, {
      key: weight.value,
      value: weight.value
    }, weight.label);
  }))), /*#__PURE__*/react.createElement(FontSelector_PropertyRow, null, /*#__PURE__*/react.createElement(FontSelector_PropertyLabel, null, "Line Height:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(property_editor_NumberInput, {
    value: lineHeight,
    onChange: function onChange(val) {
      return handleValueChange('lineHeight', val);
    },
    min: 0.5,
    max: 3,
    step: 0.1,
    precision: 1,
    showUnit: false,
    size: "small",
    tooltip: "Line height as a multiplier (e.g., 1.5 = 150%)"
  }))), showPreview && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(antd_es/* Divider */.cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react.createElement(FontSelector_Text, {
    style: {
      fontSize: '12px',
      marginBottom: '4px'
    }
  }, "Preview:"), /*#__PURE__*/react.createElement(FontPreview, {
    fontFamily: fontFamily,
    fontSize: fontSize,
    fontWeight: fontWeight,
    lineHeight: lineHeight
  }, /*#__PURE__*/react.createElement(es/* FontSizeOutlined */.ld1, {
    style: {
      marginRight: '8px'
    }
  }), "The quick brown fox jumps over the lazy dog"), /*#__PURE__*/react.createElement(FontSelector_Text, {
    type: "secondary",
    style: {
      fontSize: '11px',
      textAlign: 'center'
    }
  }, fontFamily, " \u2022 ", fontSize, " \u2022 ", fontWeight, " \u2022 ", lineHeight))));
};
/* harmony default export */ const property_editor_FontSelector = (FontSelector);
;// ./src/components/enhanced/property-editor/PropertyTypeDetector.js


/**
 * Property Type Detection System
 * Automatically detects property types and provides metadata for rendering appropriate UI controls
 */

// Property type definitions
var PropertyTypes = {
  TEXT: 'text',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  COLOR: 'color',
  SELECT: 'select',
  SPACING: 'spacing',
  BORDER: 'border',
  SHADOW: 'shadow',
  FONT: 'font',
  ARRAY: 'array',
  OBJECT: 'object',
  JSON: 'json'
};

// Component property schemas
var ComponentSchemas = {
  button: {
    text: {
      type: PropertyTypes.TEXT,
      label: 'Button Text',
      placeholder: 'Enter button text'
    },
    variant: {
      type: PropertyTypes.SELECT,
      label: 'Variant',
      options: [{
        value: 'primary',
        label: 'Primary'
      }, {
        value: 'secondary',
        label: 'Secondary'
      }, {
        value: 'text',
        label: 'Text'
      }, {
        value: 'link',
        label: 'Link'
      }, {
        value: 'ghost',
        label: 'Ghost'
      }, {
        value: 'dashed',
        label: 'Dashed'
      }]
    },
    size: {
      type: PropertyTypes.SELECT,
      label: 'Size',
      options: [{
        value: 'small',
        label: 'Small'
      }, {
        value: 'medium',
        label: 'Medium'
      }, {
        value: 'large',
        label: 'Large'
      }]
    },
    disabled: {
      type: PropertyTypes.BOOLEAN,
      label: 'Disabled'
    },
    block: {
      type: PropertyTypes.BOOLEAN,
      label: 'Full Width'
    },
    onClick: {
      type: PropertyTypes.TEXT,
      label: 'onClick Handler',
      placeholder: 'Enter function name'
    }
  },
  text: {
    content: {
      type: PropertyTypes.TEXT,
      label: 'Text Content',
      multiline: true,
      placeholder: 'Enter text content'
    },
    variant: {
      type: PropertyTypes.SELECT,
      label: 'Variant',
      options: [{
        value: 'h1',
        label: 'Heading 1'
      }, {
        value: 'h2',
        label: 'Heading 2'
      }, {
        value: 'h3',
        label: 'Heading 3'
      }, {
        value: 'h4',
        label: 'Heading 4'
      }, {
        value: 'h5',
        label: 'Heading 5'
      }, {
        value: 'h6',
        label: 'Heading 6'
      }, {
        value: 'p',
        label: 'Paragraph'
      }, {
        value: 'span',
        label: 'Span'
      }]
    },
    color: {
      type: PropertyTypes.COLOR,
      label: 'Text Color'
    },
    align: {
      type: PropertyTypes.SELECT,
      label: 'Text Alignment',
      options: [{
        value: 'left',
        label: 'Left'
      }, {
        value: 'center',
        label: 'Center'
      }, {
        value: 'right',
        label: 'Right'
      }, {
        value: 'justify',
        label: 'Justify'
      }]
    }
  },
  input: {
    label: {
      type: PropertyTypes.TEXT,
      label: 'Input Label',
      placeholder: 'Enter input label'
    },
    placeholder: {
      type: PropertyTypes.TEXT,
      label: 'Placeholder',
      placeholder: 'Enter placeholder text'
    },
    type: {
      type: PropertyTypes.SELECT,
      label: 'Input Type',
      options: [{
        value: 'text',
        label: 'Text'
      }, {
        value: 'password',
        label: 'Password'
      }, {
        value: 'email',
        label: 'Email'
      }, {
        value: 'number',
        label: 'Number'
      }, {
        value: 'tel',
        label: 'Telephone'
      }, {
        value: 'url',
        label: 'URL'
      }]
    },
    required: {
      type: PropertyTypes.BOOLEAN,
      label: 'Required'
    },
    disabled: {
      type: PropertyTypes.BOOLEAN,
      label: 'Disabled'
    },
    validation: {
      type: PropertyTypes.SELECT,
      label: 'Validation',
      options: [{
        value: 'none',
        label: 'None'
      }, {
        value: 'email',
        label: 'Email'
      }, {
        value: 'url',
        label: 'URL'
      }, {
        value: 'phone',
        label: 'Phone'
      }, {
        value: 'custom',
        label: 'Custom'
      }]
    }
  },
  card: {
    title: {
      type: PropertyTypes.TEXT,
      label: 'Card Title',
      placeholder: 'Enter card title'
    },
    description: {
      type: PropertyTypes.TEXT,
      label: 'Description',
      multiline: true,
      placeholder: 'Enter card description'
    },
    image: {
      type: PropertyTypes.TEXT,
      label: 'Image URL',
      placeholder: 'Enter image URL'
    },
    elevation: {
      type: PropertyTypes.SELECT,
      label: 'Elevation',
      options: [{
        value: 'none',
        label: 'None'
      }, {
        value: 'sm',
        label: 'Small'
      }, {
        value: 'md',
        label: 'Medium'
      }, {
        value: 'lg',
        label: 'Large'
      }]
    },
    bordered: {
      type: PropertyTypes.BOOLEAN,
      label: 'Bordered'
    }
  }
};

// Style property schemas (common across all components)
var StyleSchemas = {
  // Dimensions
  width: {
    type: PropertyTypes.TEXT,
    label: 'Width',
    placeholder: 'e.g., 100%, 200px',
    group: 'dimensions'
  },
  height: {
    type: PropertyTypes.TEXT,
    label: 'Height',
    placeholder: 'e.g., 100%, 200px',
    group: 'dimensions'
  },
  minWidth: {
    type: PropertyTypes.TEXT,
    label: 'Min Width',
    placeholder: 'e.g., 100px',
    group: 'dimensions'
  },
  maxWidth: {
    type: PropertyTypes.TEXT,
    label: 'Max Width',
    placeholder: 'e.g., 500px',
    group: 'dimensions'
  },
  minHeight: {
    type: PropertyTypes.TEXT,
    label: 'Min Height',
    placeholder: 'e.g., 100px',
    group: 'dimensions'
  },
  maxHeight: {
    type: PropertyTypes.TEXT,
    label: 'Max Height',
    placeholder: 'e.g., 500px',
    group: 'dimensions'
  },
  // Spacing
  margin: {
    type: PropertyTypes.SPACING,
    label: 'Margin',
    group: 'spacing'
  },
  padding: {
    type: PropertyTypes.SPACING,
    label: 'Padding',
    group: 'spacing'
  },
  // Typography
  fontSize: {
    type: PropertyTypes.NUMBER,
    label: 'Font Size',
    min: 8,
    max: 72,
    unit: 'px',
    units: ['px', 'em', 'rem', '%'],
    group: 'typography'
  },
  fontWeight: {
    type: PropertyTypes.SELECT,
    label: 'Font Weight',
    options: [{
      value: 'normal',
      label: 'Normal'
    }, {
      value: 'bold',
      label: 'Bold'
    }, {
      value: 'lighter',
      label: 'Lighter'
    }, {
      value: 'bolder',
      label: 'Bolder'
    }, {
      value: '100',
      label: '100'
    }, {
      value: '200',
      label: '200'
    }, {
      value: '300',
      label: '300'
    }, {
      value: '400',
      label: '400'
    }, {
      value: '500',
      label: '500'
    }, {
      value: '600',
      label: '600'
    }, {
      value: '700',
      label: '700'
    }, {
      value: '800',
      label: '800'
    }, {
      value: '900',
      label: '900'
    }],
    group: 'typography'
  },
  lineHeight: {
    type: PropertyTypes.NUMBER,
    label: 'Line Height',
    min: 0.5,
    max: 3,
    step: 0.1,
    precision: 1,
    showUnit: false,
    group: 'typography'
  },
  fontFamily: {
    type: PropertyTypes.FONT,
    label: 'Font Family',
    group: 'typography'
  },
  // Colors
  color: {
    type: PropertyTypes.COLOR,
    label: 'Text Color',
    group: 'colors'
  },
  backgroundColor: {
    type: PropertyTypes.COLOR,
    label: 'Background Color',
    group: 'colors'
  },
  // Border
  border: {
    type: PropertyTypes.BORDER,
    label: 'Border',
    group: 'border'
  },
  borderTop: {
    type: PropertyTypes.BORDER,
    label: 'Border Top',
    group: 'border'
  },
  borderRight: {
    type: PropertyTypes.BORDER,
    label: 'Border Right',
    group: 'border'
  },
  borderBottom: {
    type: PropertyTypes.BORDER,
    label: 'Border Bottom',
    group: 'border'
  },
  borderLeft: {
    type: PropertyTypes.BORDER,
    label: 'Border Left',
    group: 'border'
  },
  borderRadius: {
    type: PropertyTypes.NUMBER,
    label: 'Border Radius',
    min: 0,
    max: 50,
    unit: 'px',
    units: ['px', 'em', 'rem', '%'],
    group: 'border'
  },
  // Shadow
  boxShadow: {
    type: PropertyTypes.SHADOW,
    label: 'Box Shadow',
    group: 'shadow'
  },
  textShadow: {
    type: PropertyTypes.SHADOW,
    label: 'Text Shadow',
    group: 'shadow'
  },
  // Layout
  display: {
    type: PropertyTypes.SELECT,
    label: 'Display',
    options: [{
      value: 'block',
      label: 'Block'
    }, {
      value: 'inline',
      label: 'Inline'
    }, {
      value: 'inline-block',
      label: 'Inline Block'
    }, {
      value: 'flex',
      label: 'Flex'
    }, {
      value: 'grid',
      label: 'Grid'
    }, {
      value: 'none',
      label: 'None'
    }],
    group: 'layout'
  },
  position: {
    type: PropertyTypes.SELECT,
    label: 'Position',
    options: [{
      value: 'static',
      label: 'Static'
    }, {
      value: 'relative',
      label: 'Relative'
    }, {
      value: 'absolute',
      label: 'Absolute'
    }, {
      value: 'fixed',
      label: 'Fixed'
    }, {
      value: 'sticky',
      label: 'Sticky'
    }],
    group: 'layout'
  }
};

/**
 * Detect property type based on property name and value
 */
var detectPropertyType = function detectPropertyType(propertyName, value, componentType) {
  // Check component-specific schema first
  if (componentType && ComponentSchemas[componentType] && ComponentSchemas[componentType][propertyName]) {
    return ComponentSchemas[componentType][propertyName];
  }

  // Check style schema
  if (StyleSchemas[propertyName]) {
    return StyleSchemas[propertyName];
  }

  // Fallback to value-based detection
  return detectTypeFromValue(propertyName, value);
};

/**
 * Detect property type from value and property name patterns
 */
var detectTypeFromValue = function detectTypeFromValue(propertyName, value) {
  var name = propertyName.toLowerCase();

  // Color detection
  if (name.includes('color') || name.includes('background')) {
    return {
      type: PropertyTypes.COLOR,
      label: formatLabel(propertyName)
    };
  }

  // Number detection
  if (typeof value === 'number' || typeof value === 'string' && /^\d+(\.\d+)?(px|em|rem|%)?$/.test(value)) {
    return {
      type: PropertyTypes.NUMBER,
      label: formatLabel(propertyName)
    };
  }

  // Boolean detection
  if (typeof value === 'boolean') {
    return {
      type: PropertyTypes.BOOLEAN,
      label: formatLabel(propertyName)
    };
  }

  // Spacing detection
  if (name.includes('margin') || name.includes('padding')) {
    return {
      type: PropertyTypes.SPACING,
      label: formatLabel(propertyName)
    };
  }

  // Border detection
  if (name.includes('border') && !name.includes('radius')) {
    return {
      type: PropertyTypes.BORDER,
      label: formatLabel(propertyName)
    };
  }

  // Shadow detection
  if (name.includes('shadow')) {
    return {
      type: PropertyTypes.SHADOW,
      label: formatLabel(propertyName)
    };
  }

  // Font detection
  if (name.includes('font') && !name.includes('size') && !name.includes('weight')) {
    return {
      type: PropertyTypes.FONT,
      label: formatLabel(propertyName)
    };
  }

  // Array detection
  if (Array.isArray(value)) {
    return {
      type: PropertyTypes.ARRAY,
      label: formatLabel(propertyName)
    };
  }

  // Object detection
  if ((0,esm_typeof/* default */.A)(value) === 'object' && value !== null) {
    return {
      type: PropertyTypes.OBJECT,
      label: formatLabel(propertyName)
    };
  }

  // Default to text
  return {
    type: PropertyTypes.TEXT,
    label: formatLabel(propertyName)
  };
};

/**
 * Format property name into a readable label
 */
var formatLabel = function formatLabel(propertyName) {
  return propertyName.replace(/([A-Z])/g, ' $1').replace(/^./, function (str) {
    return str.toUpperCase();
  }).trim();
};

/**
 * Get all properties for a component type
 */
var getComponentProperties = function getComponentProperties(componentType) {
  return ComponentSchemas[componentType] || {};
};

/**
 * Get style properties grouped by category
 */
var getStylePropertiesGrouped = function getStylePropertiesGrouped() {
  var grouped = {};
  Object.entries(StyleSchemas).forEach(function (_ref) {
    var _ref2 = (0,slicedToArray/* default */.A)(_ref, 2),
      key = _ref2[0],
      schema = _ref2[1];
    var group = schema.group || 'other';
    if (!grouped[group]) {
      grouped[group] = {};
    }
    grouped[group][key] = schema;
  });
  return grouped;
};

/**
 * Validate property value based on its type
 */
var validatePropertyValue = function validatePropertyValue(value, schema) {
  if (!schema) return {
    valid: true
  };
  switch (schema.type) {
    case PropertyTypes.NUMBER:
      var num = parseFloat(value);
      if (isNaN(num)) {
        return {
          valid: false,
          error: 'Must be a valid number'
        };
      }
      if (schema.min !== undefined && num < schema.min) {
        return {
          valid: false,
          error: "Must be at least ".concat(schema.min)
        };
      }
      if (schema.max !== undefined && num > schema.max) {
        return {
          valid: false,
          error: "Must be at most ".concat(schema.max)
        };
      }
      break;
    case PropertyTypes.COLOR:
      var colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/;
      if (value && !colorRegex.test(value)) {
        return {
          valid: false,
          error: 'Must be a valid color value'
        };
      }
      break;
    case PropertyTypes.SELECT:
      if (schema.options && value && !schema.options.some(function (opt) {
        return opt.value === value;
      })) {
        return {
          valid: false,
          error: 'Must be one of the available options'
        };
      }
      break;
  }
  return {
    valid: true
  };
};
;// ./src/components/enhanced/property-editor/PropertyRenderer.js



var PropertyRenderer_excluded = ["propertyName", "value", "onChange", "componentType", "schema", "showValidation"];





var TextArea = antd_es/* Input */.pd.TextArea;
var PropertyRenderer_Option = antd_es/* Select */.l6.Option;
var PropertyRenderer_Text = antd_es/* Typography */.o5.Text;

/**
 * Renders the appropriate input component based on property type
 */
var PropertyRenderer = function PropertyRenderer(_ref) {
  var _propertySchema$optio;
  var propertyName = _ref.propertyName,
    value = _ref.value,
    onChange = _ref.onChange,
    componentType = _ref.componentType,
    schema = _ref.schema,
    _ref$showValidation = _ref.showValidation,
    showValidation = _ref$showValidation === void 0 ? true : _ref$showValidation,
    props = (0,objectWithoutProperties/* default */.A)(_ref, PropertyRenderer_excluded);
  // Get property schema (either provided or detected)
  var propertySchema = schema || detectPropertyType(propertyName, value, componentType);

  // Validate current value
  var validation = showValidation ? validatePropertyValue(value, propertySchema) : {
    valid: true
  };

  // Handle value change with validation
  var handleChange = function handleChange(newValue) {
    if (onChange) {
      onChange(newValue, propertyName, propertySchema);
    }
  };

  // Render validation error
  var renderValidationError = function renderValidationError() {
    if (!showValidation || validation.valid) return null;
    return /*#__PURE__*/react.createElement(PropertyRenderer_Text, {
      type: "danger",
      style: {
        fontSize: '12px',
        display: 'block',
        marginTop: '4px'
      }
    }, validation.error);
  };

  // Render tooltip if description is provided
  var renderTooltip = function renderTooltip() {
    if (!propertySchema.description && !propertySchema.tooltip) return null;
    return /*#__PURE__*/react.createElement(antd_es/* Tooltip */.m_, {
      title: propertySchema.description || propertySchema.tooltip
    }, /*#__PURE__*/react.createElement(es/* InfoCircleOutlined */.rUN, {
      style: {
        color: '#8c8c8c',
        marginLeft: '4px'
      }
    }));
  };

  // Render based on property type
  switch (propertySchema.type) {
    case PropertyTypes.TEXT:
      if (propertySchema.multiline) {
        return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(TextArea, (0,esm_extends/* default */.A)({
          value: value,
          onChange: function onChange(e) {
            return handleChange(e.target.value);
          },
          placeholder: propertySchema.placeholder,
          rows: propertySchema.rows || 3,
          status: !validation.valid ? 'error' : ''
        }, props)), renderTooltip(), renderValidationError());
      }
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(antd_es/* Input */.pd, (0,esm_extends/* default */.A)({
        value: value,
        onChange: function onChange(e) {
          return handleChange(e.target.value);
        },
        placeholder: propertySchema.placeholder,
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.NUMBER:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(property_editor_NumberInput, (0,esm_extends/* default */.A)({
        value: value,
        onChange: handleChange,
        min: propertySchema.min,
        max: propertySchema.max,
        step: propertySchema.step,
        precision: propertySchema.precision,
        unit: propertySchema.unit,
        units: propertySchema.units,
        showSlider: propertySchema.showSlider,
        showUnit: propertySchema.showUnit,
        placeholder: propertySchema.placeholder,
        tooltip: propertySchema.tooltip
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.BOOLEAN:
      return /*#__PURE__*/react.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, /*#__PURE__*/react.createElement(antd_es/* Switch */.dO, (0,esm_extends/* default */.A)({
        checked: value,
        onChange: handleChange
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.COLOR:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(property_editor_ColorInput, (0,esm_extends/* default */.A)({
        value: value,
        onChange: handleChange,
        showPresets: propertySchema.showPresets,
        showModeToggle: propertySchema.showModeToggle,
        presets: propertySchema.presets,
        placeholder: propertySchema.placeholder
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.SELECT:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(antd_es/* Select */.l6, (0,esm_extends/* default */.A)({
        value: value,
        onChange: handleChange,
        placeholder: propertySchema.placeholder,
        style: {
          width: '100%'
        },
        status: !validation.valid ? 'error' : ''
      }, props), (_propertySchema$optio = propertySchema.options) === null || _propertySchema$optio === void 0 ? void 0 : _propertySchema$optio.map(function (option) {
        return /*#__PURE__*/react.createElement(PropertyRenderer_Option, {
          key: option.value,
          value: option.value
        }, option.label);
      })), renderTooltip(), renderValidationError());
    case PropertyTypes.SPACING:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(property_editor_SpacingEditor, (0,esm_extends/* default */.A)({
        value: value,
        onChange: handleChange,
        type: propertyName.includes('margin') ? 'margin' : 'padding',
        showVisual: propertySchema.showVisual,
        showPresets: propertySchema.showPresets,
        unit: propertySchema.unit
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.BORDER:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(property_editor_BorderEditor, (0,esm_extends/* default */.A)({
        value: value,
        onChange: handleChange,
        showPreview: propertySchema.showPreview
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.SHADOW:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(property_editor_ShadowEditor, (0,esm_extends/* default */.A)({
        value: value,
        onChange: handleChange,
        showPreview: propertySchema.showPreview
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.FONT:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(property_editor_FontSelector, (0,esm_extends/* default */.A)({
        value: value,
        onChange: handleChange,
        showPreview: propertySchema.showPreview
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.ARRAY:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(TextArea, (0,esm_extends/* default */.A)({
        value: Array.isArray(value) ? JSON.stringify(value, null, 2) : value,
        onChange: function onChange(e) {
          try {
            var parsed = JSON.parse(e.target.value);
            handleChange(parsed);
          } catch (error) {
            // Keep the raw string value for now
            handleChange(e.target.value);
          }
        },
        placeholder: propertySchema.placeholder || 'Enter array as JSON',
        rows: 4,
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.OBJECT:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(TextArea, (0,esm_extends/* default */.A)({
        value: (0,esm_typeof/* default */.A)(value) === 'object' ? JSON.stringify(value, null, 2) : value,
        onChange: function onChange(e) {
          try {
            var parsed = JSON.parse(e.target.value);
            handleChange(parsed);
          } catch (error) {
            // Keep the raw string value for now
            handleChange(e.target.value);
          }
        },
        placeholder: propertySchema.placeholder || 'Enter object as JSON',
        rows: 6,
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
    case PropertyTypes.JSON:
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(TextArea, (0,esm_extends/* default */.A)({
        value: typeof value === 'string' ? value : JSON.stringify(value, null, 2),
        onChange: function onChange(e) {
          return handleChange(e.target.value);
        },
        placeholder: propertySchema.placeholder || 'Enter JSON',
        rows: 8,
        status: !validation.valid ? 'error' : '',
        style: {
          fontFamily: 'monospace'
        }
      }, props)), renderTooltip(), renderValidationError());
    default:
      // Fallback to text input
      return /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(antd_es/* Input */.pd, (0,esm_extends/* default */.A)({
        value: value,
        onChange: function onChange(e) {
          return handleChange(e.target.value);
        },
        placeholder: propertySchema.placeholder || 'Enter value',
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
  }
};
/* harmony default export */ const property_editor_PropertyRenderer = (PropertyRenderer);
;// ./src/components/enhanced/property-editor/PropertySearch.js



var PropertySearch_excluded = ["properties", "onFilter", "showGroupFilter", "showTypeFilter", "placeholder"];
var PropertySearch_templateObject, PropertySearch_templateObject2, PropertySearch_templateObject3;




var PropertySearch_Text = antd_es/* Typography */.o5.Text;
var PropertySearch_Option = antd_es/* Select */.l6.Option;
var SearchContainer = design_system.styled.div(PropertySearch_templateObject || (PropertySearch_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n"])));
var FilterRow = design_system.styled.div(PropertySearch_templateObject2 || (PropertySearch_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"])));
var TagContainer = design_system.styled.div(PropertySearch_templateObject3 || (PropertySearch_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n"])));

/**
 * Property search and filter component
 */
var PropertySearch = function PropertySearch(_ref) {
  var _ref$properties = _ref.properties,
    properties = _ref$properties === void 0 ? {} : _ref$properties,
    onFilter = _ref.onFilter,
    _ref$showGroupFilter = _ref.showGroupFilter,
    showGroupFilter = _ref$showGroupFilter === void 0 ? true : _ref$showGroupFilter,
    _ref$showTypeFilter = _ref.showTypeFilter,
    showTypeFilter = _ref$showTypeFilter === void 0 ? true : _ref$showTypeFilter,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? 'Search properties...' : _ref$placeholder,
    props = (0,objectWithoutProperties/* default */.A)(_ref, PropertySearch_excluded);
  var _useState = (0,react.useState)(''),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    searchTerm = _useState2[0],
    setSearchTerm = _useState2[1];
  var _useState3 = (0,react.useState)('all'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    selectedGroup = _useState4[0],
    setSelectedGroup = _useState4[1];
  var _useState5 = (0,react.useState)('all'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    selectedType = _useState6[0],
    setSelectedType = _useState6[1];

  // Extract unique groups and types from properties
  var _useMemo = (0,react.useMemo)(function () {
      var groupSet = new Set();
      var typeSet = new Set();
      Object.values(properties).forEach(function (property) {
        if (property.group) {
          groupSet.add(property.group);
        }
        if (property.type) {
          typeSet.add(property.type);
        }
      });
      return {
        groups: Array.from(groupSet).sort(),
        types: Array.from(typeSet).sort()
      };
    }, [properties]),
    groups = _useMemo.groups,
    types = _useMemo.types;

  // Filter properties based on search term, group, and type
  var filteredProperties = (0,react.useMemo)(function () {
    var filtered = {};
    Object.entries(properties).forEach(function (_ref2) {
      var _ref3 = (0,slicedToArray/* default */.A)(_ref2, 2),
        key = _ref3[0],
        property = _ref3[1];
      var matchesSearch = !searchTerm || key.toLowerCase().includes(searchTerm.toLowerCase()) || property.label && property.label.toLowerCase().includes(searchTerm.toLowerCase()) || property.description && property.description.toLowerCase().includes(searchTerm.toLowerCase());
      var matchesGroup = selectedGroup === 'all' || property.group === selectedGroup;
      var matchesType = selectedType === 'all' || property.type === selectedType;
      if (matchesSearch && matchesGroup && matchesType) {
        filtered[key] = property;
      }
    });
    return filtered;
  }, [properties, searchTerm, selectedGroup, selectedType]);

  // Handle filter changes
  var handleSearchChange = function handleSearchChange(e) {
    var value = e.target.value;
    setSearchTerm(value);
    onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
      searchTerm: value,
      group: selectedGroup,
      type: selectedType
    });
  };
  var handleGroupChange = function handleGroupChange(value) {
    setSelectedGroup(value);
    onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
      searchTerm: searchTerm,
      group: value,
      type: selectedType
    });
  };
  var handleTypeChange = function handleTypeChange(value) {
    setSelectedType(value);
    onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
      searchTerm: searchTerm,
      group: selectedGroup,
      type: value
    });
  };
  var handleClear = function handleClear() {
    setSearchTerm('');
    setSelectedGroup('all');
    setSelectedType('all');
    onFilter === null || onFilter === void 0 || onFilter(properties, {
      searchTerm: '',
      group: 'all',
      type: 'all'
    });
  };

  // Get active filter count
  var activeFilters = [searchTerm && 'search', selectedGroup !== 'all' && 'group', selectedType !== 'all' && 'type'].filter(Boolean);

  // Format group and type names for display
  var formatName = function formatName(name) {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
  };
  return /*#__PURE__*/react.createElement(SearchContainer, null, /*#__PURE__*/react.createElement(FilterRow, null, /*#__PURE__*/react.createElement(antd_es/* Input */.pd, {
    prefix: /*#__PURE__*/react.createElement(es/* SearchOutlined */.VrN, null),
    placeholder: placeholder,
    value: searchTerm,
    onChange: handleSearchChange,
    allowClear: true,
    style: {
      flex: 1
    }
  }), showGroupFilter && groups.length > 0 && /*#__PURE__*/react.createElement(antd_es/* Select */.l6, {
    value: selectedGroup,
    onChange: handleGroupChange,
    style: {
      minWidth: 120
    },
    size: "small"
  }, /*#__PURE__*/react.createElement(PropertySearch_Option, {
    value: "all"
  }, "All Groups"), groups.map(function (group) {
    return /*#__PURE__*/react.createElement(PropertySearch_Option, {
      key: group,
      value: group
    }, formatName(group));
  })), showTypeFilter && types.length > 0 && /*#__PURE__*/react.createElement(antd_es/* Select */.l6, {
    value: selectedType,
    onChange: handleTypeChange,
    style: {
      minWidth: 100
    },
    size: "small"
  }, /*#__PURE__*/react.createElement(PropertySearch_Option, {
    value: "all"
  }, "All Types"), types.map(function (type) {
    return /*#__PURE__*/react.createElement(PropertySearch_Option, {
      key: type,
      value: type
    }, formatName(type));
  })), activeFilters.length > 0 && /*#__PURE__*/react.createElement(es/* ClearOutlined */.ohj, {
    onClick: handleClear,
    style: {
      cursor: 'pointer',
      color: '#8c8c8c'
    },
    title: "Clear all filters"
  })), activeFilters.length > 0 && /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    size: 4,
    style: {
      marginBottom: 4
    }
  }, /*#__PURE__*/react.createElement(es/* FilterOutlined */.Lxx, {
    style: {
      fontSize: '12px',
      color: '#8c8c8c'
    }
  }), /*#__PURE__*/react.createElement(PropertySearch_Text, {
    type: "secondary",
    style: {
      fontSize: '12px'
    }
  }, "Active filters:")), /*#__PURE__*/react.createElement(TagContainer, null, searchTerm && /*#__PURE__*/react.createElement(antd_es/* Tag */.vw, {
    closable: true,
    onClose: function onClose() {
      setSearchTerm('');
      onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
        searchTerm: '',
        group: selectedGroup,
        type: selectedType
      });
    },
    size: "small"
  }, "Search: \"", searchTerm, "\""), selectedGroup !== 'all' && /*#__PURE__*/react.createElement(antd_es/* Tag */.vw, {
    closable: true,
    onClose: function onClose() {
      setSelectedGroup('all');
      onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
        searchTerm: searchTerm,
        group: 'all',
        type: selectedType
      });
    },
    size: "small"
  }, "Group: ", formatName(selectedGroup)), selectedType !== 'all' && /*#__PURE__*/react.createElement(antd_es/* Tag */.vw, {
    closable: true,
    onClose: function onClose() {
      setSelectedType('all');
      onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
        searchTerm: searchTerm,
        group: selectedGroup,
        type: 'all'
      });
    },
    size: "small"
  }, "Type: ", formatName(selectedType)))), /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    style: {
      marginTop: 8,
      fontSize: '12px'
    }
  }, /*#__PURE__*/react.createElement(PropertySearch_Text, {
    type: "secondary"
  }, "Showing ", Object.keys(filteredProperties).length, " of ", Object.keys(properties).length, " properties")));
};
/* harmony default export */ const property_editor_PropertySearch = (PropertySearch);
;// ./src/components/enhanced/property-editor/PropertyGroup.js




var PropertyGroup_excluded = ["groupName", "properties", "values", "onChange", "componentType", "collapsible", "defaultExpanded", "showResetAll", "showPropertyCount"];
var PropertyGroup_templateObject, PropertyGroup_templateObject2, PropertyGroup_templateObject3, PropertyGroup_templateObject4, PropertyGroup_templateObject5, PropertyGroup_templateObject6, PropertyGroup_templateObject7, PropertyGroup_templateObject8;





var Panel = antd_es/* Collapse */.SD.Panel;
var PropertyGroup_Text = antd_es/* Typography */.o5.Text;
var GroupContainer = design_system.styled.div(PropertyGroup_templateObject || (PropertyGroup_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n"])));
var GroupHeader = design_system.styled.div(PropertyGroup_templateObject2 || (PropertyGroup_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: #fafafa;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  \n  &:hover {\n    background: #f5f5f5;\n  }\n"])));
var GroupTitle = design_system.styled.div(PropertyGroup_templateObject3 || (PropertyGroup_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var GroupActions = design_system.styled.div(PropertyGroup_templateObject4 || (PropertyGroup_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"])));
var PropertyItem = design_system.styled.div(PropertyGroup_templateObject5 || (PropertyGroup_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n"])));
var PropertyGroup_PropertyLabel = design_system.styled.div(PropertyGroup_templateObject6 || (PropertyGroup_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n"])));
var PropertyName = (0,design_system.styled)(PropertyGroup_Text)(PropertyGroup_templateObject7 || (PropertyGroup_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-weight: 500;\n  font-size: 13px;\n"])));
var PropertyDescription = (0,design_system.styled)(PropertyGroup_Text)(PropertyGroup_templateObject8 || (PropertyGroup_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n  color: #8c8c8c;\n  display: block;\n  margin-top: 2px;\n"])));

/**
 * Property group component for organizing and displaying properties
 */
var PropertyGroup = function PropertyGroup(_ref) {
  var groupName = _ref.groupName,
    _ref$properties = _ref.properties,
    properties = _ref$properties === void 0 ? {} : _ref$properties,
    _ref$values = _ref.values,
    values = _ref$values === void 0 ? {} : _ref$values,
    onChange = _ref.onChange,
    componentType = _ref.componentType,
    _ref$collapsible = _ref.collapsible,
    collapsible = _ref$collapsible === void 0 ? true : _ref$collapsible,
    _ref$defaultExpanded = _ref.defaultExpanded,
    defaultExpanded = _ref$defaultExpanded === void 0 ? true : _ref$defaultExpanded,
    _ref$showResetAll = _ref.showResetAll,
    showResetAll = _ref$showResetAll === void 0 ? true : _ref$showResetAll,
    _ref$showPropertyCoun = _ref.showPropertyCount,
    showPropertyCount = _ref$showPropertyCoun === void 0 ? true : _ref$showPropertyCoun,
    props = (0,objectWithoutProperties/* default */.A)(_ref, PropertyGroup_excluded);
  var _useState = (0,react.useState)(defaultExpanded),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    isExpanded = _useState2[0],
    setIsExpanded = _useState2[1];

  // Get group icon based on group name
  var getGroupIcon = function getGroupIcon(name) {
    var iconMap = {
      basic: /*#__PURE__*/react.createElement(es/* SettingOutlined */.JO7, null),
      dimensions: /*#__PURE__*/react.createElement(es/* ColumnWidthOutlined */.x18, null),
      spacing: /*#__PURE__*/react.createElement(es/* LayoutOutlined */.hy2, null),
      typography: /*#__PURE__*/react.createElement(es/* FontSizeOutlined */.ld1, null),
      colors: /*#__PURE__*/react.createElement(es/* BgColorsOutlined */.Ebl, null),
      border: /*#__PURE__*/react.createElement(es/* BorderOutlined */.bnM, null),
      shadow: /*#__PURE__*/react.createElement(es/* HighlightOutlined */.NSj, null),
      layout: /*#__PURE__*/react.createElement(es/* LayoutOutlined */.hy2, null)
    };
    return iconMap[name.toLowerCase()] || /*#__PURE__*/react.createElement(es/* SettingOutlined */.JO7, null);
  };

  // Format group name for display
  var formatGroupName = function formatGroupName(name) {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
  };

  // Handle property value change
  var handlePropertyChange = function handlePropertyChange(newValue, propertyName, schema) {
    if (onChange) {
      onChange(propertyName, newValue, schema);
    }
  };

  // Handle reset all properties in group
  var handleResetAll = function handleResetAll(e) {
    e.stopPropagation();
    Object.keys(properties).forEach(function (propertyName) {
      var schema = properties[propertyName];
      var defaultValue = schema.defaultValue || '';
      handlePropertyChange(defaultValue, propertyName, schema);
    });
  };

  // Handle reset individual property
  var handleResetProperty = function handleResetProperty(propertyName, e) {
    e.stopPropagation();
    var schema = properties[propertyName];
    var defaultValue = schema.defaultValue || '';
    handlePropertyChange(defaultValue, propertyName, schema);
  };

  // Toggle group expansion
  var toggleExpanded = function toggleExpanded() {
    if (collapsible) {
      setIsExpanded(!isExpanded);
    }
  };

  // Count properties with non-default values
  var modifiedCount = Object.keys(properties).filter(function (key) {
    var value = values[key];
    var defaultValue = properties[key].defaultValue || '';
    return value !== defaultValue && value !== '' && value !== null && value !== undefined;
  }).length;
  var propertyCount = Object.keys(properties).length;
  if (propertyCount === 0) {
    return null;
  }
  return /*#__PURE__*/react.createElement(GroupContainer, null, /*#__PURE__*/react.createElement(GroupHeader, {
    onClick: toggleExpanded
  }, /*#__PURE__*/react.createElement(GroupTitle, null, collapsible && (isExpanded ? /*#__PURE__*/react.createElement(es/* DownOutlined */.lHd, {
    style: {
      fontSize: '12px'
    }
  }) : /*#__PURE__*/react.createElement(es/* RightOutlined */.Xq1, {
    style: {
      fontSize: '12px'
    }
  })), getGroupIcon(groupName), /*#__PURE__*/react.createElement(PropertyGroup_Text, {
    strong: true,
    style: {
      fontSize: '14px'
    }
  }, formatGroupName(groupName)), showPropertyCount && /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    size: 4
  }, /*#__PURE__*/react.createElement(antd_es/* Badge */.Ex, {
    count: propertyCount,
    size: "small",
    color: "#f0f0f0",
    style: {
      color: '#8c8c8c'
    }
  }), modifiedCount > 0 && /*#__PURE__*/react.createElement(antd_es/* Badge */.Ex, {
    count: modifiedCount,
    size: "small",
    color: "#1890ff"
  }))), /*#__PURE__*/react.createElement(GroupActions, null, showResetAll && modifiedCount > 0 && /*#__PURE__*/react.createElement(antd_es/* Tooltip */.m_, {
    title: "Reset all properties in this group"
  }, /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react.createElement(es/* UndoOutlined */.Xrf, null),
    onClick: handleResetAll,
    style: {
      fontSize: '12px'
    }
  })))), isExpanded && /*#__PURE__*/react.createElement("div", {
    style: {
      border: '1px solid #f0f0f0',
      borderTop: 'none',
      borderRadius: '0 0 4px 4px'
    }
  }, Object.entries(properties).map(function (_ref2) {
    var _ref3 = (0,slicedToArray/* default */.A)(_ref2, 2),
      propertyName = _ref3[0],
      schema = _ref3[1];
    var currentValue = values[propertyName];
    var hasValue = currentValue !== undefined && currentValue !== '' && currentValue !== null;
    var isModified = hasValue && currentValue !== (schema.defaultValue || '');
    return /*#__PURE__*/react.createElement(PropertyItem, {
      key: propertyName
    }, /*#__PURE__*/react.createElement(PropertyGroup_PropertyLabel, null, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(PropertyName, null, schema.label || propertyName, schema.required && /*#__PURE__*/react.createElement(PropertyGroup_Text, {
      type: "danger"
    }, " *")), schema.description && /*#__PURE__*/react.createElement(PropertyDescription, null, schema.description)), isModified && /*#__PURE__*/react.createElement(antd_es/* Tooltip */.m_, {
      title: "Reset to default"
    }, /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react.createElement(es/* UndoOutlined */.Xrf, null),
      onClick: function onClick(e) {
        return handleResetProperty(propertyName, e);
      },
      style: {
        fontSize: '12px'
      }
    }))), /*#__PURE__*/react.createElement(property_editor_PropertyRenderer, (0,esm_extends/* default */.A)({
      propertyName: propertyName,
      value: currentValue,
      onChange: handlePropertyChange,
      componentType: componentType,
      schema: schema,
      size: "small"
    }, props)));
  })));
};
/* harmony default export */ const property_editor_PropertyGroup = (PropertyGroup);
;// ./src/components/enhanced/property-editor/PropertyPreview.js






var PropertyPreview_excluded = ["component", "properties", "values", "showPreview", "showCode", "showValidation", "onReset"];
var PropertyPreview_templateObject, PropertyPreview_templateObject2, PropertyPreview_templateObject3, PropertyPreview_templateObject4, PropertyPreview_templateObject5;
function PropertyPreview_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function PropertyPreview_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? PropertyPreview_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : PropertyPreview_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var PropertyPreview_Text = antd_es/* Typography */.o5.Text,
  Title = antd_es/* Typography */.o5.Title;
var PreviewContainer = design_system.styled.div(PropertyPreview_templateObject || (PropertyPreview_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background: white;\n  border-bottom: 1px solid #f0f0f0;\n"])));
var PreviewArea = design_system.styled.div(PropertyPreview_templateObject2 || (PropertyPreview_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  min-height: 120px;\n  padding: 16px;\n  background: ", ";\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n"])), function (props) {
  return props.background || '#f5f5f5';
});
var PreviewElement = design_system.styled.div(PropertyPreview_templateObject3 || (PropertyPreview_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  transition: all 0.2s ease;\n  ", "\n"])), function (props) {
  return props.styles || '';
});
var CodePreview = design_system.styled.pre(PropertyPreview_templateObject4 || (PropertyPreview_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  background: #f6f8fa;\n  border: 1px solid #e1e4e8;\n  border-radius: 4px;\n  padding: 12px;\n  font-size: 12px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  overflow-x: auto;\n  max-height: 200px;\n  margin: 0;\n"])));
var ValidationMessage = design_system.styled.div(PropertyPreview_templateObject5 || (PropertyPreview_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-top: 8px;\n"])));

/**
 * Real-time preview component for property changes
 */
var PropertyPreview = function PropertyPreview(_ref) {
  var component = _ref.component,
    _ref$properties = _ref.properties,
    properties = _ref$properties === void 0 ? {} : _ref$properties,
    _ref$values = _ref.values,
    values = _ref$values === void 0 ? {} : _ref$values,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    _ref$showCode = _ref.showCode,
    showCode = _ref$showCode === void 0 ? false : _ref$showCode,
    _ref$showValidation = _ref.showValidation,
    showValidation = _ref$showValidation === void 0 ? true : _ref$showValidation,
    onReset = _ref.onReset,
    props = (0,objectWithoutProperties/* default */.A)(_ref, PropertyPreview_excluded);
  var _useState = (0,react.useState)(showPreview),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    previewEnabled = _useState2[0],
    setPreviewEnabled = _useState2[1];
  var _useState3 = (0,react.useState)(showCode),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    codeVisible = _useState4[0],
    setCodeVisible = _useState4[1];
  var _useState5 = (0,react.useState)({}),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    validationErrors = _useState6[0],
    setValidationErrors = _useState6[1];

  // Generate styles from current property values
  var generatedStyles = (0,react.useMemo)(function () {
    var styles = {};
    Object.entries(values).forEach(function (_ref2) {
      var _ref3 = (0,slicedToArray/* default */.A)(_ref2, 2),
        key = _ref3[0],
        value = _ref3[1];
      if (value !== undefined && value !== null && value !== '') {
        // Convert camelCase to kebab-case for CSS
        var cssProperty = key.replace(/([A-Z])/g, '-$1').toLowerCase();

        // Handle special cases
        if ((0,esm_typeof/* default */.A)(value) === 'object' && value !== null) {
          // For complex objects like font settings
          if (key === 'font' || key === 'fontFamily') {
            Object.entries(value).forEach(function (_ref4) {
              var _ref5 = (0,slicedToArray/* default */.A)(_ref4, 2),
                subKey = _ref5[0],
                subValue = _ref5[1];
              var cssSubProperty = subKey.replace(/([A-Z])/g, '-$1').toLowerCase();
              styles[cssSubProperty] = subValue;
            });
          } else {
            // Convert object to string representation
            styles[cssProperty] = JSON.stringify(value);
          }
        } else {
          styles[cssProperty] = value;
        }
      }
    });
    return styles;
  }, [values]);

  // Generate CSS string for code preview
  var cssCode = (0,react.useMemo)(function () {
    var cssLines = Object.entries(generatedStyles).map(function (_ref6) {
      var _ref7 = (0,slicedToArray/* default */.A)(_ref6, 2),
        property = _ref7[0],
        value = _ref7[1];
      return "  ".concat(property, ": ").concat(value, ";");
    });
    return ".component {\n".concat(cssLines.join('\n'), "\n}");
  }, [generatedStyles]);

  // Generate inline styles object
  var inlineStyles = (0,react.useMemo)(function () {
    var styles = {};
    Object.entries(generatedStyles).forEach(function (_ref8) {
      var _ref9 = (0,slicedToArray/* default */.A)(_ref8, 2),
        property = _ref9[0],
        value = _ref9[1];
      // Convert kebab-case back to camelCase for React inline styles
      var reactProperty = property.replace(/-([a-z])/g, function (match, letter) {
        return letter.toUpperCase();
      });
      styles[reactProperty] = value;
    });
    return styles;
  }, [generatedStyles]);

  // Validate current values
  (0,react.useEffect)(function () {
    var errors = {};
    Object.entries(values).forEach(function (_ref0) {
      var _ref1 = (0,slicedToArray/* default */.A)(_ref0, 2),
        key = _ref1[0],
        value = _ref1[1];
      var schema = properties[key];
      if (schema && value !== undefined && value !== null && value !== '') {
        // Basic validation
        if (schema.type === 'number') {
          var num = parseFloat(value);
          if (isNaN(num)) {
            errors[key] = 'Must be a valid number';
          } else if (schema.min !== undefined && num < schema.min) {
            errors[key] = "Must be at least ".concat(schema.min);
          } else if (schema.max !== undefined && num > schema.max) {
            errors[key] = "Must be at most ".concat(schema.max);
          }
        } else if (schema.type === 'color') {
          var colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/;
          if (!colorRegex.test(value)) {
            errors[key] = 'Must be a valid color value';
          }
        }
      }
    });
    setValidationErrors(errors);
  }, [values, properties]);

  // Render preview element based on component type
  var renderPreviewElement = function renderPreviewElement() {
    var elementProps = {
      style: inlineStyles,
      className: 'preview-element'
    };
    switch (component === null || component === void 0 ? void 0 : component.type) {
      case 'button':
        return /*#__PURE__*/react.createElement("button", elementProps, values.text || 'Button');
      case 'text':
        var Tag = values.variant || 'p';
        return /*#__PURE__*/react.createElement(Tag, elementProps, values.content || 'Sample text');
      case 'input':
        return /*#__PURE__*/react.createElement("input", (0,esm_extends/* default */.A)({}, elementProps, {
          type: values.type || 'text',
          placeholder: values.placeholder || 'Enter text',
          disabled: values.disabled
        }));
      case 'card':
        return /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.A)({}, elementProps, {
          style: PropertyPreview_objectSpread(PropertyPreview_objectSpread({}, inlineStyles), {}, {
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            padding: '16px',
            minWidth: '200px'
          })
        }), values.title && /*#__PURE__*/react.createElement("h4", {
          style: {
            margin: '0 0 8px 0'
          }
        }, values.title), values.description && /*#__PURE__*/react.createElement("p", {
          style: {
            margin: 0,
            color: '#666'
          }
        }, values.description));
      default:
        return /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.A)({}, elementProps, {
          style: PropertyPreview_objectSpread(PropertyPreview_objectSpread({}, inlineStyles), {}, {
            padding: '16px',
            background: '#fff',
            border: '1px solid #d9d9d9',
            borderRadius: '4px'
          })
        }), (component === null || component === void 0 ? void 0 : component.name) || 'Component', " Preview");
    }
  };
  var hasErrors = Object.keys(validationErrors).length > 0;
  var hasChanges = Object.keys(values).some(function (key) {
    return values[key] !== undefined && values[key] !== '' && values[key] !== null;
  });
  return /*#__PURE__*/react.createElement(PreviewContainer, null, /*#__PURE__*/react.createElement(antd_es/* Card */.Zp, {
    size: "small",
    title: "Property Preview"
  }, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    style: {
      width: '100%',
      justifyContent: 'space-between'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, null, /*#__PURE__*/react.createElement(antd_es/* Switch */.dO, {
    checked: previewEnabled,
    onChange: setPreviewEnabled,
    checkedChildren: /*#__PURE__*/react.createElement(es/* EyeOutlined */.Om2, null),
    unCheckedChildren: /*#__PURE__*/react.createElement(es/* EyeInvisibleOutlined */.LCF, null),
    size: "small"
  }), /*#__PURE__*/react.createElement(PropertyPreview_Text, {
    style: {
      fontSize: '12px'
    }
  }, "Live Preview")), /*#__PURE__*/react.createElement(antd_es/* Space */.$x, null, /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react.createElement(es/* CodeOutlined */.C$o, null),
    onClick: function onClick() {
      return setCodeVisible(!codeVisible);
    },
    style: {
      fontSize: '12px'
    }
  }, codeVisible ? 'Hide' : 'Show', " CSS"), hasChanges && /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react.createElement(es/* UndoOutlined */.Xrf, null),
    onClick: onReset,
    style: {
      fontSize: '12px'
    }
  }, "Reset All"))), previewEnabled && /*#__PURE__*/react.createElement(PreviewArea, null, /*#__PURE__*/react.createElement(PreviewElement, {
    styles: Object.entries(inlineStyles).map(function (_ref10) {
      var _ref11 = (0,slicedToArray/* default */.A)(_ref10, 2),
        k = _ref11[0],
        v = _ref11[1];
      return "".concat(k, ": ").concat(v, ";");
    }).join(' ')
  }, renderPreviewElement())), codeVisible && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(antd_es/* Divider */.cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(PropertyPreview_Text, {
    strong: true,
    style: {
      fontSize: '12px'
    }
  }, "Generated CSS:"), /*#__PURE__*/react.createElement(CodePreview, null, cssCode))), showValidation && hasErrors && /*#__PURE__*/react.createElement(ValidationMessage, null, /*#__PURE__*/react.createElement(antd_es/* Alert */.Fc, {
    message: "Validation Errors",
    description: /*#__PURE__*/react.createElement("ul", {
      style: {
        margin: 0,
        paddingLeft: '16px'
      }
    }, Object.entries(validationErrors).map(function (_ref12) {
      var _ref13 = (0,slicedToArray/* default */.A)(_ref12, 2),
        key = _ref13[0],
        error = _ref13[1];
      return /*#__PURE__*/react.createElement("li", {
        key: key,
        style: {
          fontSize: '12px'
        }
      }, /*#__PURE__*/react.createElement("strong", null, key, ":"), " ", error);
    })),
    type: "error",
    size: "small",
    showIcon: true
  })), showValidation && !hasErrors && hasChanges && /*#__PURE__*/react.createElement(ValidationMessage, null, /*#__PURE__*/react.createElement(antd_es/* Alert */.Fc, {
    message: "All properties are valid",
    type: "success",
    size: "small",
    showIcon: true
  })))));
};
/* harmony default export */ const property_editor_PropertyPreview = (PropertyPreview);
// EXTERNAL MODULE: ./src/redux/actions.js
var actions = __webpack_require__(81616);
// EXTERNAL MODULE: ./node_modules/lodash/lodash.js
var lodash = __webpack_require__(2543);
;// ./src/components/enhanced/property-editor/EnhancedComponentProperties.js




var EnhancedComponentProperties_excluded = ["name"];
var EnhancedComponentProperties_templateObject, EnhancedComponentProperties_templateObject2, EnhancedComponentProperties_templateObject3, EnhancedComponentProperties_templateObject4;
function EnhancedComponentProperties_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function EnhancedComponentProperties_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? EnhancedComponentProperties_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : EnhancedComponentProperties_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









// Import enhanced property editor components

var EnhancedComponentProperties_Title = antd_es/* Typography */.o5.Title,
  EnhancedComponentProperties_Text = antd_es/* Typography */.o5.Text;
var TabPane = antd_es/* Tabs */.tU.TabPane;
var PropertiesContainer = design_system.styled.div(EnhancedComponentProperties_templateObject || (EnhancedComponentProperties_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n"])));
var PropertiesContent = design_system.styled.div(EnhancedComponentProperties_templateObject2 || (EnhancedComponentProperties_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n"])));
var PropertiesActions = design_system.styled.div(EnhancedComponentProperties_templateObject3 || (EnhancedComponentProperties_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 16px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n"])));
var TabContent = design_system.styled.div(EnhancedComponentProperties_templateObject4 || (EnhancedComponentProperties_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 0;\n"])));

/**
 * Enhanced ComponentProperties component with advanced property editing capabilities
 * Now includes real-time preview updates and collaborative editing support
 */
var EnhancedComponentProperties = function EnhancedComponentProperties(_ref) {
  var component = _ref.component,
    onUpdate = _ref.onUpdate,
    onRealTimeUpdate = _ref.onRealTimeUpdate,
    _ref$enableRealTimePr = _ref.enableRealTimePreview,
    enableRealTimePreview = _ref$enableRealTimePr === void 0 ? true : _ref$enableRealTimePr,
    _ref$enableCollaborat = _ref.enableCollaboration,
    enableCollaboration = _ref$enableCollaborat === void 0 ? false : _ref$enableCollaborat,
    _ref$collaborativeSes = _ref.collaborativeSession,
    collaborativeSession = _ref$collaborativeSes === void 0 ? null : _ref$collaborativeSes;
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var _useState = (0,react.useState)('basic'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react.useState)({}),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    propertyValues = _useState4[0],
    setPropertyValues = _useState4[1];
  var _useState5 = (0,react.useState)({}),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    filteredProperties = _useState6[0],
    setFilteredProperties = _useState6[1];
  var _useState7 = (0,react.useState)(false),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    hasUnsavedChanges = _useState8[0],
    setHasUnsavedChanges = _useState8[1];
  var _useState9 = (0,react.useState)(enableRealTimePreview),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    isRealTimeEnabled = _useState0[0],
    setIsRealTimeEnabled = _useState0[1];
  var _useState1 = (0,react.useState)(false),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    isUpdating = _useState10[0],
    setIsUpdating = _useState10[1];
  var _useState11 = (0,react.useState)(null),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    lastUpdateTime = _useState12[0],
    setLastUpdateTime = _useState12[1];

  // Refs for debouncing and performance
  var updateTimeoutRef = (0,react.useRef)(null);
  var lastValuesRef = (0,react.useRef)({});

  // Initialize property values when component changes
  (0,react.useEffect)(function () {
    if (component) {
      var initialValues = EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({
        name: component.name
      }, component.props), component.style);
      setPropertyValues(initialValues);
      setHasUnsavedChanges(false);
    }
  }, [component]);

  // Get component-specific properties
  var componentProperties = (0,react.useMemo)(function () {
    if (!(component !== null && component !== void 0 && component.type)) return {};
    return getComponentProperties(component.type);
  }, [component === null || component === void 0 ? void 0 : component.type]);

  // Get style properties grouped by category
  var stylePropertiesGrouped = (0,react.useMemo)(function () {
    return getStylePropertiesGrouped();
  }, []);

  // Debounced real-time update function
  var debouncedRealTimeUpdate = (0,react.useMemo)(function () {
    return (0,lodash.debounce)(function (updatedComponent) {
      if (isRealTimeEnabled && onRealTimeUpdate) {
        setIsUpdating(true);
        onRealTimeUpdate(updatedComponent);
        setLastUpdateTime(new Date());

        // Clear updating state after a short delay
        setTimeout(function () {
          return setIsUpdating(false);
        }, 300);
      }
    }, 300);
  }, [isRealTimeEnabled, onRealTimeUpdate]);

  // Immediate visual update function (no debounce)
  var immediateUpdate = (0,react.useCallback)(function (updatedComponent) {
    if (onUpdate) {
      onUpdate(updatedComponent);
    }
  }, [onUpdate]);

  // Handle property value change with real-time updates
  var handlePropertyChange = (0,react.useCallback)(function (propertyName, newValue, schema) {
    var newPropertyValues = EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, propertyValues), {}, (0,defineProperty/* default */.A)({}, propertyName, newValue));
    setPropertyValues(newPropertyValues);
    setHasUnsavedChanges(true);

    // Create updated component
    var updatedComponent = EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, component), {}, {
      name: propertyName === 'name' ? newValue : newPropertyValues.name,
      props: EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, component.props), propertyName !== 'name' && !StyleSchemas[propertyName] ? (0,defineProperty/* default */.A)({}, propertyName, newValue) : {}),
      style: EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, component.style), StyleSchemas[propertyName] ? (0,defineProperty/* default */.A)({}, propertyName, newValue) : {})
    });

    // Immediate visual update for responsive UI
    immediateUpdate(updatedComponent);

    // Debounced real-time update for performance
    if (isRealTimeEnabled) {
      debouncedRealTimeUpdate(updatedComponent);
    }

    // Store last values for comparison
    lastValuesRef.current = newPropertyValues;
  }, [propertyValues, component, isRealTimeEnabled, immediateUpdate, debouncedRealTimeUpdate]);

  // Handle save changes
  var handleSave = function handleSave() {
    if (!component) return;
    var name = propertyValues.name,
      otherValues = (0,objectWithoutProperties/* default */.A)(propertyValues, EnhancedComponentProperties_excluded);
    var props = {};
    var style = {};

    // Separate props and styles
    Object.entries(otherValues).forEach(function (_ref4) {
      var _ref5 = (0,slicedToArray/* default */.A)(_ref4, 2),
        key = _ref5[0],
        value = _ref5[1];
      if (StyleSchemas[key]) {
        style[key] = value;
      } else {
        props[key] = value;
      }
    });
    var updatedComponent = EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, component), {}, {
      name: name || component.name,
      props: EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, component.props), props),
      style: EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, component.style), style)
    });

    // Dispatch update action
    dispatch((0,actions/* updateComponent */.ZP)(updatedComponent));
    setHasUnsavedChanges(false);

    // Call onUpdate callback
    if (onUpdate) {
      onUpdate(updatedComponent);
    }
  };

  // Handle reset all changes
  var handleResetAll = function handleResetAll() {
    if (component) {
      var initialValues = EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({
        name: component.name
      }, component.props), component.style);
      setPropertyValues(initialValues);
      setHasUnsavedChanges(false);
    }
  };

  // Handle property filter
  var handlePropertyFilter = function handlePropertyFilter(filtered, filterInfo) {
    setFilteredProperties(filtered);
  };

  // Cleanup debounced function on unmount
  (0,react.useEffect)(function () {
    return function () {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      debouncedRealTimeUpdate.cancel();
    };
  }, [debouncedRealTimeUpdate]);

  // Handle real-time toggle
  var handleRealTimeToggle = (0,react.useCallback)(function (enabled) {
    setIsRealTimeEnabled(enabled);
    if (enabled && hasUnsavedChanges) {
      // Trigger immediate update when enabling real-time mode
      var updatedComponent = EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, component), {}, {
        name: propertyValues.name || component.name,
        props: EnhancedComponentProperties_objectSpread({}, component.props),
        style: EnhancedComponentProperties_objectSpread({}, component.style)
      });

      // Separate props and styles from propertyValues
      Object.entries(propertyValues).forEach(function (_ref6) {
        var _ref7 = (0,slicedToArray/* default */.A)(_ref6, 2),
          key = _ref7[0],
          value = _ref7[1];
        if (key === 'name') return;
        if (StyleSchemas[key]) {
          updatedComponent.style[key] = value;
        } else {
          updatedComponent.props[key] = value;
        }
      });
      debouncedRealTimeUpdate(updatedComponent);
    }
  }, [hasUnsavedChanges, component, propertyValues, debouncedRealTimeUpdate]);

  // If no component is selected, show a message
  if (!component) {
    return /*#__PURE__*/react.createElement(PropertiesContainer, null, /*#__PURE__*/react.createElement("div", {
      style: {
        padding: '24px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/react.createElement(EnhancedComponentProperties_Text, {
      type: "secondary"
    }, "Select a component to edit its properties")));
  }
  return /*#__PURE__*/react.createElement(PropertiesContainer, null, /*#__PURE__*/react.createElement("div", {
    style: {
      padding: '12px 16px',
      borderBottom: '1px solid #f0f0f0',
      background: '#fafafa',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, null, /*#__PURE__*/react.createElement(EnhancedComponentProperties_Text, {
    strong: true,
    style: {
      fontSize: '14px'
    }
  }, component.name || component.type, " Properties"), isUpdating && /*#__PURE__*/react.createElement(es/* SyncOutlined */.OmY, {
    spin: true,
    style: {
      color: '#1890ff'
    }
  })), /*#__PURE__*/react.createElement(antd_es/* Space */.$x, null, enableRealTimePreview && /*#__PURE__*/react.createElement(antd_es/* Tooltip */.m_, {
    title: "Toggle real-time preview updates"
  }, /*#__PURE__*/react.createElement(antd_es/* Switch */.dO, {
    size: "small",
    checked: isRealTimeEnabled,
    onChange: handleRealTimeToggle,
    checkedChildren: /*#__PURE__*/react.createElement(es/* EyeOutlined */.Om2, null),
    unCheckedChildren: /*#__PURE__*/react.createElement(es/* EyeOutlined */.Om2, null)
  })), lastUpdateTime && isRealTimeEnabled && /*#__PURE__*/react.createElement(antd_es/* Badge */.Ex, {
    status: "success",
    text: "Updated ".concat(lastUpdateTime.toLocaleTimeString()),
    style: {
      fontSize: '11px'
    }
  }), enableCollaboration && collaborativeSession && /*#__PURE__*/react.createElement(antd_es/* Badge */.Ex, {
    count: collaborativeSession.collaboratorCount || 0,
    showZero: false,
    style: {
      backgroundColor: '#52c41a'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Tooltip */.m_, {
    title: "Active collaborators"
  }, /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    size: "small",
    type: "text",
    icon: /*#__PURE__*/react.createElement(es/* SyncOutlined */.OmY, null)
  }))))), /*#__PURE__*/react.createElement(property_editor_PropertyPreview, {
    component: component,
    properties: EnhancedComponentProperties_objectSpread(EnhancedComponentProperties_objectSpread({}, componentProperties), StyleSchemas),
    values: propertyValues,
    onReset: handleResetAll,
    showPreview: true,
    showCode: false,
    showValidation: true,
    realTimeEnabled: isRealTimeEnabled
  }), /*#__PURE__*/react.createElement(PropertiesContent, null, /*#__PURE__*/react.createElement(antd_es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    size: "small"
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: "Properties",
    key: "basic"
  }, /*#__PURE__*/react.createElement(TabContent, null, /*#__PURE__*/react.createElement(property_editor_PropertySearch, {
    properties: componentProperties,
    onFilter: handlePropertyFilter,
    placeholder: "Search component properties..."
  }), /*#__PURE__*/react.createElement(property_editor_PropertyGroup, {
    groupName: "basic",
    properties: {
      name: {
        type: 'text',
        label: 'Component Name',
        required: true,
        placeholder: 'Enter component name'
      }
    },
    values: propertyValues,
    onChange: handlePropertyChange,
    componentType: component.type,
    defaultExpanded: true
  }), Object.keys(componentProperties).length > 0 && /*#__PURE__*/react.createElement(property_editor_PropertyGroup, {
    groupName: "component",
    properties: componentProperties,
    values: propertyValues,
    onChange: handlePropertyChange,
    componentType: component.type,
    defaultExpanded: true
  }))), /*#__PURE__*/react.createElement(TabPane, {
    tab: "Styling",
    key: "style"
  }, /*#__PURE__*/react.createElement(TabContent, null, /*#__PURE__*/react.createElement(property_editor_PropertySearch, {
    properties: StyleSchemas,
    onFilter: handlePropertyFilter,
    placeholder: "Search style properties..."
  }), Object.entries(stylePropertiesGrouped).map(function (_ref8) {
    var _ref9 = (0,slicedToArray/* default */.A)(_ref8, 2),
      groupName = _ref9[0],
      groupProperties = _ref9[1];
    return /*#__PURE__*/react.createElement(property_editor_PropertyGroup, {
      key: groupName,
      groupName: groupName,
      properties: groupProperties,
      values: propertyValues,
      onChange: handlePropertyChange,
      componentType: component.type,
      defaultExpanded: groupName === 'dimensions' || groupName === 'colors'
    });
  }))), /*#__PURE__*/react.createElement(TabPane, {
    tab: "Advanced",
    key: "advanced"
  }, /*#__PURE__*/react.createElement(TabContent, null, /*#__PURE__*/react.createElement(property_editor_PropertyGroup, {
    groupName: "advanced",
    properties: {
      customProps: {
        type: 'json',
        label: 'Custom Properties',
        placeholder: 'Enter custom properties as JSON',
        description: 'Additional properties not covered by the standard options'
      },
      customStyles: {
        type: 'json',
        label: 'Custom Styles',
        placeholder: 'Enter custom styles as JSON',
        description: 'Additional CSS styles not covered by the standard options'
      }
    },
    values: propertyValues,
    onChange: handlePropertyChange,
    componentType: component.type,
    defaultExpanded: true
  }))))), /*#__PURE__*/react.createElement(PropertiesActions, null, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    style: {
      width: '100%',
      justifyContent: 'space-between'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, null, hasUnsavedChanges && /*#__PURE__*/react.createElement(EnhancedComponentProperties_Text, {
    type: "warning",
    style: {
      fontSize: '12px'
    }
  }, "Unsaved changes")), /*#__PURE__*/react.createElement(antd_es/* Space */.$x, null, /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    size: "small",
    icon: /*#__PURE__*/react.createElement(es/* UndoOutlined */.Xrf, null),
    onClick: handleResetAll,
    disabled: !hasUnsavedChanges
  }, "Reset"), /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    type: "primary",
    size: "small",
    icon: /*#__PURE__*/react.createElement(es/* SaveOutlined */.ylI, null),
    onClick: handleSave,
    disabled: !hasUnsavedChanges
  }, "Apply Changes")))));
};
/* harmony default export */ const property_editor_EnhancedComponentProperties = (EnhancedComponentProperties);
;// ./src/components/enhanced/property-editor/index.js
// Enhanced Property Editor Components







// Property Type Detection and Rendering



// Property Organization and Management




// Enhanced Property Editor

;// ./src/components/enhanced/ComponentBuilder.js




var ComponentBuilder_templateObject, ComponentBuilder_templateObject2, ComponentBuilder_templateObject3, ComponentBuilder_templateObject4, ComponentBuilder_templateObject5, ComponentBuilder_templateObject6, ComponentBuilder_templateObject7;
function ComponentBuilder_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function ComponentBuilder_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ComponentBuilder_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ComponentBuilder_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }










// Import enhanced component builder with fallback
var EnhancedComponentBuilder;
try {
  EnhancedComponentBuilder = (__webpack_require__(54043)/* ["default"] */ .A);
} catch (error) {
  console.warn('Enhanced component builder not available, using fallback');
  EnhancedComponentBuilder = null;
}
var ComponentBuilderContainer = design_system.styled.div(ComponentBuilder_templateObject || (ComponentBuilder_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);
var ComponentGrid = design_system.styled.div(ComponentBuilder_templateObject2 || (ComponentBuilder_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);
var ComponentPreview = design_system.styled.div(ComponentBuilder_templateObject3 || (ComponentBuilder_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: white;\n"])), theme/* default.spacing */.Ay.spacing[4], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md);
var PropertyEditor = design_system.styled.div(ComponentBuilder_templateObject4 || (ComponentBuilder_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[3]);
var ComponentBuilder_PropertyGroup = design_system.styled.div(ComponentBuilder_templateObject5 || (ComponentBuilder_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2]);
var ComponentItem = design_system.styled.div(ComponentBuilder_templateObject6 || (ComponentBuilder_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  cursor: pointer;\n  transition: ", ";\n  border: 2px solid ", ";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ", ";\n  }\n"])), theme/* default.transitions */.Ay.transitions["default"], function (props) {
  return props.isSelected ? theme/* default.colors */.Ay.colors.primary.main : 'transparent';
}, theme/* default.shadows */.Ay.shadows.md);
var EmptyState = design_system.styled.div(ComponentBuilder_templateObject7 || (ComponentBuilder_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ", ";\n  background-color: ", ";\n  border-radius: ", ";\n  text-align: center;\n"])), theme/* default.spacing */.Ay.spacing[8], theme/* default.colors */.Ay.colors.neutral[100], theme/* default.borderRadius */.Ay.borderRadius.md);
var componentTypes = [{
  value: 'container',
  label: 'Container'
}, {
  value: 'text',
  label: 'Text'
}, {
  value: 'button',
  label: 'Button'
}, {
  value: 'input',
  label: 'Input Field'
}, {
  value: 'image',
  label: 'Image'
}, {
  value: 'card',
  label: 'Card'
}, {
  value: 'list',
  label: 'List'
}, {
  value: 'custom',
  label: 'Custom'
}];
var ComponentBuilder = function ComponentBuilder() {
  // Check if enhanced mode is available
  var _useState = (0,react.useState)(EnhancedComponentBuilder !== null),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 1),
    useEnhancedMode = _useState2[0];

  // If enhanced mode is enabled and available, use the enhanced component builder
  if (useEnhancedMode && EnhancedComponentBuilder) {
    return /*#__PURE__*/react.createElement(EnhancedComponentBuilder, null);
  }

  // Fallback to original implementation
  var dispatch = (0,react_redux/* useDispatch */.wA)();

  // Fix selector path to match store structure
  var components = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$app, _state$appData;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || ((_state$appData = state.appData) === null || _state$appData === void 0 ? void 0 : _state$appData.components) || [];
  });

  // Add loading state
  var _useState3 = (0,react.useState)(true),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    isLoading = _useState4[0],
    setIsLoading = _useState4[1];

  // All useState hooks must be called before any conditional returns
  var _useState5 = (0,react.useState)(''),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    componentName = _useState6[0],
    setComponentName = _useState6[1];
  var _useState7 = (0,react.useState)('container'),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    componentType = _useState8[0],
    setComponentType = _useState8[1];
  var _useState9 = (0,react.useState)('{}'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    componentProps = _useState0[0],
    setComponentProps = _useState0[1];
  var _useState1 = (0,react.useState)(null),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    selectedComponent = _useState10[0],
    setSelectedComponent = _useState10[1];
  var _useState11 = (0,react.useState)(false),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    editMode = _useState12[0],
    setEditMode = _useState12[1];
  var _useState13 = (0,react.useState)({}),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    errors = _useState14[0],
    setErrors = _useState14[1];

  // Function to create sample components
  var createSampleComponents = function createSampleComponents() {
    var sampleComponents = [{
      id: 'button-1',
      name: 'Primary Button',
      type: 'button',
      props: {
        text: 'Click Me',
        variant: 'primary',
        size: 'medium',
        onClick: 'handleButtonClick'
      },
      createdAt: new Date().toISOString()
    }, {
      id: 'text-1',
      name: 'Header Text',
      type: 'text',
      props: {
        content: 'Welcome to App Builder',
        variant: 'h1',
        color: '#2563EB',
        align: 'center'
      },
      createdAt: new Date().toISOString()
    }, {
      id: 'input-1',
      name: 'Email Input',
      type: 'input',
      props: {
        label: 'Email Address',
        placeholder: 'Enter your email',
        type: 'email',
        required: true,
        validation: 'email'
      },
      createdAt: new Date().toISOString()
    }, {
      id: 'card-1',
      name: 'Feature Card',
      type: 'card',
      props: {
        title: 'Easy to Use',
        description: 'Build applications with a simple drag-and-drop interface',
        image: 'https://via.placeholder.com/150',
        elevation: 'md'
      },
      createdAt: new Date().toISOString()
    }];

    // Add each sample component to the store
    sampleComponents.forEach(function (component) {
      dispatch((0,minimal_store.addComponent)(component));
    });
  };
  (0,react.useEffect)(function () {
    // Initialize component
    var init = /*#__PURE__*/function () {
      var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
        return regenerator_default().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              try {
                setIsLoading(true);

                // Check if we already have components
                if (components.length === 0) {
                  // Add sample components if none exist
                  createSampleComponents();
                }
                setIsLoading(false);
              } catch (error) {
                console.error('Failed to initialize ComponentBuilder:', error);
                setIsLoading(false);
              }
            case 1:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function init() {
        return _ref.apply(this, arguments);
      };
    }();
    init();
  }, [components.length, dispatch]);

  // All useEffect hooks must be called before any conditional returns
  (0,react.useEffect)(function () {
    console.log('ComponentBuilder mounting...');
    return function () {
      console.log('ComponentBuilder unmounting...');
    };
  }, []);
  (0,react.useEffect)(function () {
    console.log('Components updated:', components);
  }, [components]);
  (0,react.useEffect)(function () {
    if (Object.keys(errors).length > 0) {
      console.error('ComponentBuilder errors:', errors);
    }
  }, [errors]);
  if (isLoading) {
    return /*#__PURE__*/react.createElement("div", null, "Loading ComponentBuilder...");
  }
  var validateForm = function validateForm() {
    var newErrors = {};
    if (!componentName.trim()) {
      newErrors.name = 'Component name is required';
    }
    try {
      if (componentProps) {
        JSON.parse(componentProps);
      }
    } catch (error) {
      newErrors.props = 'Invalid JSON format';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  var handleAddComponent = function handleAddComponent() {
    if (!validateForm()) return;
    try {
      var propsObject = componentProps ? JSON.parse(componentProps) : {};
      var newComponent = {
        id: Date.now().toString(),
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        createdAt: new Date().toISOString()
      };
      dispatch((0,minimal_store.addComponent)(newComponent)).then(function () {
        // Reset form
        setComponentName('');
        setComponentType('container');
        setComponentProps('{}');
        setErrors({});
      })["catch"](function (error) {
        console.error('Failed to add component:', error);
        setErrors({
          submit: 'Failed to add component'
        });
      });
    } catch (error) {
      setErrors(ComponentBuilder_objectSpread(ComponentBuilder_objectSpread({}, errors), {}, {
        props: error.message
      }));
    }
  };
  var handleUpdateComponent = function handleUpdateComponent() {
    if (!selectedComponent || !validateForm()) return;
    try {
      var propsObject = componentProps ? JSON.parse(componentProps) : {};
      var updatedComponent = ComponentBuilder_objectSpread(ComponentBuilder_objectSpread({}, selectedComponent), {}, {
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        updatedAt: new Date().toISOString()
      });
      dispatch((0,minimal_store.updateComponent)(updatedComponent));

      // Reset form and exit edit mode
      setComponentName('');
      setComponentType('container');
      setComponentProps('{}');
      setSelectedComponent(null);
      setEditMode(false);
      setErrors({});
    } catch (error) {
      setErrors(ComponentBuilder_objectSpread(ComponentBuilder_objectSpread({}, errors), {}, {
        props: error.message
      }));
    }
  };
  var handleRemoveComponent = function handleRemoveComponent(id) {
    dispatch((0,minimal_store.removeComponent)(id));

    // If the removed component was selected, reset the form
    if (selectedComponent && selectedComponent.id === id) {
      setComponentName('');
      setComponentType('container');
      setComponentProps('{}');
      setSelectedComponent(null);
      setEditMode(false);
    }
  };
  var handleSelectComponent = function handleSelectComponent(component) {
    setSelectedComponent(component);
    setComponentName(component.name);
    setComponentType(component.type);
    setComponentProps(JSON.stringify(component.props, null, 2));
    setEditMode(true);
    setErrors({});
  };
  var handleCancelEdit = function handleCancelEdit() {
    setComponentName('');
    setComponentType('container');
    setComponentProps('{}');
    setSelectedComponent(null);
    setEditMode(false);
    setErrors({});
  };
  var handleDuplicateComponent = function handleDuplicateComponent(component) {
    var duplicatedComponent = ComponentBuilder_objectSpread(ComponentBuilder_objectSpread({}, component), {}, {
      id: Date.now().toString(),
      name: "".concat(component.name, " (Copy)"),
      createdAt: new Date().toISOString()
    });
    dispatch((0,minimal_store.addComponent)(duplicatedComponent));
  };
  return /*#__PURE__*/react.createElement(ComponentBuilderContainer, null, /*#__PURE__*/react.createElement(design_system.Card, null, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement(design_system.Card.Title, null, editMode ? 'Edit Component' : 'Create Component'), editMode && /*#__PURE__*/react.createElement(design_system.Button, {
    variant: "text",
    size: "small",
    onClick: handleCancelEdit,
    startIcon: /*#__PURE__*/react.createElement(es/* CloseOutlined */.r$3, null)
  }, "Cancel")), /*#__PURE__*/react.createElement(design_system.Card.Content, null, /*#__PURE__*/react.createElement(PropertyEditor, null, /*#__PURE__*/react.createElement(ComponentBuilder_PropertyGroup, null, /*#__PURE__*/react.createElement(design_system.Input, {
    label: "Component Name",
    value: componentName,
    onChange: function onChange(e) {
      return setComponentName(e.target.value);
    },
    placeholder: "Enter component name",
    fullWidth: true,
    error: !!errors.name,
    helperText: errors.name
  })), /*#__PURE__*/react.createElement(ComponentBuilder_PropertyGroup, null, /*#__PURE__*/react.createElement(design_system.Select, {
    label: "Component Type",
    value: componentType,
    onChange: function onChange(e) {
      return setComponentType(e.target.value);
    },
    options: componentTypes,
    fullWidth: true
  })), /*#__PURE__*/react.createElement(ComponentBuilder_PropertyGroup, null, /*#__PURE__*/react.createElement(design_system.Input, {
    label: "Component Props (JSON)",
    value: componentProps,
    onChange: function onChange(e) {
      return setComponentProps(e.target.value);
    },
    placeholder: "{\"text\": \"Hello\", \"color\": \"blue\"}",
    fullWidth: true,
    error: !!errors.props,
    helperText: errors.props,
    as: "textarea",
    rows: 5,
    style: {
      fontFamily: theme/* default.typography */.Ay.typography.fontFamily.code
    }
  })))), /*#__PURE__*/react.createElement(design_system.Card.Footer, null, editMode ? /*#__PURE__*/react.createElement(design_system.Button, {
    variant: "primary",
    onClick: handleUpdateComponent,
    startIcon: /*#__PURE__*/react.createElement(es/* SaveOutlined */.ylI, null)
  }, "Update Component") : /*#__PURE__*/react.createElement(design_system.Button, {
    variant: "primary",
    onClick: handleAddComponent,
    startIcon: /*#__PURE__*/react.createElement(es/* PlusOutlined */.bW0, null)
  }, "Add Component"))), /*#__PURE__*/react.createElement(design_system.Card, null, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement(design_system.Card.Title, null, "Component Library")), /*#__PURE__*/react.createElement(design_system.Card.Content, null, components.length === 0 ? /*#__PURE__*/react.createElement(EmptyState, null, /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: '48px',
      color: theme/* default.colors */.Ay.colors.neutral[400],
      marginBottom: theme/* default.spacing */.Ay.spacing[4]
    }
  }, /*#__PURE__*/react.createElement(es/* AppstoreOutlined */.rS9, null)), /*#__PURE__*/react.createElement("h3", null, "No Components Yet"), /*#__PURE__*/react.createElement("p", null, "Create your first component to get started")) : /*#__PURE__*/react.createElement(ComponentGrid, null, components.map(function (component) {
    return /*#__PURE__*/react.createElement(ComponentItem, {
      key: component.id,
      isSelected: selectedComponent && selectedComponent.id === component.id
    }, /*#__PURE__*/react.createElement(design_system.Card, {
      elevation: "sm"
    }, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
      style: {
        fontWeight: theme/* default.typography */.Ay.typography.fontWeight.semibold
      }
    }, component.name), /*#__PURE__*/react.createElement("div", {
      style: {
        fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
        color: theme/* default.colors */.Ay.colors.neutral[500]
      }
    }, component.type)), /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        gap: theme/* default.spacing */.Ay.spacing[1]
      }
    }, /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleDuplicateComponent(component);
      }
    }, /*#__PURE__*/react.createElement(es/* CopyOutlined */.wq3, null)), /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleSelectComponent(component);
      }
    }, /*#__PURE__*/react.createElement(es/* EditOutlined */.xjh, null)), /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleRemoveComponent(component.id);
      }
    }, /*#__PURE__*/react.createElement(es/* DeleteOutlined */.SUY, null)))), /*#__PURE__*/react.createElement(design_system.Card.Content, {
      onClick: function onClick() {
        return handleSelectComponent(component);
      }
    }, /*#__PURE__*/react.createElement(ComponentPreview, null, /*#__PURE__*/react.createElement("pre", {
      style: {
        margin: 0,
        overflow: 'auto',
        maxHeight: '100px'
      }
    }, JSON.stringify(component.props, null, 2))))));
  })))), selectedComponent && /*#__PURE__*/react.createElement(design_system.Card, null, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement(design_system.Card.Title, null, "Component Properties")), /*#__PURE__*/react.createElement(design_system.Card.Content, null, /*#__PURE__*/react.createElement(property_editor_EnhancedComponentProperties, {
    component: selectedComponent,
    onUpdate: function onUpdate(updatedComponent) {
      dispatch((0,minimal_store.updateComponent)(updatedComponent));
    }
  }))));
};
/* harmony default export */ const enhanced_ComponentBuilder = (ComponentBuilder);

/***/ })

}]);