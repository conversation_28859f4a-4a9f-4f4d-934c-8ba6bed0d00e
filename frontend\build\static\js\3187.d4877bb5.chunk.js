"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3187],{

/***/ 71602:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_WebSocketPage)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./src/redux/reducers/uiReducer.js
var uiReducer = __webpack_require__(85331);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./src/styles/components.js
var components = __webpack_require__(57749);
;// ./src/components/accessibility/Announcer.js


var _templateObject;



/**
 * Styled component for the announcer element
 * Visually hidden but available to screen readers
 */
var AnnouncerElement = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n"])));

/**
 * Announcer context for providing announcer functionality throughout the app
 */
var AnnouncerContext = /*#__PURE__*/react.createContext({
  announce: function announce() {},
  announcePolite: function announcePolite() {},
  announceAssertive: function announceAssertive() {}
});

/**
 * Announcer component
 * Provides a way to make announcements to screen readers
 */
var Announcer = function Announcer(_ref) {
  var children = _ref.children;
  var _useState = useState(''),
    _useState2 = _slicedToArray(_useState, 2),
    politeMessage = _useState2[0],
    setPoliteMessage = _useState2[1];
  var _useState3 = useState(''),
    _useState4 = _slicedToArray(_useState3, 2),
    assertiveMessage = _useState4[0],
    setAssertiveMessage = _useState4[1];

  // Clear messages after they've been announced
  useEffect(function () {
    if (politeMessage) {
      var timerId = setTimeout(function () {
        setPoliteMessage('');
      }, 500);
      return function () {
        return clearTimeout(timerId);
      };
    }
  }, [politeMessage]);
  useEffect(function () {
    if (assertiveMessage) {
      var timerId = setTimeout(function () {
        setAssertiveMessage('');
      }, 500);
      return function () {
        return clearTimeout(timerId);
      };
    }
  }, [assertiveMessage]);

  // Announce functions
  var announce = useCallback(function (message) {
    var ariaLive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'polite';
    if (ariaLive === 'assertive') {
      setAssertiveMessage(message);
    } else {
      setPoliteMessage(message);
    }
  }, []);
  var announcePolite = useCallback(function (message) {
    setPoliteMessage(message);
  }, []);
  var announceAssertive = useCallback(function (message) {
    setAssertiveMessage(message);
  }, []);
  return /*#__PURE__*/React.createElement(AnnouncerContext.Provider, {
    value: {
      announce: announce,
      announcePolite: announcePolite,
      announceAssertive: announceAssertive
    }
  }, /*#__PURE__*/React.createElement(AnnouncerElement, {
    "aria-live": "polite",
    "aria-atomic": "true",
    "data-testid": "announcer-polite"
  }, politeMessage), /*#__PURE__*/React.createElement(AnnouncerElement, {
    "aria-live": "assertive",
    "aria-atomic": "true",
    "data-testid": "announcer-assertive"
  }, assertiveMessage), children);
};

/**
 * Custom hook for using the announcer
 * @returns {Object} Announcer functions
 */
var useAnnouncer = function useAnnouncer() {
  return react.useContext(AnnouncerContext);
};
/* harmony default export */ const accessibility_Announcer = ((/* unused pure expression or super */ null && (Announcer)));
;// ./src/components/accessibility/FocusVisible.js

var FocusVisible_templateObject;


/**
 * FocusVisible component
 * Adds focus styles to elements that are focused via keyboard navigation
 * but not via mouse clicks
 */
var FocusVisible = (0,styled_components_browser_esm/* createGlobalStyle */.DU)(FocusVisible_templateObject || (FocusVisible_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Base focus styles */\n  :focus {\n    outline: none;\n  }\n\n  /* Focus visible styles for keyboard navigation */\n  :focus-visible {\n    outline: 2px solid ", ";\n    outline-offset: 2px;\n    box-shadow: 0 0 0 4px ", ";\n  }\n\n  /* Specific focus styles for different elements */\n  a:focus-visible,\n  button:focus-visible,\n  input:focus-visible,\n  textarea:focus-visible,\n  select:focus-visible,\n  [tabindex]:focus-visible {\n    outline: 2px solid ", ";\n    outline-offset: 2px;\n    box-shadow: 0 0 0 4px ", ";\n  }\n\n  /* High contrast mode focus styles */\n  @media (forced-colors: active) {\n    :focus-visible {\n      outline: 3px solid CanvasText;\n      outline-offset: 3px;\n    }\n  }\n"])), function (props) {
  var _props$theme, _props$theme2, _props$theme3;
  // Handle different theme structures
  if ((_props$theme = props.theme) !== null && _props$theme !== void 0 && (_props$theme = _props$theme.colorPalette) !== null && _props$theme !== void 0 && _props$theme.primary) {
    return props.theme.colorPalette.primary;
  }
  if ((_props$theme2 = props.theme) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.colors) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.primary) !== null && _props$theme2 !== void 0 && _props$theme2.main) {
    return props.theme.colors.primary.main;
  }
  if ((_props$theme3 = props.theme) !== null && _props$theme3 !== void 0 && _props$theme3.primaryColor) {
    return props.theme.primaryColor;
  }
  // Fallback color
  return '#2563EB';
}, function (props) {
  var _props$theme4, _props$theme5;
  // Handle different theme structures for light color
  if ((_props$theme4 = props.theme) !== null && _props$theme4 !== void 0 && (_props$theme4 = _props$theme4.colorPalette) !== null && _props$theme4 !== void 0 && _props$theme4.primaryLight) {
    return props.theme.colorPalette.primaryLight;
  }
  if ((_props$theme5 = props.theme) !== null && _props$theme5 !== void 0 && (_props$theme5 = _props$theme5.colors) !== null && _props$theme5 !== void 0 && (_props$theme5 = _props$theme5.primary) !== null && _props$theme5 !== void 0 && _props$theme5.light) {
    return props.theme.colors.primary.light;
  }
  // Fallback color
  return 'rgba(37, 99, 235, 0.2)';
}, function (props) {
  var _props$theme6, _props$theme7, _props$theme8;
  if ((_props$theme6 = props.theme) !== null && _props$theme6 !== void 0 && (_props$theme6 = _props$theme6.colorPalette) !== null && _props$theme6 !== void 0 && _props$theme6.primary) return props.theme.colorPalette.primary;
  if ((_props$theme7 = props.theme) !== null && _props$theme7 !== void 0 && (_props$theme7 = _props$theme7.colors) !== null && _props$theme7 !== void 0 && (_props$theme7 = _props$theme7.primary) !== null && _props$theme7 !== void 0 && _props$theme7.main) return props.theme.colors.primary.main;
  if ((_props$theme8 = props.theme) !== null && _props$theme8 !== void 0 && _props$theme8.primaryColor) return props.theme.primaryColor;
  return '#2563EB';
}, function (props) {
  var _props$theme9, _props$theme0;
  if ((_props$theme9 = props.theme) !== null && _props$theme9 !== void 0 && (_props$theme9 = _props$theme9.colorPalette) !== null && _props$theme9 !== void 0 && _props$theme9.primaryLight) return props.theme.colorPalette.primaryLight;
  if ((_props$theme0 = props.theme) !== null && _props$theme0 !== void 0 && (_props$theme0 = _props$theme0.colors) !== null && _props$theme0 !== void 0 && (_props$theme0 = _props$theme0.primary) !== null && _props$theme0 !== void 0 && _props$theme0.light) return props.theme.colors.primary.light;
  return 'rgba(37, 99, 235, 0.2)';
});
/* harmony default export */ const accessibility_FocusVisible = ((/* unused pure expression or super */ null && (FocusVisible)));
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
;// ./src/components/accessibility/ScreenReaderOnly.js



var _excluded = ["children", "as"];
var ScreenReaderOnly_templateObject;



/**
 * Styled component for visually hidden content that's only available to screen readers
 */
var VisuallyHidden = styled_components_browser_esm/* default */.Ay.span(ScreenReaderOnly_templateObject || (ScreenReaderOnly_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n"])));

/**
 * ScreenReaderOnly component
 * Renders content that is only visible to screen readers
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Content to be hidden visually but available to screen readers
 * @param {string} props.as - HTML element to render (default: span)
 */
var ScreenReaderOnly = function ScreenReaderOnly(_ref) {
  var children = _ref.children,
    _ref$as = _ref.as,
    as = _ref$as === void 0 ? 'span' : _ref$as,
    props = (0,objectWithoutProperties/* default */.A)(_ref, _excluded);
  return /*#__PURE__*/react.createElement(VisuallyHidden, (0,esm_extends/* default */.A)({
    as: as
  }, props), children);
};
/* harmony default export */ const accessibility_ScreenReaderOnly = (ScreenReaderOnly);
// EXTERNAL MODULE: ./src/components/accessibility/SkipLink.js
var SkipLink = __webpack_require__(76413);
;// ./src/components/accessibility/index.js





// EXTERNAL MODULE: ./src/config/env.js
var env = __webpack_require__(26390);
// EXTERNAL MODULE: ./src/services/EnhancedWebSocketClient.js
var EnhancedWebSocketClient = __webpack_require__(17177);
;// ./src/pages/WebSocketPage.js




var WebSocketPage_templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }










var Paragraph = es/* Typography */.o5.Paragraph,
  Text = es/* Typography */.o5.Text;
var TabPane = es/* Tabs */.tU.TabPane;

// Styled components
var MessageContainer = styled_components_browser_esm/* default */.Ay.div(WebSocketPage_templateObject || (WebSocketPage_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  height: 300px;\n  overflow-y: auto;\n  margin-bottom: 16px;\n  border: 1px solid ", ";\n  padding: 8px;\n  border-radius: ", ";\n"])), function (props) {
  var _props$theme;
  return ((_props$theme = props.theme) === null || _props$theme === void 0 || (_props$theme = _props$theme.colorPalette) === null || _props$theme === void 0 ? void 0 : _props$theme.border) || '#D1D5DB';
}, function (props) {
  var _props$theme2;
  return ((_props$theme2 = props.theme) === null || _props$theme2 === void 0 || (_props$theme2 = _props$theme2.borderRadius) === null || _props$theme2 === void 0 ? void 0 : _props$theme2.md) || '4px';
});
var MessageItem = (0,styled_components_browser_esm/* default */.Ay)(es/* List */.B8.Item)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 8px;\n  background-color: ", ";\n  margin-bottom: 8px;\n  border-radius: ", ";\n"])), function (props) {
  var _props$theme3, _props$theme4, _props$theme5, _props$theme6;
  switch (props.messageType) {
    case 'sent':
      return ((_props$theme3 = props.theme) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.colorPalette) === null || _props$theme3 === void 0 ? void 0 : _props$theme3.infoLight) || '#DBEAFE';
    case 'received':
      return ((_props$theme4 = props.theme) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.colorPalette) === null || _props$theme4 === void 0 ? void 0 : _props$theme4.successLight) || '#D1FAE5';
    case 'error':
      return ((_props$theme5 = props.theme) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.colorPalette) === null || _props$theme5 === void 0 ? void 0 : _props$theme5.errorLight) || '#FEE2E2';
    default:
      return ((_props$theme6 = props.theme) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.colorPalette) === null || _props$theme6 === void 0 ? void 0 : _props$theme6.warningLight) || '#FEF3C7';
  }
}, function (props) {
  var _props$theme7;
  return ((_props$theme7 = props.theme) === null || _props$theme7 === void 0 || (_props$theme7 = _props$theme7.borderRadius) === null || _props$theme7 === void 0 ? void 0 : _props$theme7.md) || '4px';
});
var MessageHeader = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 4px;\n"])));
var MessageContent = styled_components_browser_esm/* default */.Ay.pre(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin: 0;\n  white-space: pre-wrap;\n  word-break: break-word;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n"])));
var EmptyMessages = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  text-align: center;\n  color: ", ";\n  margin-top: 120px;\n"])), function (props) {
  var _props$theme8;
  return ((_props$theme8 = props.theme) === null || _props$theme8 === void 0 || (_props$theme8 = _props$theme8.colorPalette) === null || _props$theme8 === void 0 ? void 0 : _props$theme8.textSecondary) || '#4B5563';
});
var SettingItem = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n"])));

/**
 * WebSocket Page
 * Dedicated page for WebSocket functionality with enhanced WebSocket client
 */
var WebSocketPage = function WebSocketPage() {
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var _useAnnouncer = useAnnouncer(),
    announce = _useAnnouncer.announce;
  var _useState = (0,react.useState)((0,env/* getWebSocketUrl */.$0)('test')),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    wsUrl = _useState2[0],
    setWsUrl = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    connected = _useState4[0],
    setConnected = _useState4[1];
  var _useState5 = (0,react.useState)(EnhancedWebSocketClient.ConnectionState.CLOSED),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    connectionState = _useState6[0],
    setConnectionState = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    messages = _useState8[0],
    setMessages = _useState8[1];
  var _useState9 = (0,react.useState)(''),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    messageText = _useState0[0],
    setMessageText = _useState0[1];
  var _useState1 = (0,react.useState)('1'),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    activeTab = _useState10[0],
    setActiveTab = _useState10[1];
  var _useState11 = (0,react.useState)(false),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    isConnecting = _useState12[0],
    setIsConnecting = _useState12[1];

  // WebSocket client reference
  var wsClientRef = (0,react.useRef)(null);

  // WebSocket settings
  var _useState13 = (0,react.useState)({
      autoReconnect: true,
      debug: true,
      batchInterval: 50,
      maxBatchSize: 100,
      heartbeatInterval: 30000,
      enableCompression: true,
      persistOfflineMessages: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 10
    }),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    settings = _useState14[0],
    setSettings = _useState14[1];

  // Set the current view to 'websocket' when this page loads
  (0,react.useEffect)(function () {
    dispatch((0,uiReducer/* setCurrentView */.tI)('websocket'));

    // Clean up WebSocket client on unmount
    return function () {
      if (wsClientRef.current) {
        wsClientRef.current.destroy();
        wsClientRef.current = null;
      }
    };
  }, [dispatch]);

  // Connect to WebSocket using EnhancedWebSocketClient
  var connect = function connect() {
    // Clean up existing client
    if (wsClientRef.current) {
      wsClientRef.current.destroy();
    }

    // Add connection attempt message
    setMessages(function (prev) {
      return [].concat((0,toConsumableArray/* default */.A)(prev), [{
        type: 'system',
        text: "Attempting to connect to ".concat(wsUrl),
        timestamp: new Date()
      }]);
    });
    announce("Attempting to connect to ".concat(wsUrl));
    setIsConnecting(true);
    try {
      // Create new enhanced WebSocket client
      wsClientRef.current = new EnhancedWebSocketClient["default"]({
        url: wsUrl,
        autoConnect: false,
        debug: settings.debug,
        autoReconnect: settings.autoReconnect,
        reconnectInterval: settings.reconnectInterval,
        maxReconnectAttempts: settings.maxReconnectAttempts,
        batchInterval: settings.batchInterval,
        maxBatchSize: settings.maxBatchSize,
        heartbeatInterval: settings.heartbeatInterval,
        enableCompression: settings.enableCompression,
        persistOfflineMessages: settings.persistOfflineMessages
      });

      // Add event listeners
      wsClientRef.current.addEventListener('open', function (event) {
        setConnected(true);
        setConnectionState(EnhancedWebSocketClient.ConnectionState.OPEN);
        setIsConnecting(false);
        var message = "Connected to WebSocket server at ".concat(wsUrl);
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [{
            type: 'system',
            text: message,
            timestamp: new Date()
          }]);
        });
        announce('WebSocket connection established');

        // Send a test message automatically
        setTimeout(function () {
          if (wsClientRef.current && wsClientRef.current.connectionState === EnhancedWebSocketClient.ConnectionState.OPEN) {
            try {
              var testMessage = {
                type: 'ping',
                timestamp: new Date().toISOString(),
                client: 'EnhancedWebSocketPage'
              };
              wsClientRef.current.send(testMessage, true, {
                urgent: true
              });
              setMessages(function (prev) {
                return [].concat((0,toConsumableArray/* default */.A)(prev), [{
                  type: 'sent',
                  text: JSON.stringify(testMessage, null, 2),
                  timestamp: new Date(),
                  isTest: true
                }]);
              });
              announce('Sent automatic test message');
            } catch (e) {
              console.error('Failed to send test message:', e);
            }
          }
        }, 1000);
      });
      wsClientRef.current.addEventListener('message', function (event) {
        try {
          var data = event.data;
          var displayText;
          if (typeof data === 'string') {
            displayText = data;
          } else {
            displayText = JSON.stringify(data, null, 2);
          }
          setMessages(function (prev) {
            var _event$originalEvent;
            return [].concat((0,toConsumableArray/* default */.A)(prev), [{
              type: 'received',
              text: displayText,
              timestamp: new Date(),
              raw: (_event$originalEvent = event.originalEvent) === null || _event$originalEvent === void 0 ? void 0 : _event$originalEvent.data
            }]);
          });
          announce('Message received from server');
        } catch (error) {
          setMessages(function (prev) {
            return [].concat((0,toConsumableArray/* default */.A)(prev), [{
              type: 'received',
              text: String(event.data),
              timestamp: new Date(),
              parseError: error.message
            }]);
          });
          announce('Message received but could not be processed');
        }
      });
      wsClientRef.current.addEventListener('close', function (event) {
        setConnected(false);
        setConnectionState(EnhancedWebSocketClient.ConnectionState.CLOSED);
        setIsConnecting(false);
        var message = "Disconnected from server: ".concat(event.reason || 'Connection closed', " (code: ").concat(event.code, ")");
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [{
            type: 'system',
            text: message,
            timestamp: new Date(),
            wasClean: event.wasClean,
            code: event.code
          }]);
        });
        announce('WebSocket connection closed');
      });
      wsClientRef.current.addEventListener('error', function (error) {
        setIsConnecting(false);
        var message = "WebSocket error: ".concat(error.message || 'Unknown error');
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [{
            type: 'error',
            text: message,
            timestamp: new Date(),
            error: error
          }]);
        });
        announce('WebSocket error occurred', 'assertive');

        // Log detailed error information
        console.error('WebSocket connection error:', {
          url: wsUrl,
          error: error,
          connectionState: error.connectionState,
          timestamp: new Date().toISOString()
        });
      });
      wsClientRef.current.addEventListener('reconnect_attempt', function (data) {
        setConnectionState(EnhancedWebSocketClient.ConnectionState.RECONNECTING);
        var message = "Reconnecting to WebSocket server (attempt ".concat(data.attempt, ")...");
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [{
            type: 'system',
            text: message,
            timestamp: new Date()
          }]);
        });
        announce(message);
      });

      // Connect
      wsClientRef.current.open();
      setConnectionState(EnhancedWebSocketClient.ConnectionState.CONNECTING);
    } catch (error) {
      setIsConnecting(false);
      setMessages(function (prev) {
        return [].concat((0,toConsumableArray/* default */.A)(prev), [{
          type: 'error',
          text: "Failed to connect: ".concat(error.message),
          timestamp: new Date(),
          stack: error.stack
        }]);
      });
      announce("Connection error: ".concat(error.message));
      console.error('Failed to create WebSocket:', error);
    }
  };

  // Disconnect from WebSocket
  var disconnect = function disconnect() {
    if (wsClientRef.current) {
      wsClientRef.current.close();
      setConnectionState(EnhancedWebSocketClient.ConnectionState.CLOSING);
      announce('Disconnecting from WebSocket server');
    }
  };

  // Send message
  var sendMessage = function sendMessage() {
    if (!wsClientRef.current || wsClientRef.current.connectionState !== EnhancedWebSocketClient.ConnectionState.OPEN || !messageText.trim()) {
      announce('Cannot send message: WebSocket is not connected', 'assertive');
      return;
    }
    try {
      var messageToSend;

      // Try to parse as JSON
      try {
        messageToSend = JSON.parse(messageText);
      } catch (error) {
        // Send as plain text if not valid JSON
        messageToSend = messageText;
      }

      // Send the message
      wsClientRef.current.send(messageToSend);

      // Add to messages list
      setMessages(function (prev) {
        return [].concat((0,toConsumableArray/* default */.A)(prev), [{
          type: 'sent',
          text: typeof messageToSend === 'string' ? messageToSend : JSON.stringify(messageToSend, null, 2),
          timestamp: new Date()
        }]);
      });
      setMessageText('');
      announce('Message sent');
    } catch (error) {
      announce("Failed to send message: ".concat(error.message), 'assertive');
      console.error('Error sending message:', error);
    }
  };

  // Clear messages
  var clearMessages = function clearMessages() {
    setMessages([]);
    announce('Messages cleared');
  };

  // Format timestamp
  var formatTime = function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
  };
  return /*#__PURE__*/react.createElement(components/* Container */.mc, null, /*#__PURE__*/react.createElement(components/* PageTitle */.sT, {
    level: 2
  }, /*#__PURE__*/react.createElement(icons_es/* WifiOutlined */._bA, {
    style: {
      marginRight: '8px'
    }
  }), "WebSocket Manager"), /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: "Messages",
    key: "1"
  }, /*#__PURE__*/react.createElement(components/* StyledCard */.ee, {
    title: /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react.createElement("span", null, "Enhanced WebSocket Connection"), /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      status: connectionState === EnhancedWebSocketClient.ConnectionState.OPEN ? "success" : connectionState === EnhancedWebSocketClient.ConnectionState.CONNECTING ? "processing" : connectionState === EnhancedWebSocketClient.ConnectionState.RECONNECTING ? "warning" : connectionState === EnhancedWebSocketClient.ConnectionState.CLOSING ? "warning" : "error",
      text: connectionState === EnhancedWebSocketClient.ConnectionState.OPEN ? "Connected" : connectionState === EnhancedWebSocketClient.ConnectionState.CONNECTING ? "Connecting" : connectionState === EnhancedWebSocketClient.ConnectionState.RECONNECTING ? "Reconnecting" : connectionState === EnhancedWebSocketClient.ConnectionState.CLOSING ? "Closing" : "Disconnected"
    }), /*#__PURE__*/react.createElement(accessibility_ScreenReaderOnly, null, connectionState === EnhancedWebSocketClient.ConnectionState.OPEN ? "WebSocket is connected" : connectionState === EnhancedWebSocketClient.ConnectionState.CONNECTING ? "WebSocket is connecting" : connectionState === EnhancedWebSocketClient.ConnectionState.RECONNECTING ? "WebSocket is reconnecting" : connectionState === EnhancedWebSocketClient.ConnectionState.CLOSING ? "WebSocket is closing" : "WebSocket is disconnected")),
    extra: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(components/* PrimaryButton */.jn, {
      type: "primary",
      onClick: connect,
      disabled: connectionState !== EnhancedWebSocketClient.ConnectionState.CLOSED,
      loading: isConnecting,
      icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
      "aria-label": "Connect to WebSocket"
    }, "Connect"), /*#__PURE__*/react.createElement(components/* SecondaryButton */.tA, {
      danger: true,
      onClick: disconnect,
      disabled: connectionState === EnhancedWebSocketClient.ConnectionState.CLOSED || connectionState === EnhancedWebSocketClient.ConnectionState.CLOSING,
      "aria-label": "Disconnect from WebSocket"
    }, "Disconnect"), /*#__PURE__*/react.createElement(components/* IconButton */.K0, {
      onClick: clearMessages,
      icon: /*#__PURE__*/react.createElement(icons_es/* ClearOutlined */.ohj, null),
      "aria-label": "Clear all messages"
    }, "Clear"))
  }, /*#__PURE__*/react.createElement(components/* FormGroup */.gE, null, /*#__PURE__*/react.createElement(components/* StyledInput */.sQ, {
    value: wsUrl,
    onChange: function onChange(e) {
      return setWsUrl(e.target.value);
    },
    placeholder: "WebSocket URL",
    addonBefore: "URL"
  })), /*#__PURE__*/react.createElement(MessageContainer, null, messages.length === 0 ? /*#__PURE__*/react.createElement(EmptyMessages, null, /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "No messages yet")) : /*#__PURE__*/react.createElement(es/* List */.B8, {
    dataSource: messages,
    "aria-label": "WebSocket messages",
    renderItem: function renderItem(message) {
      return /*#__PURE__*/react.createElement(MessageItem, {
        messageType: message.type
      }, /*#__PURE__*/react.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react.createElement(MessageHeader, null, /*#__PURE__*/react.createElement(Text, {
        strong: true
      }, message.type === 'sent' ? 'Sent' : message.type === 'received' ? 'Received' : message.type === 'error' ? 'Error' : 'System'), /*#__PURE__*/react.createElement(Text, {
        type: "secondary"
      }, formatTime(message.timestamp))), /*#__PURE__*/react.createElement(MessageContent, null, message.text)));
    }
  })), /*#__PURE__*/react.createElement(components/* FormGroup */.gE, null, /*#__PURE__*/react.createElement(components/* StyledTextArea */.aQ, {
    value: messageText,
    onChange: function onChange(e) {
      return setMessageText(e.target.value);
    },
    placeholder: "Enter message to send (JSON or plain text)",
    rows: 4,
    disabled: connectionState !== EnhancedWebSocketClient.ConnectionState.OPEN,
    onPressEnter: function onPressEnter(e) {
      if (!e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    }
  })), /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(components/* PrimaryButton */.jn, {
    type: "primary",
    onClick: sendMessage,
    disabled: connectionState !== EnhancedWebSocketClient.ConnectionState.OPEN || !messageText.trim(),
    icon: /*#__PURE__*/react.createElement(icons_es/* SendOutlined */.jnF, null),
    "aria-label": "Send message"
  }, "Send Message"), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Send a test ping message"
  }, /*#__PURE__*/react.createElement(components/* SecondaryButton */.tA, {
    onClick: function onClick() {
      if (wsClientRef.current && wsClientRef.current.connectionState === EnhancedWebSocketClient.ConnectionState.OPEN) {
        try {
          var testMessage = {
            type: 'ping',
            timestamp: new Date().toISOString(),
            client: 'EnhancedWebSocketPage'
          };
          wsClientRef.current.send(testMessage, true, {
            urgent: true
          });
          setMessages(function (prev) {
            return [].concat((0,toConsumableArray/* default */.A)(prev), [{
              type: 'sent',
              text: JSON.stringify(testMessage, null, 2),
              timestamp: new Date(),
              isTest: true
            }]);
          });
          announce('Sent test ping message');
        } catch (e) {
          console.error('Failed to send test message:', e);
        }
      }
    },
    disabled: connectionState !== EnhancedWebSocketClient.ConnectionState.OPEN,
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null),
    "aria-label": "Send test ping message"
  }, "Send Ping"))))), /*#__PURE__*/react.createElement(TabPane, {
    tab: "Settings",
    key: "2"
  }, /*#__PURE__*/react.createElement(components/* StyledCard */.ee, {
    title: "Enhanced WebSocket Settings"
  }, /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Auto Reconnect"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: settings.autoReconnect,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        autoReconnect: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, settings.autoReconnect ? 'Enabled - Will automatically try to reconnect when disconnected' : 'Disabled - Will not automatically reconnect'))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Debug Mode"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: settings.debug,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        debug: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, settings.debug ? 'Enabled - Detailed logs will be shown in the console' : 'Disabled - Minimal logging'))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Persist Offline Messages"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: settings.persistOfflineMessages,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        persistOfflineMessages: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, settings.persistOfflineMessages ? 'Enabled - Messages will be saved when offline' : 'Disabled - Messages may be lost when offline'))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Enable Compression"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: settings.enableCompression,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        enableCompression: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, settings.enableCompression ? 'Enabled - Large messages will be compressed' : 'Disabled - No compression'))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Reconnect Interval (ms)"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    min: 1000,
    max: 10000,
    step: 500,
    value: settings.reconnectInterval,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        reconnectInterval: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Time to wait before attempting to reconnect"))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Max Reconnect Attempts"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    min: 1,
    max: 50,
    value: settings.maxReconnectAttempts,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        maxReconnectAttempts: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Maximum number of reconnection attempts"))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Batch Interval (ms)"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    min: 0,
    max: 500,
    value: settings.batchInterval,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        batchInterval: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Time to wait before sending batched messages (0 to disable batching)"))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Max Batch Size"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    min: 1,
    max: 1000,
    value: settings.maxBatchSize,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        maxBatchSize: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Maximum number of messages in a batch"))), /*#__PURE__*/react.createElement(SettingItem, null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Heartbeat Interval (ms)"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      marginTop: '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    min: 5000,
    max: 60000,
    step: 5000,
    value: settings.heartbeatInterval,
    onChange: function onChange(value) {
      return setSettings(_objectSpread(_objectSpread({}, settings), {}, {
        heartbeatInterval: value
      }));
    },
    style: {
      marginRight: '8px'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Time between heartbeat messages to keep the connection alive"))), /*#__PURE__*/react.createElement(Paragraph, null, /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "These settings control the behavior of the Enhanced WebSocket client. Changes will take effect on the next connection."))))));
};
/* harmony default export */ const pages_WebSocketPage = (WebSocketPage);

/***/ }),

/***/ 76413:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(70572);

var _templateObject;


var SkipLinkButton = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.a(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -40px;\n  left: 0;\n  background: ", ";\n  color: white;\n  padding: 8px;\n  z-index: ", ";\n  transition: top 0.3s;\n\n  &:focus {\n    top: 0;\n  }\n"])), function (props) {
  var _props$theme, _props$theme2, _props$theme3;
  // Handle different theme structures
  if ((_props$theme = props.theme) !== null && _props$theme !== void 0 && (_props$theme = _props$theme.colorPalette) !== null && _props$theme !== void 0 && _props$theme.primary) {
    return props.theme.colorPalette.primary;
  }
  if ((_props$theme2 = props.theme) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.colors) !== null && _props$theme2 !== void 0 && (_props$theme2 = _props$theme2.primary) !== null && _props$theme2 !== void 0 && _props$theme2.main) {
    return props.theme.colors.primary.main;
  }
  if ((_props$theme3 = props.theme) !== null && _props$theme3 !== void 0 && _props$theme3.primaryColor) {
    return props.theme.primaryColor;
  }
  // Fallback color
  return '#2563EB';
}, function (props) {
  var _props$theme4, _props$theme5;
  return ((_props$theme4 = props.theme) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.zIndex) === null || _props$theme4 === void 0 ? void 0 : _props$theme4.tooltip) || ((_props$theme5 = props.theme) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.zIndex) === null || _props$theme5 === void 0 ? void 0 : _props$theme5.modal) || 1000;
});

/**
 * SkipLink component
 * Provides a way for keyboard users to skip navigation and go directly to main content
 * This is an accessibility feature that is hidden until focused
 */
var SkipLink = function SkipLink(_ref) {
  var _ref$targetId = _ref.targetId,
    targetId = _ref$targetId === void 0 ? 'main-content' : _ref$targetId;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SkipLinkButton, {
    href: "#".concat(targetId)
  }, "Skip to main content");
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SkipLink);

/***/ }),

/***/ 85331:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   tI: () => (/* binding */ setCurrentView)
/* harmony export */ });
/* unused harmony exports TOGGLE_SIDEBAR, SET_CURRENT_VIEW, TOGGLE_PREVIEW_MODE, UI_LOADING_START, UI_LOADING_COMPLETE, toggleSidebar, togglePreviewMode, startLoading, completeLoading */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UI Reducer
 *
 * This reducer handles UI state, including sidebar, current view, and preview mode.
 */

// Action types
var TOGGLE_SIDEBAR = 'TOGGLE_SIDEBAR';
var SET_CURRENT_VIEW = 'SET_CURRENT_VIEW';
var TOGGLE_PREVIEW_MODE = 'TOGGLE_PREVIEW_MODE';
var UI_LOADING_START = 'UI_LOADING_START';
var UI_LOADING_COMPLETE = 'UI_LOADING_COMPLETE';

// Initial state
var initialState = {
  sidebarOpen: true,
  currentView: 'components',
  previewMode: false,
  loading: false
};

/**
 * UI reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
var uiReducer = function uiReducer() {
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;
  switch (action.type) {
    case TOGGLE_SIDEBAR:
      return _objectSpread(_objectSpread({}, state), {}, {
        sidebarOpen: !state.sidebarOpen
      });
    case SET_CURRENT_VIEW:
      return _objectSpread(_objectSpread({}, state), {}, {
        currentView: action.payload
      });
    case TOGGLE_PREVIEW_MODE:
      return _objectSpread(_objectSpread({}, state), {}, {
        previewMode: !state.previewMode
      });
    case UI_LOADING_START:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: true
      });
    case UI_LOADING_COMPLETE:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: false
      });
    default:
      return state;
  }
};

// Action creators
var toggleSidebar = function toggleSidebar() {
  return {
    type: TOGGLE_SIDEBAR
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: SET_CURRENT_VIEW,
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: TOGGLE_PREVIEW_MODE
  };
};
var startLoading = function startLoading() {
  return {
    type: UI_LOADING_START
  };
};
var completeLoading = function completeLoading() {
  return {
    type: UI_LOADING_COMPLETE
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (uiReducer)));

/***/ })

}]);