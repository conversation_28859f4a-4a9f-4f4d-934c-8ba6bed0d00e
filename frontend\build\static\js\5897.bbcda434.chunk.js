"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5897],{

/***/ 47119:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $l: () => (/* binding */ useEnhancedDragDrop)
/* harmony export */ });
/* unused harmony exports useDragVisualFeedback, useDragReorder */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);




/**
 * Enhanced drag and drop hook with visual feedback and animations
 * @param {Object} options - Configuration options
 * @param {Function} options.onDrop - Callback when item is dropped
 * @param {Function} options.onDragStart - Callback when drag starts
 * @param {Function} options.onDragEnd - Callback when drag ends
 * @param {Function} options.onDragOver - Callback when dragging over
 * @param {Function} options.onDragLeave - Callback when leaving drag area
 * @param {boolean} options.snapToGrid - Enable snap to grid functionality
 * @param {number} options.gridSize - Grid size for snapping
 * @param {boolean} options.showDropZones - Show visual drop zones
 * @param {Array} options.acceptedTypes - Accepted drag data types
 * @returns {Object} Drag and drop state and handlers
 */
var useEnhancedDragDrop = function useEnhancedDragDrop() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var onDrop = options.onDrop,
    onDragStart = options.onDragStart,
    onDragEnd = options.onDragEnd,
    onDragOver = options.onDragOver,
    onDragLeave = options.onDragLeave,
    _options$snapToGrid = options.snapToGrid,
    snapToGrid = _options$snapToGrid === void 0 ? false : _options$snapToGrid,
    _options$gridSize = options.gridSize,
    gridSize = _options$gridSize === void 0 ? 20 : _options$gridSize,
    _options$showDropZone = options.showDropZones,
    showDropZones = _options$showDropZone === void 0 ? true : _options$showDropZone,
    _options$acceptedType = options.acceptedTypes,
    acceptedTypes = _options$acceptedType === void 0 ? ['application/json'] : _options$acceptedType;

  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    isDragging = _useState2[0],
    setIsDragging = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    isOver = _useState4[0],
    setIsOver = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    dragData = _useState6[0],
    setDragData = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      x: 0,
      y: 0
    }),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    dropPosition = _useState8[0],
    setDropPosition = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    validDropZone = _useState0[0],
    setValidDropZone = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    dragPreview = _useState10[0],
    setDragPreview = _useState10[1];

  // Refs
  var dropZoneRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
  var dragPreviewRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Handle drag start
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e, data) {
    setIsDragging(true);
    setDragData(data);

    // Set drag data
    if (data) {
      e.dataTransfer.setData('application/json', JSON.stringify(data));
    }
    e.dataTransfer.effectAllowed = 'copy';

    // Create custom drag preview if provided
    if (dragPreviewRef.current) {
      var preview = dragPreviewRef.current.cloneNode(true);
      preview.style.position = 'absolute';
      preview.style.top = '-1000px';
      preview.style.left = '-1000px';
      preview.style.opacity = '0.8';
      preview.style.transform = 'rotate(5deg) scale(0.9)';
      preview.style.pointerEvents = 'none';
      preview.style.zIndex = '9999';
      document.body.appendChild(preview);
      e.dataTransfer.setDragImage(preview, 50, 25);

      // Clean up preview after drag
      setTimeout(function () {
        if (document.body.contains(preview)) {
          document.body.removeChild(preview);
        }
      }, 0);
    }
    if (onDragStart) {
      onDragStart(e, data);
    }
  }, [onDragStart]);

  // Handle drag end
  var handleDragEnd = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    setIsDragging(false);
    setDragData(null);
    setDropPosition({
      x: 0,
      y: 0
    });
    setValidDropZone(true);
    if (onDragEnd) {
      onDragEnd(e);
    }
  }, [onDragEnd]);

  // Handle drag enter
  var handleDragEnter = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();
    setIsOver(true);
  }, []);

  // Handle drag over
  var handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();

    // Calculate drop position
    if (dropZoneRef.current) {
      var rect = dropZoneRef.current.getBoundingClientRect();
      var x = e.clientX - rect.left;
      var y = e.clientY - rect.top;

      // Apply snap to grid if enabled
      if (snapToGrid) {
        x = Math.round(x / gridSize) * gridSize;
        y = Math.round(y / gridSize) * gridSize;
      }
      setDropPosition({
        x: x,
        y: y
      });
    }

    // Check if drop is valid
    var dragType = e.dataTransfer.types[0];
    var isValidType = acceptedTypes.includes(dragType) || acceptedTypes.length === 0;
    setValidDropZone(isValidType);
    e.dataTransfer.dropEffect = isValidType ? 'copy' : 'none';
    if (onDragOver) {
      onDragOver(e);
    }
  }, [snapToGrid, gridSize, acceptedTypes, onDragOver]);

  // Handle drag leave
  var handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();

    // Only set isOver to false if we're actually leaving the drop zone
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsOver(false);
      setValidDropZone(true);
      if (onDragLeave) {
        onDragLeave(e);
      }
    }
  }, [onDragLeave]);

  // Handle drop
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();
    setIsOver(false);
    setValidDropZone(true);
    try {
      // Get dropped data
      var jsonData = e.dataTransfer.getData('application/json');
      var droppedData = null;
      if (jsonData) {
        droppedData = JSON.parse(jsonData);
      }

      // Calculate final position
      var finalPosition = dropPosition;
      if (snapToGrid) {
        finalPosition = {
          x: Math.round(dropPosition.x / gridSize) * gridSize,
          y: Math.round(dropPosition.y / gridSize) * gridSize
        };
      }
      if (onDrop) {
        onDrop(e, droppedData, finalPosition);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  }, [dropPosition, snapToGrid, gridSize, onDrop]);

  // Set up event listeners
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var dropZone = dropZoneRef.current;
    if (!dropZone) return;
    dropZone.addEventListener('dragenter', handleDragEnter);
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    return function () {
      dropZone.removeEventListener('dragenter', handleDragEnter);
      dropZone.removeEventListener('dragover', handleDragOver);
      dropZone.removeEventListener('dragleave', handleDragLeave);
      dropZone.removeEventListener('drop', handleDrop);
    };
  }, [handleDragEnter, handleDragOver, handleDragLeave, handleDrop]);
  return {
    // State
    isDragging: isDragging,
    isOver: isOver,
    dragData: dragData,
    dropPosition: dropPosition,
    validDropZone: validDropZone,
    // Refs
    dropZoneRef: dropZoneRef,
    dragPreviewRef: dragPreviewRef,
    // Handlers
    handleDragStart: handleDragStart,
    handleDragEnd: handleDragEnd,
    // Utilities
    reset: function reset() {
      setIsDragging(false);
      setIsOver(false);
      setDragData(null);
      setDropPosition({
        x: 0,
        y: 0
      });
      setValidDropZone(true);
    }
  };
};

/**
 * Hook for managing drag visual feedback
 * @param {Object} options - Configuration options
 * @returns {Object} Visual feedback utilities
 */
var useDragVisualFeedback = function useDragVisualFeedback() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$showGhost = options.showGhost,
    showGhost = _options$showGhost === void 0 ? true : _options$showGhost,
    _options$showDropIndi = options.showDropIndicator,
    showDropIndicator = _options$showDropIndi === void 0 ? true : _options$showDropIndi,
    _options$animationDur = options.animationDuration,
    animationDuration = _options$animationDur === void 0 ? 300 : _options$animationDur;
  var _useState11 = useState({
      x: 0,
      y: 0
    }),
    _useState12 = _slicedToArray(_useState11, 2),
    ghostPosition = _useState12[0],
    setGhostPosition = _useState12[1];
  var _useState13 = useState(false),
    _useState14 = _slicedToArray(_useState13, 2),
    showGhostElement = _useState14[0],
    setShowGhostElement = _useState14[1];
  var _useState15 = useState(null),
    _useState16 = _slicedToArray(_useState15, 2),
    dropIndicatorPosition = _useState16[0],
    setDropIndicatorPosition = _useState16[1];

  // Update ghost position during drag
  var updateGhostPosition = useCallback(function (x, y) {
    if (showGhost) {
      setGhostPosition({
        x: x,
        y: y
      });
    }
  }, [showGhost]);

  // Show/hide ghost element
  var toggleGhost = useCallback(function (show) {
    setShowGhostElement(show);
  }, []);

  // Update drop indicator
  var updateDropIndicator = useCallback(function (position) {
    if (showDropIndicator) {
      setDropIndicatorPosition(position);
    }
  }, [showDropIndicator]);

  // Clear all visual feedback
  var clearFeedback = useCallback(function () {
    setShowGhostElement(false);
    setDropIndicatorPosition(null);
    setGhostPosition({
      x: 0,
      y: 0
    });
  }, []);
  return {
    ghostPosition: ghostPosition,
    showGhostElement: showGhostElement,
    dropIndicatorPosition: dropIndicatorPosition,
    updateGhostPosition: updateGhostPosition,
    toggleGhost: toggleGhost,
    updateDropIndicator: updateDropIndicator,
    clearFeedback: clearFeedback
  };
};

/**
 * Hook for managing component reordering with drag and drop
 * @param {Array} items - Array of items to reorder
 * @param {Function} onReorder - Callback when items are reordered
 * @returns {Object} Reordering utilities
 */
var useDragReorder = function useDragReorder(items, onReorder) {
  var _useState17 = useState(null),
    _useState18 = _slicedToArray(_useState17, 2),
    draggedItem = _useState18[0],
    setDraggedItem = _useState18[1];
  var _useState19 = useState(null),
    _useState20 = _slicedToArray(_useState19, 2),
    draggedOverItem = _useState20[0],
    setDraggedOverItem = _useState20[1];
  var _useState21 = useState('after'),
    _useState22 = _slicedToArray(_useState21, 2),
    dropPosition = _useState22[0],
    setDropPosition = _useState22[1]; // 'before' or 'after'

  var handleDragStart = useCallback(function (e, item) {
    setDraggedItem(item);
    e.dataTransfer.setData('application/json', JSON.stringify(item));
    e.dataTransfer.effectAllowed = 'move';
  }, []);
  var handleDragOver = useCallback(function (e, item) {
    e.preventDefault();
    if (draggedItem && draggedItem.id !== item.id) {
      setDraggedOverItem(item);

      // Determine drop position based on mouse position
      var rect = e.currentTarget.getBoundingClientRect();
      var midpoint = rect.top + rect.height / 2;
      setDropPosition(e.clientY < midpoint ? 'before' : 'after');
    }
    e.dataTransfer.dropEffect = 'move';
  }, [draggedItem]);
  var handleDrop = useCallback(function (e, targetItem) {
    e.preventDefault();
    if (draggedItem && targetItem && draggedItem.id !== targetItem.id) {
      var draggedIndex = items.findIndex(function (item) {
        return item.id === draggedItem.id;
      });
      var targetIndex = items.findIndex(function (item) {
        return item.id === targetItem.id;
      });
      if (draggedIndex !== -1 && targetIndex !== -1) {
        var newItems = _toConsumableArray(items);
        var _newItems$splice = newItems.splice(draggedIndex, 1),
          _newItems$splice2 = _slicedToArray(_newItems$splice, 1),
          removed = _newItems$splice2[0];
        var insertIndex = dropPosition === 'before' ? targetIndex : targetIndex + 1;
        newItems.splice(insertIndex, 0, removed);
        if (onReorder) {
          onReorder(newItems);
        }
      }
    }
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, [items, draggedItem, dropPosition, onReorder]);
  var handleDragEnd = useCallback(function () {
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, []);
  return {
    draggedItem: draggedItem,
    draggedOverItem: draggedOverItem,
    dropPosition: dropPosition,
    handleDragStart: handleDragStart,
    handleDragOver: handleDragOver,
    handleDrop: handleDrop,
    handleDragEnd: handleDragEnd
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useEnhancedDragDrop)));

/***/ }),

/***/ 48860:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/**
 * Custom hook for optimizing preview performance
 * Handles virtual rendering, component caching, and performance monitoring
 */
var usePreviewPerformance = function usePreviewPerformance(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$containerHeight = _ref.containerHeight,
    containerHeight = _ref$containerHeight === void 0 ? 600 : _ref$containerHeight,
    _ref$itemHeight = _ref.itemHeight,
    itemHeight = _ref$itemHeight === void 0 ? 100 : _ref$itemHeight,
    _ref$overscan = _ref.overscan,
    overscan = _ref$overscan === void 0 ? 5 : _ref$overscan,
    _ref$enableVirtualiza = _ref.enableVirtualization,
    enableVirtualization = _ref$enableVirtualiza === void 0 ? true : _ref$enableVirtualiza,
    _ref$enablePerformanc = _ref.enablePerformanceMonitoring,
    enablePerformanceMonitoring = _ref$enablePerformanc === void 0 ? true : _ref$enablePerformanc;
  // State for virtualization
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    scrollTop = _useState2[0],
    setScrollTop = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    containerRef = _useState4[0],
    setContainerRef = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      start: 0,
      end: 0
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    visibleRange = _useState6[0],
    setVisibleRange = _useState6[1];

  // Performance monitoring state
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    renderTime = _useState8[0],
    setRenderTime = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(60),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    frameRate = _useState0[0],
    setFrameRate = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    memoryUsage = _useState10[0],
    setMemoryUsage = _useState10[1];

  // Refs for performance tracking
  var renderStartTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var frameCount = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var lastFrameTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(performance.now());
  var componentCache = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Map());
  var intersectionObserver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Calculate visible items for virtualization
  var calculateVisibleRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization || !containerRef || components.length === 0) {
      return {
        start: 0,
        end: components.length
      };
    }
    var startIndex = Math.floor(scrollTop / itemHeight);
    var endIndex = Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + overscan, components.length);
    return {
      start: Math.max(0, startIndex - overscan),
      end: endIndex
    };
  }, [scrollTop, itemHeight, containerHeight, overscan, components.length, enableVirtualization, containerRef]);

  // Update visible range when scroll changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var newRange = calculateVisibleRange();
    setVisibleRange(newRange);
  }, [calculateVisibleRange]);

  // Throttled scroll handler
  var handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_3__.throttle)(function (event) {
    if (event.target) {
      setScrollTop(event.target.scrollTop);
    }
  }, 16),
  // ~60fps
  []);

  // Get visible components for rendering
  var visibleComponents = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    if (!enableVirtualization) {
      return components.map(function (component, index) {
        return {
          component: component,
          index: index
        };
      });
    }
    return components.slice(visibleRange.start, visibleRange.end).map(function (component, relativeIndex) {
      return {
        component: component,
        index: visibleRange.start + relativeIndex
      };
    });
  }, [components, visibleRange, enableVirtualization]);

  // Component caching for performance
  var getCachedComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentId, renderFunction) {
    var cacheKey = "".concat(componentId, "_").concat(JSON.stringify(components.find(function (c) {
      return c.id === componentId;
    })));
    if (componentCache.current.has(cacheKey)) {
      return componentCache.current.get(cacheKey);
    }
    var renderedComponent = renderFunction();
    componentCache.current.set(cacheKey, renderedComponent);

    // Limit cache size to prevent memory leaks
    if (componentCache.current.size > 100) {
      var firstKey = componentCache.current.keys().next().value;
      componentCache.current["delete"](firstKey);
    }
    return renderedComponent;
  }, [components]);

  // Performance monitoring
  var startRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring) {
      renderStartTime.current = performance.now();
    }
  }, [enablePerformanceMonitoring]);
  var endRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring && renderStartTime.current > 0) {
      var renderDuration = performance.now() - renderStartTime.current;
      setRenderTime(renderDuration);
      renderStartTime.current = 0;
    }
  }, [enablePerformanceMonitoring]);

  // Frame rate monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring) return;
    var animationId;
    var _measureFrameRate = function measureFrameRate() {
      var now = performance.now();
      var delta = now - lastFrameTime.current;
      if (delta >= 1000) {
        var fps = Math.round(frameCount.current * 1000 / delta);
        setFrameRate(fps);
        frameCount.current = 0;
        lastFrameTime.current = now;
      } else {
        frameCount.current++;
      }
      animationId = requestAnimationFrame(_measureFrameRate);
    };
    animationId = requestAnimationFrame(_measureFrameRate);
    return function () {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enablePerformanceMonitoring]);

  // Memory usage monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring || !performance.memory) return;
    var measureMemory = function measureMemory() {
      var memoryInfo = performance.memory;
      var usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
      setMemoryUsage(usedMB);
    };
    var interval = setInterval(measureMemory, 5000);
    measureMemory(); // Initial measurement

    return function () {
      return clearInterval(interval);
    };
  }, [enablePerformanceMonitoring]);

  // Intersection Observer for lazy loading
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enableVirtualization) return;
    intersectionObserver.current = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          // Component is visible, ensure it's rendered
          var componentId = entry.target.dataset.componentId;
          if (componentId) {
            // Trigger re-render if needed
          }
        }
      });
    }, {
      root: containerRef,
      rootMargin: "".concat(overscan * itemHeight, "px"),
      threshold: 0.1
    });
    return function () {
      if (intersectionObserver.current) {
        intersectionObserver.current.disconnect();
      }
    };
  }, [containerRef, overscan, itemHeight, enableVirtualization]);

  // Clear cache when components change significantly
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var componentIds = new Set(components.map(function (c) {
      return c.id;
    }));
    var cachedIds = new Set(Array.from(componentCache.current.keys()).map(function (key) {
      return key.split('_')[0];
    }));

    // Remove cached components that no longer exist
    cachedIds.forEach(function (cachedId) {
      if (!componentIds.has(cachedId)) {
        Array.from(componentCache.current.keys()).filter(function (key) {
          return key.startsWith(cachedId);
        }).forEach(function (key) {
          return componentCache.current["delete"](key);
        });
      }
    });
  }, [components]);

  // Get container props for virtualization
  var getContainerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {};
    }
    return {
      ref: setContainerRef,
      onScroll: handleScroll,
      style: {
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }
    };
  }, [enableVirtualization, containerHeight, handleScroll]);

  // Get spacer props for virtual scrolling
  var getSpacerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {
        before: {},
        after: {}
      };
    }
    var totalHeight = components.length * itemHeight;
    var beforeHeight = visibleRange.start * itemHeight;
    var afterHeight = totalHeight - visibleRange.end * itemHeight;
    return {
      before: {
        style: {
          height: beforeHeight,
          width: '100%'
        }
      },
      after: {
        style: {
          height: afterHeight,
          width: '100%'
        }
      }
    };
  }, [enableVirtualization, components.length, itemHeight, visibleRange]);

  // Performance optimization utilities
  var optimizationUtils = {
    clearCache: function clearCache() {
      return componentCache.current.clear();
    },
    getCacheSize: function getCacheSize() {
      return componentCache.current.size;
    },
    getPerformanceMetrics: function getPerformanceMetrics() {
      return {
        renderTime: renderTime,
        frameRate: frameRate,
        memoryUsage: memoryUsage,
        cacheSize: componentCache.current.size,
        visibleComponents: visibleComponents.length,
        totalComponents: components.length
      };
    },
    shouldRender: function shouldRender(componentId) {
      // Check if component should be rendered based on visibility
      if (!enableVirtualization) return true;
      var componentIndex = components.findIndex(function (c) {
        return c.id === componentId;
      });
      return componentIndex >= visibleRange.start && componentIndex < visibleRange.end;
    }
  };
  return _objectSpread({
    // Virtualization
    visibleComponents: visibleComponents,
    visibleRange: visibleRange,
    getContainerProps: getContainerProps,
    getSpacerProps: getSpacerProps,
    // Performance monitoring
    renderTime: renderTime,
    frameRate: frameRate,
    memoryUsage: memoryUsage,
    startRenderMeasurement: startRenderMeasurement,
    endRenderMeasurement: endRenderMeasurement,
    // Caching
    getCachedComponent: getCachedComponent
  }, optimizationUtils);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (usePreviewPerformance);

/***/ }),

/***/ 87169:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useAIDesignSuggestions */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71468);
/* harmony import */ var _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(86329);
/* harmony import */ var _redux_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(81616);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






/**
 * Custom hook for managing AI design suggestions
 * Provides layout suggestions, component combinations, and app analysis
 */
var useAIDesignSuggestions = function useAIDesignSuggestions() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$autoRefresh = options.autoRefresh,
    autoRefresh = _options$autoRefresh === void 0 ? true : _options$autoRefresh,
    _options$refreshInter = options.refreshInterval,
    refreshInterval = _options$refreshInter === void 0 ? 30000 : _options$refreshInter,
    _options$enableCache = options.enableCache,
    enableCache = _options$enableCache === void 0 ? true : _options$enableCache,
    _options$context = options.context,
    context = _options$context === void 0 ? {} : _options$context;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useDispatch */ .wA)();

  // Get app state from Redux
  var components = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$app;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || state.components || [];
  });
  var layouts = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$app2;
    return ((_state$app2 = state.app) === null || _state$app2 === void 0 ? void 0 : _state$app2.layouts) || state.layouts || [];
  });
  var selectedComponent = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$ui;
    return ((_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.selectedComponent) || null;
  });

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      layout: [],
      combinations: [],
      analysis: null
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    suggestions = _useState2[0],
    setSuggestions = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      layout: false,
      combinations: false,
      analysis: false
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    lastRefresh = _useState8[0],
    setLastRefresh = _useState8[1];

  // Refs for cleanup
  var refreshIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);

  // Load all suggestions
  var loadSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
    var force,
      _yield$Promise$allSet,
      _yield$Promise$allSet2,
      layoutResponse,
      combinationsResponse,
      analysisResponse,
      layoutSuggestions,
      combinationSuggestions,
      analysis,
      _args = arguments;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          force = _args.length > 0 && _args[0] !== undefined ? _args[0] : false;
          if (!(!components || components.length === 0)) {
            _context.next = 4;
            break;
          }
          setSuggestions({
            layout: [],
            combinations: [],
            analysis: null
          });
          return _context.abrupt("return");
        case 4:
          // Abort previous requests
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          abortControllerRef.current = new AbortController();
          setError(null);
          setLoading({
            layout: true,
            combinations: true,
            analysis: true
          });
          _context.prev = 8;
          // Clear cache if force refresh
          if (force && enableCache) {
            _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.clearCache();
          }

          // Load all suggestions in parallel
          _context.next = 12;
          return Promise.allSettled([_services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateLayoutSuggestions(components, layouts, context), _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateComponentCombinations(components, selectedComponent, context), _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.analyzeAppStructure(components, layouts)]);
        case 12:
          _yield$Promise$allSet = _context.sent;
          _yield$Promise$allSet2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_yield$Promise$allSet, 3);
          layoutResponse = _yield$Promise$allSet2[0];
          combinationsResponse = _yield$Promise$allSet2[1];
          analysisResponse = _yield$Promise$allSet2[2];
          // Process layout suggestions
          layoutSuggestions = layoutResponse.status === 'fulfilled' ? layoutResponse.value.suggestions || [] : []; // Process combination suggestions
          combinationSuggestions = combinationsResponse.status === 'fulfilled' ? combinationsResponse.value.suggestions || [] : []; // Process analysis
          analysis = analysisResponse.status === 'fulfilled' ? analysisResponse.value.analysis || null : null;
          setSuggestions({
            layout: layoutSuggestions,
            combinations: combinationSuggestions,
            analysis: analysis
          });
          setLastRefresh(new Date());

          // Log any errors
          [layoutResponse, combinationsResponse, analysisResponse].forEach(function (response, index) {
            if (response.status === 'rejected') {
              var names = ['layout', 'combinations', 'analysis'];
              console.warn("Failed to load ".concat(names[index], " suggestions:"), response.reason);
            }
          });
          _context.next = 28;
          break;
        case 25:
          _context.prev = 25;
          _context.t0 = _context["catch"](8);
          if (_context.t0.name !== 'AbortError') {
            setError("Failed to load suggestions: ".concat(_context.t0.message));
          }
        case 28:
          _context.prev = 28;
          setLoading({
            layout: false,
            combinations: false,
            analysis: false
          });
          return _context.finish(28);
        case 31:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[8, 25, 28, 31]]);
  })), [components, layouts, selectedComponent, context, enableCache]);

  // Load specific suggestion type
  var loadLayoutSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2() {
    var response;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context2.next = 2;
            break;
          }
          return _context2.abrupt("return");
        case 2:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: true
            });
          });
          setError(null);
          _context2.prev = 4;
          _context2.next = 7;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateLayoutSuggestions(components, layouts, context);
        case 7:
          response = _context2.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: response.suggestions || []
            });
          });
          _context2.next = 14;
          break;
        case 11:
          _context2.prev = 11;
          _context2.t0 = _context2["catch"](4);
          setError("Failed to load layout suggestions: ".concat(_context2.t0.message));
        case 14:
          _context2.prev = 14;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: false
            });
          });
          return _context2.finish(14);
        case 17:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[4, 11, 14, 17]]);
  })), [components, layouts, context]);
  var loadCombinationSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3() {
    var response;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context3.next = 2;
            break;
          }
          return _context3.abrupt("return");
        case 2:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: true
            });
          });
          setError(null);
          _context3.prev = 4;
          _context3.next = 7;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateComponentCombinations(components, selectedComponent, context);
        case 7:
          response = _context3.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: response.suggestions || []
            });
          });
          _context3.next = 14;
          break;
        case 11:
          _context3.prev = 11;
          _context3.t0 = _context3["catch"](4);
          setError("Failed to load combination suggestions: ".concat(_context3.t0.message));
        case 14:
          _context3.prev = 14;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: false
            });
          });
          return _context3.finish(14);
        case 17:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[4, 11, 14, 17]]);
  })), [components, selectedComponent, context]);

  // Apply layout suggestion
  var applyLayoutSuggestion = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    try {
      // This would integrate with your layout system
      // For now, we'll dispatch a generic action
      console.log('Applying layout suggestion:', suggestion);

      // You could dispatch a specific action here
      // dispatch(applyLayout(suggestion));

      return true;
    } catch (err) {
      setError("Failed to apply layout suggestion: ".concat(err.message));
      return false;
    }
  }, []);

  // Apply component combination suggestion
  var applyComponentCombination = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    try {
      if (suggestion.missing_components && suggestion.missing_components.length > 0) {
        // Add missing components
        suggestion.missing_components.forEach(function (componentType) {
          var newComponent = {
            type: componentType,
            props: {},
            id: "".concat(componentType, "-").concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9))
          };
          dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_7__/* .addComponent */ .X8)(newComponent.type, newComponent.props));
        });
      }
      console.log('Applied component combination:', suggestion);
      return true;
    } catch (err) {
      setError("Failed to apply component combination: ".concat(err.message));
      return false;
    }
  }, [dispatch]);

  // Refresh suggestions
  var refresh = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    loadSuggestions(true);
  }, [loadSuggestions]);

  // Clear error
  var clearError = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    setError(null);
  }, []);

  // Setup auto-refresh
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(function () {
        loadSuggestions();
      }, refreshInterval);
      return function () {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, loadSuggestions]);

  // Load suggestions when dependencies change
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    loadSuggestions();
  }, [loadSuggestions]);

  // Cleanup on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    return function () {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  return {
    // Data
    suggestions: suggestions,
    loading: loading,
    error: error,
    lastRefresh: lastRefresh,
    // Actions
    loadSuggestions: loadSuggestions,
    loadLayoutSuggestions: loadLayoutSuggestions,
    loadCombinationSuggestions: loadCombinationSuggestions,
    applyLayoutSuggestion: applyLayoutSuggestion,
    applyComponentCombination: applyComponentCombination,
    refresh: refresh,
    clearError: clearError,
    // Computed values
    hasLayoutSuggestions: suggestions.layout.length > 0,
    hasCombinationSuggestions: suggestions.combinations.length > 0,
    hasAnalysis: suggestions.analysis !== null,
    isLoading: loading.layout || loading.combinations || loading.analysis,
    // Component counts for display
    componentCount: components.length,
    layoutCount: layouts.length,
    selectedComponentType: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.type) || null
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAIDesignSuggestions);

/***/ }),

/***/ 94588:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cd: () => (/* binding */ useSelection),
/* harmony export */   EF: () => (/* binding */ useContextMenu),
/* harmony export */   KW: () => (/* binding */ useKeyboardShortcuts),
/* harmony export */   R2: () => (/* binding */ useLoadingState),
/* harmony export */   aD: () => (/* binding */ useUndoRedo),
/* harmony export */   iD: () => (/* binding */ useClipboard)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/**
 * Hook for managing undo/redo functionality
 * @param {*} initialState - Initial state value
 * @param {number} maxHistorySize - Maximum number of history entries to keep
 * @returns {Object} State and undo/redo functions
 */
var useUndoRedo = function useUndoRedo(initialState) {
  var maxHistorySize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 50;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([initialState]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    history = _useState2[0],
    setHistory = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    currentIndex = _useState4[0],
    setCurrentIndex = _useState4[1];
  var isUndoRedoAction = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);

  // Get current state
  var currentState = history[currentIndex];

  // Push new state to history
  var pushState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newState) {
    if (isUndoRedoAction.current) {
      isUndoRedoAction.current = false;
      return;
    }
    setHistory(function (prev) {
      var newHistory = prev.slice(0, currentIndex + 1);
      newHistory.push(newState);

      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        return newHistory;
      }
      return newHistory;
    });
    setCurrentIndex(function (prev) {
      var newIndex = Math.min(prev + 1, maxHistorySize - 1);
      return newIndex;
    });
  }, [currentIndex, maxHistorySize]);

  // Undo function
  var undo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex > 0) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev - 1;
      });
      return history[currentIndex - 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Redo function
  var redo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex < history.length - 1) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev + 1;
      });
      return history[currentIndex + 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Check if undo is available
  var canUndo = currentIndex > 0;

  // Check if redo is available
  var canRedo = currentIndex < history.length - 1;

  // Clear history
  var clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setHistory([currentState]);
    setCurrentIndex(0);
  }, [currentState]);

  // Get history info
  var getHistoryInfo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return {
      totalStates: history.length,
      currentIndex: currentIndex,
      canUndo: canUndo,
      canRedo: canRedo
    };
  }, [history.length, currentIndex, canUndo, canRedo]);
  return {
    state: currentState,
    pushState: pushState,
    undo: undo,
    redo: redo,
    canUndo: canUndo,
    canRedo: canRedo,
    clearHistory: clearHistory,
    getHistoryInfo: getHistoryInfo
  };
};

/**
 * Hook for keyboard shortcuts
 * @param {Object} shortcuts - Object mapping key combinations to functions
 * @param {Array} dependencies - Dependencies for the effect
 */
var useKeyboardShortcuts = function useKeyboardShortcuts(shortcuts) {
  var dependencies = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleKeyDown = function handleKeyDown(event) {
      var ctrlKey = event.ctrlKey,
        metaKey = event.metaKey,
        shiftKey = event.shiftKey,
        altKey = event.altKey,
        key = event.key;

      // Create key combination string
      var modifiers = [];
      if (ctrlKey || metaKey) modifiers.push('ctrl');
      if (shiftKey) modifiers.push('shift');
      if (altKey) modifiers.push('alt');
      var combination = [].concat(modifiers, [key.toLowerCase()]).join('+');

      // Check if combination exists in shortcuts
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination](event);
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, dependencies);
};

/**
 * Hook for managing contextual menus
 * @returns {Object} Context menu state and functions
 */
var useContextMenu = function useContextMenu() {
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      visible: false,
      x: 0,
      y: 0,
      items: []
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    contextMenu = _useState6[0],
    setContextMenu = _useState6[1];
  var showContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (event, items) {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      items: items || []
    });
  }, []);
  var hideContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setContextMenu(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        visible: false
      });
    });
  }, []);

  // Hide context menu when clicking outside
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleClick = function handleClick() {
      if (contextMenu.visible) {
        hideContextMenu();
      }
    };
    document.addEventListener('click', handleClick);
    return function () {
      document.removeEventListener('click', handleClick);
    };
  }, [contextMenu.visible, hideContextMenu]);
  return {
    contextMenu: contextMenu,
    showContextMenu: showContextMenu,
    hideContextMenu: hideContextMenu
  };
};

/**
 * Hook for managing loading states with debouncing
 * @param {number} delay - Delay before showing loading state
 * @returns {Object} Loading state and functions
 */
var useLoadingState = function useLoadingState() {
  var delay = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 200;
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    isLoading = _useState8[0],
    setIsLoading = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    loadingMessage = _useState0[0],
    setLoadingMessage = _useState0[1];
  var timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
  var startLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    var message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Loading...';
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(function () {
      setIsLoading(true);
      setLoadingMessage(message);
    }, delay);
  }, [delay]);
  var stopLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsLoading(false);
    setLoadingMessage('');
  }, []);

  // Cleanup timeout on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    return function () {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  return {
    isLoading: isLoading,
    loadingMessage: loadingMessage,
    startLoading: startLoading,
    stopLoading: stopLoading
  };
};

/**
 * Hook for managing component selection with multi-select support
 * @param {Array} items - Array of selectable items
 * @returns {Object} Selection state and functions
 */
var useSelection = function useSelection() {
  var items = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set()),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    selectedItems = _useState10[0],
    setSelectedItems = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
    lastSelectedIndex = _useState12[0],
    setLastSelectedIndex = _useState12[1];
  var selectItem = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var multiSelect = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    setSelectedItems(function (prev) {
      var newSelection = new Set(multiSelect ? prev : []);
      if (newSelection.has(item.id)) {
        newSelection["delete"](item.id);
      } else {
        newSelection.add(item.id);
      }
      return newSelection;
    });
    setLastSelectedIndex(itemIndex);
  }, [items]);
  var selectRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    if (lastSelectedIndex !== -1) {
      var start = Math.min(lastSelectedIndex, itemIndex);
      var end = Math.max(lastSelectedIndex, itemIndex);
      setSelectedItems(function (prev) {
        var newSelection = new Set(prev);
        for (var i = start; i <= end; i++) {
          if (items[i]) {
            newSelection.add(items[i].id);
          }
        }
        return newSelection;
      });
    } else {
      selectItem(item);
    }
  }, [items, lastSelectedIndex, selectItem]);
  var selectAll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set(items.map(function (item) {
      return item.id;
    })));
  }, [items]);
  var clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set());
    setLastSelectedIndex(-1);
  }, []);
  var isSelected = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (itemId) {
    return selectedItems.has(itemId);
  }, [selectedItems]);
  var getSelectedItems = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return items.filter(function (item) {
      return selectedItems.has(item.id);
    });
  }, [items, selectedItems]);
  return {
    selectedItems: Array.from(selectedItems),
    selectItem: selectItem,
    selectRange: selectRange,
    selectAll: selectAll,
    clearSelection: clearSelection,
    isSelected: isSelected,
    getSelectedItems: getSelectedItems,
    selectedCount: selectedItems.size
  };
};

/**
 * Hook for managing clipboard operations
 * @returns {Object} Clipboard functions
 */
var useClipboard = function useClipboard() {
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState13, 2),
    clipboardData = _useState14[0],
    setClipboardData = _useState14[1];
  var copy = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (data) {
    setClipboardData(data);

    // Also copy to system clipboard if possible
    if (navigator.clipboard && typeof data === 'string') {
      navigator.clipboard.writeText(data)["catch"](console.error);
    }
  }, []);
  var paste = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return clipboardData;
  }, [clipboardData]);
  var clear = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setClipboardData(null);
  }, []);
  var hasData = clipboardData !== null;
  return {
    copy: copy,
    paste: paste,
    clear: clear,
    hasData: hasData,
    data: clipboardData
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useUndoRedo)));

/***/ })

}]);