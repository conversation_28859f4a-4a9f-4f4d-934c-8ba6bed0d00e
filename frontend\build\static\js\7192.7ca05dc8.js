"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7192],{

/***/ 16370:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _grid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(36768);
"use client";


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_grid__WEBPACK_IMPORTED_MODULE_0__/* .Col */ .fv);

/***/ }),

/***/ 19911:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ol: () => (/* binding */ toHexFormat),
/* harmony export */   kf: () => (/* binding */ AggregationColor)
/* harmony export */ });
/* unused harmony export getHex */
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(92901);
/* harmony import */ var _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(71021);



const toHexFormat = (value, alpha) => (value === null || value === void 0 ? void 0 : value.replace(/[^\w/]/g, '').slice(0, alpha ? 8 : 6)) || '';
const getHex = (value, alpha) => value ? toHexFormat(value, alpha) : '';
let AggregationColor = /*#__PURE__*/function () {
  function AggregationColor(color) {
    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this, AggregationColor);
    var _a;
    this.cleared = false;
    // Clone from another AggregationColor
    if (color instanceof AggregationColor) {
      this.metaColor = color.metaColor.clone();
      this.colors = (_a = color.colors) === null || _a === void 0 ? void 0 : _a.map(info => ({
        color: new AggregationColor(info.color),
        percent: info.percent
      }));
      this.cleared = color.cleared;
      return;
    }
    const isArray = Array.isArray(color);
    if (isArray && color.length) {
      this.colors = color.map(({
        color: c,
        percent
      }) => ({
        color: new AggregationColor(c),
        percent
      }));
      this.metaColor = new _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_2__/* .Color */ .Q1(this.colors[0].color.metaColor);
    } else {
      this.metaColor = new _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_2__/* .Color */ .Q1(isArray ? '' : color);
    }
    if (!color || isArray && !this.colors) {
      this.metaColor = this.metaColor.setA(0);
      this.cleared = true;
    }
  }
  return (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(AggregationColor, [{
    key: "toHsb",
    value: function toHsb() {
      return this.metaColor.toHsb();
    }
  }, {
    key: "toHsbString",
    value: function toHsbString() {
      return this.metaColor.toHsbString();
    }
  }, {
    key: "toHex",
    value: function toHex() {
      return getHex(this.toHexString(), this.metaColor.a < 1);
    }
  }, {
    key: "toHexString",
    value: function toHexString() {
      return this.metaColor.toHexString();
    }
  }, {
    key: "toRgb",
    value: function toRgb() {
      return this.metaColor.toRgb();
    }
  }, {
    key: "toRgbString",
    value: function toRgbString() {
      return this.metaColor.toRgbString();
    }
  }, {
    key: "isGradient",
    value: function isGradient() {
      return !!this.colors && !this.cleared;
    }
  }, {
    key: "getColors",
    value: function getColors() {
      return this.colors || [{
        color: this,
        percent: 0
      }];
    }
  }, {
    key: "toCssString",
    value: function toCssString() {
      const {
        colors
      } = this;
      // CSS line-gradient
      if (colors) {
        const colorsStr = colors.map(c => `${c.color.toRgbString()} ${c.percent}%`).join(', ');
        return `linear-gradient(90deg, ${colorsStr})`;
      }
      return this.metaColor.toRgbString();
    }
  }, {
    key: "equals",
    value: function equals(color) {
      if (!color || this.isGradient() !== color.isGradient()) {
        return false;
      }
      if (!this.isGradient()) {
        return this.toHexString() === color.toHexString();
      }
      return this.colors.length === color.colors.length && this.colors.every((c, i) => {
        const target = color.colors[i];
        return c.percent === target.percent && c.color.equals(target.color);
      });
    }
  }]);
}();

/***/ }),

/***/ 35307:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ color_picker)
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(12533);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/ContextIsolator.js
var ContextIsolator = __webpack_require__(62897);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/PurePanel.js
var PurePanel = __webpack_require__(53425);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/statusUtils.js
var statusUtils = __webpack_require__(58182);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/warning.js
var warning = __webpack_require__(18877);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(62279);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/DisabledContext.js
var DisabledContext = __webpack_require__(98119);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useCSSVarCls.js
var useCSSVarCls = __webpack_require__(20934);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useSize.js
var useSize = __webpack_require__(829);
// EXTERNAL MODULE: ./node_modules/antd/es/form/context.js
var form_context = __webpack_require__(94241);
// EXTERNAL MODULE: ./node_modules/antd/es/popover/index.js
var popover = __webpack_require__(28073);
// EXTERNAL MODULE: ./node_modules/antd/es/space/Compact.js
var Compact = __webpack_require__(76327);
// EXTERNAL MODULE: ./node_modules/antd/es/color-picker/color.js
var color = __webpack_require__(19911);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(36552);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@rc-component/color-picker/es/index.js + 14 modules
var es = __webpack_require__(71021);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useLayoutEffect.js
var useLayoutEffect = __webpack_require__(30981);
// EXTERNAL MODULE: ./node_modules/antd/es/segmented/index.js + 1 modules
var segmented = __webpack_require__(25339);
;// ./node_modules/antd/es/color-picker/context.js

const PanelPickerContext = /*#__PURE__*/react.createContext({});
const PanelPresetsContext = /*#__PURE__*/react.createContext({});
// EXTERNAL MODULE: ./node_modules/antd/es/color-picker/util.js
var util = __webpack_require__(36058);
;// ./node_modules/antd/es/color-picker/components/ColorClear.js
"use client";



const ColorClear = ({
  prefixCls,
  value,
  onChange
}) => {
  const handleClick = () => {
    if (onChange && value && !value.cleared) {
      const hsba = value.toHsb();
      hsba.a = 0;
      const genColor = (0,util/* generateColor */.Z6)(hsba);
      genColor.cleared = true;
      onChange(genColor);
    }
  };
  return /*#__PURE__*/react.createElement("div", {
    className: `${prefixCls}-clear`,
    onClick: handleClick
  });
};
/* harmony default export */ const components_ColorClear = (ColorClear);
// EXTERNAL MODULE: ./node_modules/antd/es/select/index.js
var es_select = __webpack_require__(36492);
;// ./node_modules/antd/es/color-picker/interface.js
const FORMAT_HEX = 'hex';
const FORMAT_RGB = 'rgb';
const FORMAT_HSB = 'hsb';
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 2 modules
var input_number = __webpack_require__(7142);
;// ./node_modules/antd/es/color-picker/components/ColorSteppers.js
"use client";




const ColorSteppers = ({
  prefixCls,
  min = 0,
  max = 100,
  value,
  onChange,
  className,
  formatter
}) => {
  const colorSteppersPrefixCls = `${prefixCls}-steppers`;
  const [internalValue, setInternalValue] = (0,react.useState)(0);
  const stepValue = !Number.isNaN(value) ? value : internalValue;
  return /*#__PURE__*/react.createElement(input_number/* default */.A, {
    className: classnames_default()(colorSteppersPrefixCls, className),
    min: min,
    max: max,
    value: stepValue,
    formatter: formatter,
    size: "small",
    onChange: step => {
      setInternalValue(step || 0);
      onChange === null || onChange === void 0 ? void 0 : onChange(step);
    }
  });
};
/* harmony default export */ const components_ColorSteppers = (ColorSteppers);
;// ./node_modules/antd/es/color-picker/components/ColorAlphaInput.js
"use client";




const ColorAlphaInput = ({
  prefixCls,
  value,
  onChange
}) => {
  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;
  const [internalValue, setInternalValue] = (0,react.useState)(() => (0,util/* generateColor */.Z6)(value || '#000'));
  const alphaValue = value || internalValue;
  const handleAlphaChange = step => {
    const hsba = alphaValue.toHsb();
    hsba.a = (step || 0) / 100;
    const genColor = (0,util/* generateColor */.Z6)(hsba);
    setInternalValue(genColor);
    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);
  };
  return /*#__PURE__*/react.createElement(components_ColorSteppers, {
    value: (0,util/* getColorAlpha */.Gp)(alphaValue),
    prefixCls: prefixCls,
    formatter: step => `${step}%`,
    className: colorAlphaInputPrefixCls,
    onChange: handleAlphaChange
  });
};
/* harmony default export */ const components_ColorAlphaInput = (ColorAlphaInput);
// EXTERNAL MODULE: ./node_modules/antd/es/input/Input.js + 1 modules
var Input = __webpack_require__(18017);
;// ./node_modules/antd/es/color-picker/components/ColorHexInput.js
"use client";





const hexReg = /(^#[\da-f]{6}$)|(^#[\da-f]{8}$)/i;
const isHexString = hex => hexReg.test(`#${hex}`);
const ColorHexInput = ({
  prefixCls,
  value,
  onChange
}) => {
  const colorHexInputPrefixCls = `${prefixCls}-hex-input`;
  const [hexValue, setHexValue] = (0,react.useState)(() => value ? (0,color/* toHexFormat */.Ol)(value.toHexString()) : undefined);
  // Update step value
  (0,react.useEffect)(() => {
    if (value) {
      setHexValue((0,color/* toHexFormat */.Ol)(value.toHexString()));
    }
  }, [value]);
  const handleHexChange = e => {
    const originValue = e.target.value;
    setHexValue((0,color/* toHexFormat */.Ol)(originValue));
    if (isHexString((0,color/* toHexFormat */.Ol)(originValue, true))) {
      onChange === null || onChange === void 0 ? void 0 : onChange((0,util/* generateColor */.Z6)(originValue));
    }
  };
  return /*#__PURE__*/react.createElement(Input/* default */.A, {
    className: colorHexInputPrefixCls,
    value: hexValue,
    prefix: "#",
    onChange: handleHexChange,
    size: "small"
  });
};
/* harmony default export */ const components_ColorHexInput = (ColorHexInput);
;// ./node_modules/antd/es/color-picker/components/ColorHsbInput.js
"use client";




const ColorHsbInput = ({
  prefixCls,
  value,
  onChange
}) => {
  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;
  const [internalValue, setInternalValue] = (0,react.useState)(() => (0,util/* generateColor */.Z6)(value || '#000'));
  const hsbValue = value || internalValue;
  const handleHsbChange = (step, type) => {
    const hsb = hsbValue.toHsb();
    hsb[type] = type === 'h' ? step : (step || 0) / 100;
    const genColor = (0,util/* generateColor */.Z6)(hsb);
    setInternalValue(genColor);
    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);
  };
  return /*#__PURE__*/react.createElement("div", {
    className: colorHsbInputPrefixCls
  }, /*#__PURE__*/react.createElement(components_ColorSteppers, {
    max: 360,
    min: 0,
    value: Number(hsbValue.toHsb().h),
    prefixCls: prefixCls,
    className: colorHsbInputPrefixCls,
    formatter: step => (0,util/* getRoundNumber */.W)(step || 0).toString(),
    onChange: step => handleHsbChange(Number(step), 'h')
  }), /*#__PURE__*/react.createElement(components_ColorSteppers, {
    max: 100,
    min: 0,
    value: Number(hsbValue.toHsb().s) * 100,
    prefixCls: prefixCls,
    className: colorHsbInputPrefixCls,
    formatter: step => `${(0,util/* getRoundNumber */.W)(step || 0)}%`,
    onChange: step => handleHsbChange(Number(step), 's')
  }), /*#__PURE__*/react.createElement(components_ColorSteppers, {
    max: 100,
    min: 0,
    value: Number(hsbValue.toHsb().b) * 100,
    prefixCls: prefixCls,
    className: colorHsbInputPrefixCls,
    formatter: step => `${(0,util/* getRoundNumber */.W)(step || 0)}%`,
    onChange: step => handleHsbChange(Number(step), 'b')
  }));
};
/* harmony default export */ const components_ColorHsbInput = (ColorHsbInput);
;// ./node_modules/antd/es/color-picker/components/ColorRgbInput.js
"use client";




const ColorRgbInput = ({
  prefixCls,
  value,
  onChange
}) => {
  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;
  const [internalValue, setInternalValue] = (0,react.useState)(() => (0,util/* generateColor */.Z6)(value || '#000'));
  const rgbValue = value || internalValue;
  const handleRgbChange = (step, type) => {
    const rgb = rgbValue.toRgb();
    rgb[type] = step || 0;
    const genColor = (0,util/* generateColor */.Z6)(rgb);
    setInternalValue(genColor);
    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);
  };
  return /*#__PURE__*/react.createElement("div", {
    className: colorRgbInputPrefixCls
  }, /*#__PURE__*/react.createElement(components_ColorSteppers, {
    max: 255,
    min: 0,
    value: Number(rgbValue.toRgb().r),
    prefixCls: prefixCls,
    className: colorRgbInputPrefixCls,
    onChange: step => handleRgbChange(Number(step), 'r')
  }), /*#__PURE__*/react.createElement(components_ColorSteppers, {
    max: 255,
    min: 0,
    value: Number(rgbValue.toRgb().g),
    prefixCls: prefixCls,
    className: colorRgbInputPrefixCls,
    onChange: step => handleRgbChange(Number(step), 'g')
  }), /*#__PURE__*/react.createElement(components_ColorSteppers, {
    max: 255,
    min: 0,
    value: Number(rgbValue.toRgb().b),
    prefixCls: prefixCls,
    className: colorRgbInputPrefixCls,
    onChange: step => handleRgbChange(Number(step), 'b')
  }));
};
/* harmony default export */ const components_ColorRgbInput = (ColorRgbInput);
;// ./node_modules/antd/es/color-picker/components/ColorInput.js
"use client";









const selectOptions = [FORMAT_HEX, FORMAT_HSB, FORMAT_RGB].map(format => ({
  value: format,
  label: format.toUpperCase()
}));
const ColorInput = props => {
  const {
    prefixCls,
    format,
    value,
    disabledAlpha,
    onFormatChange,
    onChange,
    disabledFormat
  } = props;
  const [colorFormat, setColorFormat] = (0,useMergedState/* default */.A)(FORMAT_HEX, {
    value: format,
    onChange: onFormatChange
  });
  const colorInputPrefixCls = `${prefixCls}-input`;
  const handleFormatChange = newFormat => {
    setColorFormat(newFormat);
  };
  const steppersNode = (0,react.useMemo)(() => {
    const inputProps = {
      value,
      prefixCls,
      onChange
    };
    switch (colorFormat) {
      case FORMAT_HSB:
        return /*#__PURE__*/react.createElement(components_ColorHsbInput, Object.assign({}, inputProps));
      case FORMAT_RGB:
        return /*#__PURE__*/react.createElement(components_ColorRgbInput, Object.assign({}, inputProps));
      // case FORMAT_HEX:
      default:
        return /*#__PURE__*/react.createElement(components_ColorHexInput, Object.assign({}, inputProps));
    }
  }, [colorFormat, prefixCls, value, onChange]);
  return /*#__PURE__*/react.createElement("div", {
    className: `${colorInputPrefixCls}-container`
  }, !disabledFormat && (/*#__PURE__*/react.createElement(es_select/* default */.A, {
    value: colorFormat,
    variant: "borderless",
    getPopupContainer: current => current,
    popupMatchSelectWidth: 68,
    placement: "bottomRight",
    onChange: handleFormatChange,
    className: `${prefixCls}-format-select`,
    size: "small",
    options: selectOptions
  })), /*#__PURE__*/react.createElement("div", {
    className: colorInputPrefixCls
  }, steppersNode), !disabledAlpha && (/*#__PURE__*/react.createElement(components_ColorAlphaInput, {
    prefixCls: prefixCls,
    value: value,
    onChange: onChange
  })));
};
/* harmony default export */ const components_ColorInput = (ColorInput);
// EXTERNAL MODULE: ./node_modules/rc-slider/es/index.js + 14 modules
var rc_slider_es = __webpack_require__(55168);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useEvent.js
var useEvent = __webpack_require__(26956);
// EXTERNAL MODULE: ./node_modules/antd/es/slider/index.js + 3 modules
var slider = __webpack_require__(6531);
// EXTERNAL MODULE: ./node_modules/antd/es/slider/Context.js
var Context = __webpack_require__(87534);
;// ./node_modules/antd/es/color-picker/components/ColorSlider.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};







const GradientColorSlider = props => {
  const {
      prefixCls,
      colors,
      type,
      color,
      range = false,
      className,
      activeIndex,
      onActive,
      onDragStart,
      onDragChange,
      onKeyDelete
    } = props,
    restProps = __rest(props, ["prefixCls", "colors", "type", "color", "range", "className", "activeIndex", "onActive", "onDragStart", "onDragChange", "onKeyDelete"]);
  const sliderProps = Object.assign(Object.assign({}, restProps), {
    track: false
  });
  // ========================== Background ==========================
  const linearCss = react.useMemo(() => {
    const colorsStr = colors.map(c => `${c.color} ${c.percent}%`).join(', ');
    return `linear-gradient(90deg, ${colorsStr})`;
  }, [colors]);
  const pointColor = react.useMemo(() => {
    if (!color || !type) {
      return null;
    }
    if (type === 'alpha') {
      return color.toRgbString();
    }
    return `hsl(${color.toHsb().h}, 100%, 50%)`;
  }, [color, type]);
  // ======================= Context: Slider ========================
  const onInternalDragStart = (0,useEvent/* default */.A)(onDragStart);
  const onInternalDragChange = (0,useEvent/* default */.A)(onDragChange);
  const unstableContext = react.useMemo(() => ({
    onDragStart: onInternalDragStart,
    onDragChange: onInternalDragChange
  }), []);
  // ======================= Context: Render ========================
  const handleRender = (0,useEvent/* default */.A)((ori, info) => {
    const {
      onFocus,
      style,
      className: handleCls,
      onKeyDown
    } = ori.props;
    // Point Color
    const mergedStyle = Object.assign({}, style);
    if (type === 'gradient') {
      mergedStyle.background = (0,util/* getGradientPercentColor */.PU)(colors, info.value);
    }
    return /*#__PURE__*/react.cloneElement(ori, {
      onFocus: e => {
        onActive === null || onActive === void 0 ? void 0 : onActive(info.index);
        onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);
      },
      style: mergedStyle,
      className: classnames_default()(handleCls, {
        [`${prefixCls}-slider-handle-active`]: activeIndex === info.index
      }),
      onKeyDown: e => {
        if ((e.key === 'Delete' || e.key === 'Backspace') && onKeyDelete) {
          onKeyDelete(info.index);
        }
        onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);
      }
    });
  });
  const sliderContext = react.useMemo(() => ({
    direction: 'ltr',
    handleRender
  }), []);
  // ============================ Render ============================
  return /*#__PURE__*/react.createElement(Context/* default */.A.Provider, {
    value: sliderContext
  }, /*#__PURE__*/react.createElement(rc_slider_es/* UnstableContext */.Q.Provider, {
    value: unstableContext
  }, /*#__PURE__*/react.createElement(slider/* default */.A, Object.assign({}, sliderProps, {
    className: classnames_default()(className, `${prefixCls}-slider`),
    tooltip: {
      open: false
    },
    range: {
      editable: range,
      minCount: 2
    },
    styles: {
      rail: {
        background: linearCss
      },
      handle: pointColor ? {
        background: pointColor
      } : {}
    },
    classNames: {
      rail: `${prefixCls}-slider-rail`,
      handle: `${prefixCls}-slider-handle`
    }
  }))));
};
const SingleColorSlider = props => {
  const {
    value,
    onChange,
    onChangeComplete
  } = props;
  const singleOnChange = v => onChange(v[0]);
  const singleOnChangeComplete = v => onChangeComplete(v[0]);
  return /*#__PURE__*/react.createElement(GradientColorSlider, Object.assign({}, props, {
    value: [value],
    onChange: singleOnChange,
    onChangeComplete: singleOnChangeComplete
  }));
};
/* harmony default export */ const ColorSlider = (SingleColorSlider);
;// ./node_modules/antd/es/color-picker/components/PanelPicker/GradientColorBar.js
"use client";






function sortColors(colors) {
  return (0,toConsumableArray/* default */.A)(colors).sort((a, b) => a.percent - b.percent);
}
/**
 * GradientColorBar will auto show when the mode is `gradient`.
 */
const GradientColorBar = props => {
  const {
    prefixCls,
    mode,
    onChange,
    onChangeComplete,
    onActive,
    activeIndex,
    onGradientDragging,
    colors
  } = props;
  const isGradient = mode === 'gradient';
  // ============================= Colors =============================
  const colorList = react.useMemo(() => colors.map(info => ({
    percent: info.percent,
    color: info.color.toRgbString()
  })), [colors]);
  const values = react.useMemo(() => colorList.map(info => info.percent), [colorList]);
  // ============================== Drag ==============================
  const colorsRef = react.useRef(colorList);
  // Record current colors
  const onDragStart = ({
    rawValues,
    draggingIndex,
    draggingValue
  }) => {
    if (rawValues.length > colorList.length) {
      // Add new node
      const newPointColor = (0,util/* getGradientPercentColor */.PU)(colorList, draggingValue);
      const nextColors = (0,toConsumableArray/* default */.A)(colorList);
      nextColors.splice(draggingIndex, 0, {
        percent: draggingValue,
        color: newPointColor
      });
      colorsRef.current = nextColors;
    } else {
      colorsRef.current = colorList;
    }
    onGradientDragging(true);
    onChange(new color/* AggregationColor */.kf(sortColors(colorsRef.current)), true);
  };
  // Adjust color when dragging
  const onDragChange = ({
    deleteIndex,
    draggingIndex,
    draggingValue
  }) => {
    let nextColors = (0,toConsumableArray/* default */.A)(colorsRef.current);
    if (deleteIndex !== -1) {
      nextColors.splice(deleteIndex, 1);
    } else {
      nextColors[draggingIndex] = Object.assign(Object.assign({}, nextColors[draggingIndex]), {
        percent: draggingValue
      });
      nextColors = sortColors(nextColors);
    }
    onChange(new color/* AggregationColor */.kf(nextColors), true);
  };
  // ============================== Key ===============================
  const onKeyDelete = index => {
    const nextColors = (0,toConsumableArray/* default */.A)(colorList);
    nextColors.splice(index, 1);
    const nextColor = new color/* AggregationColor */.kf(nextColors);
    onChange(nextColor);
    onChangeComplete(nextColor);
  };
  // ============================= Change =============================
  const onInternalChangeComplete = nextValues => {
    onChangeComplete(new color/* AggregationColor */.kf(colorList));
    // Reset `activeIndex` if out of range
    if (activeIndex >= nextValues.length) {
      onActive(nextValues.length - 1);
    }
    onGradientDragging(false);
  };
  // ============================= Render =============================
  if (!isGradient) {
    return null;
  }
  return /*#__PURE__*/react.createElement(GradientColorSlider, {
    min: 0,
    max: 100,
    prefixCls: prefixCls,
    className: `${prefixCls}-gradient-slider`,
    colors: colorList,
    color: null,
    value: values,
    range: true,
    onChangeComplete: onInternalChangeComplete,
    disabled: false,
    type: "gradient",
    // Active
    activeIndex: activeIndex,
    onActive: onActive,
    // Drag
    onDragStart: onDragStart,
    onDragChange: onDragChange,
    onKeyDelete: onKeyDelete
  });
};
/* harmony default export */ const PanelPicker_GradientColorBar = (/*#__PURE__*/react.memo(GradientColorBar));
;// ./node_modules/antd/es/color-picker/components/PanelPicker/index.js
"use client";


var PanelPicker_rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};











const components = {
  slider: ColorSlider
};
const PanelPicker = () => {
  const panelPickerContext = (0,react.useContext)(PanelPickerContext);
  const {
      mode,
      onModeChange,
      modeOptions,
      prefixCls,
      allowClear,
      value,
      disabledAlpha,
      onChange,
      onClear,
      onChangeComplete,
      activeIndex,
      gradientDragging
    } = panelPickerContext,
    injectProps = PanelPicker_rest(panelPickerContext, ["mode", "onModeChange", "modeOptions", "prefixCls", "allowClear", "value", "disabledAlpha", "onChange", "onClear", "onChangeComplete", "activeIndex", "gradientDragging"]);
  // ============================ Colors ============================
  const colors = react.useMemo(() => {
    if (!value.cleared) {
      return value.getColors();
    }
    return [{
      percent: 0,
      color: new color/* AggregationColor */.kf('')
    }, {
      percent: 100,
      color: new color/* AggregationColor */.kf('')
    }];
  }, [value]);
  // ========================= Single Color =========================
  const isSingle = !value.isGradient();
  // We cache the point color in case user drag the gradient point across another one
  const [lockedColor, setLockedColor] = react.useState(value);
  // Use layout effect here since `useEffect` will cause a blink when mouseDown
  (0,useLayoutEffect/* default */.A)(() => {
    var _a;
    if (!isSingle) {
      setLockedColor((_a = colors[activeIndex]) === null || _a === void 0 ? void 0 : _a.color);
    }
  }, [gradientDragging, activeIndex]);
  const activeColor = react.useMemo(() => {
    var _a;
    if (isSingle) {
      return value;
    }
    // Use cache when dragging. User can not operation panel when dragging.
    if (gradientDragging) {
      return lockedColor;
    }
    return (_a = colors[activeIndex]) === null || _a === void 0 ? void 0 : _a.color;
  }, [value, activeIndex, isSingle, lockedColor, gradientDragging]);
  // ========================= Picker Color =========================
  const [pickerColor, setPickerColor] = react.useState(activeColor);
  const [forceSync, setForceSync] = react.useState(0);
  const mergedPickerColor = (pickerColor === null || pickerColor === void 0 ? void 0 : pickerColor.equals(activeColor)) ? activeColor : pickerColor;
  (0,useLayoutEffect/* default */.A)(() => {
    setPickerColor(activeColor);
  }, [forceSync, activeColor === null || activeColor === void 0 ? void 0 : activeColor.toHexString()]);
  // ============================ Change ============================
  const fillColor = (nextColor, info) => {
    let submitColor = (0,util/* generateColor */.Z6)(nextColor);
    // Fill alpha color to 100% if origin is cleared color
    if (value.cleared) {
      const rgb = submitColor.toRgb();
      // Auto fill color if origin is `0/0/0` to enhance user experience
      if (!rgb.r && !rgb.g && !rgb.b && info) {
        const {
          type: infoType,
          value: infoValue = 0
        } = info;
        submitColor = new color/* AggregationColor */.kf({
          h: infoType === 'hue' ? infoValue : 0,
          s: 1,
          b: 1,
          a: infoType === 'alpha' ? infoValue / 100 : 1
        });
      } else {
        submitColor = (0,util/* genAlphaColor */.E)(submitColor);
      }
    }
    if (mode === 'single') {
      return submitColor;
    }
    const nextColors = (0,toConsumableArray/* default */.A)(colors);
    nextColors[activeIndex] = Object.assign(Object.assign({}, nextColors[activeIndex]), {
      color: submitColor
    });
    return new color/* AggregationColor */.kf(nextColors);
  };
  const onPickerChange = (colorValue, fromPicker, info) => {
    const nextColor = fillColor(colorValue, info);
    setPickerColor(nextColor.isGradient() ? nextColor.getColors()[activeIndex].color : nextColor);
    onChange(nextColor, fromPicker);
  };
  const onInternalChangeComplete = (nextColor, info) => {
    // Trigger complete event
    onChangeComplete(fillColor(nextColor, info));
    // Back of origin color in case in controlled
    // This will set after `onChangeComplete` to avoid `setState` trigger rerender
    // which will make `fillColor` get wrong `color.cleared` state
    setForceSync(ori => ori + 1);
  };
  const onInputChange = colorValue => {
    onChange(fillColor(colorValue));
  };
  // ============================ Render ============================
  // Operation bar
  let operationNode = null;
  const showMode = modeOptions.length > 1;
  if (allowClear || showMode) {
    operationNode = /*#__PURE__*/react.createElement("div", {
      className: `${prefixCls}-operation`
    }, showMode && (/*#__PURE__*/react.createElement(segmented/* default */.A, {
      size: "small",
      options: modeOptions,
      value: mode,
      onChange: onModeChange
    })), /*#__PURE__*/react.createElement(components_ColorClear, Object.assign({
      prefixCls: prefixCls,
      value: value,
      onChange: clearColor => {
        onChange(clearColor);
        onClear === null || onClear === void 0 ? void 0 : onClear();
      }
    }, injectProps)));
  }
  // Return
  return /*#__PURE__*/react.createElement(react.Fragment, null, operationNode, /*#__PURE__*/react.createElement(PanelPicker_GradientColorBar, Object.assign({}, panelPickerContext, {
    colors: colors
  })), /*#__PURE__*/react.createElement(es/* default */.Ay, {
    prefixCls: prefixCls,
    value: mergedPickerColor === null || mergedPickerColor === void 0 ? void 0 : mergedPickerColor.toHsb(),
    disabledAlpha: disabledAlpha,
    onChange: (colorValue, info) => {
      onPickerChange(colorValue, true, info);
    },
    onChangeComplete: (colorValue, info) => {
      onInternalChangeComplete(colorValue, info);
    },
    components: components
  }), /*#__PURE__*/react.createElement(components_ColorInput, Object.assign({
    value: activeColor,
    onChange: onInputChange,
    prefixCls: prefixCls,
    disabledAlpha: disabledAlpha
  }, injectProps)));
};
/* harmony default export */ const components_PanelPicker = (PanelPicker);
// EXTERNAL MODULE: ./node_modules/antd/es/color-picker/components/ColorPresets.js
var ColorPresets = __webpack_require__(53596);
;// ./node_modules/antd/es/color-picker/components/PanelPresets.js
"use client";




const PanelPresets = () => {
  const {
    prefixCls,
    value,
    presets,
    onChange
  } = (0,react.useContext)(PanelPresetsContext);
  return Array.isArray(presets) ? (/*#__PURE__*/react.createElement(ColorPresets/* default */.A, {
    value: value,
    presets: presets,
    prefixCls: prefixCls,
    onChange: onChange
  })) : null;
};
/* harmony default export */ const components_PanelPresets = (PanelPresets);
;// ./node_modules/antd/es/color-picker/ColorPickerPanel.js
"use client";






const ColorPickerPanel = props => {
  const {
    prefixCls,
    presets,
    panelRender,
    value,
    onChange,
    onClear,
    allowClear,
    disabledAlpha,
    mode,
    onModeChange,
    modeOptions,
    onChangeComplete,
    activeIndex,
    onActive,
    format,
    onFormatChange,
    gradientDragging,
    onGradientDragging,
    disabledFormat
  } = props;
  const colorPickerPanelPrefixCls = `${prefixCls}-inner`;
  // ===================== Context ======================
  const panelContext = react.useMemo(() => ({
    prefixCls,
    value,
    onChange,
    onClear,
    allowClear,
    disabledAlpha,
    mode,
    onModeChange,
    modeOptions,
    onChangeComplete,
    activeIndex,
    onActive,
    format,
    onFormatChange,
    gradientDragging,
    onGradientDragging,
    disabledFormat
  }), [prefixCls, value, onChange, onClear, allowClear, disabledAlpha, mode, onModeChange, modeOptions, onChangeComplete, activeIndex, onActive, format, onFormatChange, gradientDragging, onGradientDragging, disabledFormat]);
  const presetContext = react.useMemo(() => ({
    prefixCls,
    value,
    presets,
    onChange
  }), [prefixCls, value, presets, onChange]);
  // ====================== Render ======================
  const innerPanel = /*#__PURE__*/react.createElement("div", {
    className: `${colorPickerPanelPrefixCls}-content`
  }, /*#__PURE__*/react.createElement(components_PanelPicker, null), Array.isArray(presets) && /*#__PURE__*/react.createElement(divider/* default */.A, null), /*#__PURE__*/react.createElement(components_PanelPresets, null));
  return /*#__PURE__*/react.createElement(PanelPickerContext.Provider, {
    value: panelContext
  }, /*#__PURE__*/react.createElement(PanelPresetsContext.Provider, {
    value: presetContext
  }, /*#__PURE__*/react.createElement("div", {
    className: colorPickerPanelPrefixCls
  }, typeof panelRender === 'function' ? panelRender(innerPanel, {
    components: {
      Picker: components_PanelPicker,
      Presets: components_PanelPresets
    }
  }) : innerPanel)));
};
if (false) {}
/* harmony default export */ const color_picker_ColorPickerPanel = (ColorPickerPanel);
// EXTERNAL MODULE: ./node_modules/rc-util/es/pickAttrs.js
var pickAttrs = __webpack_require__(72065);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/index.js
var es_locale = __webpack_require__(21282);
;// ./node_modules/antd/es/color-picker/components/ColorTrigger.js
"use client";

var ColorTrigger_rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};







const ColorTrigger = /*#__PURE__*/(0,react.forwardRef)((props, ref) => {
  const {
      color,
      prefixCls,
      open,
      disabled,
      format,
      className,
      showText,
      activeIndex
    } = props,
    rest = ColorTrigger_rest(props, ["color", "prefixCls", "open", "disabled", "format", "className", "showText", "activeIndex"]);
  const colorTriggerPrefixCls = `${prefixCls}-trigger`;
  const colorTextPrefixCls = `${colorTriggerPrefixCls}-text`;
  const colorTextCellPrefixCls = `${colorTextPrefixCls}-cell`;
  const [locale] = (0,es_locale/* useLocale */.Ym)('ColorPicker');
  // ============================== Text ==============================
  const desc = react.useMemo(() => {
    if (!showText) {
      return '';
    }
    if (typeof showText === 'function') {
      return showText(color);
    }
    if (color.cleared) {
      return locale.transparent;
    }
    if (color.isGradient()) {
      return color.getColors().map((c, index) => {
        const inactive = activeIndex !== -1 && activeIndex !== index;
        return /*#__PURE__*/react.createElement("span", {
          key: index,
          className: classnames_default()(colorTextCellPrefixCls, inactive && `${colorTextCellPrefixCls}-inactive`)
        }, c.color.toRgbString(), " ", c.percent, "%");
      });
    }
    const hexString = color.toHexString().toUpperCase();
    const alpha = (0,util/* getColorAlpha */.Gp)(color);
    switch (format) {
      case 'rgb':
        return color.toRgbString();
      case 'hsb':
        return color.toHsbString();
      // case 'hex':
      default:
        return alpha < 100 ? `${hexString.slice(0, 7)},${alpha}%` : hexString;
    }
  }, [color, format, showText, activeIndex]);
  // ============================= Render =============================
  const containerNode = (0,react.useMemo)(() => color.cleared ? (/*#__PURE__*/react.createElement(components_ColorClear, {
    prefixCls: prefixCls
  })) : (/*#__PURE__*/react.createElement(es/* ColorBlock */.ZC, {
    prefixCls: prefixCls,
    color: color.toCssString()
  })), [color, prefixCls]);
  return /*#__PURE__*/react.createElement("div", Object.assign({
    ref: ref,
    className: classnames_default()(colorTriggerPrefixCls, className, {
      [`${colorTriggerPrefixCls}-active`]: open,
      [`${colorTriggerPrefixCls}-disabled`]: disabled
    })
  }, (0,pickAttrs/* default */.A)(rest)), containerNode, showText && /*#__PURE__*/react.createElement("div", {
    className: colorTextPrefixCls
  }, desc));
});
/* harmony default export */ const components_ColorTrigger = (ColorTrigger);
;// ./node_modules/antd/es/color-picker/hooks/useModeColor.js





/**
 * Combine the `color` and `mode` to make sure sync of state.
 */
function useModeColor(defaultValue, value, mode) {
  const [locale] = (0,es_locale/* useLocale */.Ym)('ColorPicker');
  // ======================== Base ========================
  // Color
  const [mergedColor, setMergedColor] = (0,useMergedState/* default */.A)(defaultValue, {
    value
  });
  // Mode
  const [modeState, setModeState] = react.useState('single');
  const [modeOptionList, modeSet] = react.useMemo(() => {
    const list = (Array.isArray(mode) ? mode : [mode]).filter(m => m);
    if (!list.length) {
      list.push('single');
    }
    const modes = new Set(list);
    const optionList = [];
    const pushOption = (modeType, localeTxt) => {
      if (modes.has(modeType)) {
        optionList.push({
          label: localeTxt,
          value: modeType
        });
      }
    };
    pushOption('single', locale.singleColor);
    pushOption('gradient', locale.gradientColor);
    return [optionList, modes];
  }, [mode]);
  // ======================== Post ========================
  // We need align `mode` with `color` state
  // >>>>> Color
  const [cacheColor, setCacheColor] = react.useState(null);
  const setColor = (0,useEvent/* default */.A)(nextColor => {
    setCacheColor(nextColor);
    setMergedColor(nextColor);
  });
  const postColor = react.useMemo(() => {
    const colorObj = (0,util/* generateColor */.Z6)(mergedColor || '');
    // Use `cacheColor` in case the color is `cleared`
    return colorObj.equals(cacheColor) ? cacheColor : colorObj;
  }, [mergedColor, cacheColor]);
  // >>>>> Mode
  const postMode = react.useMemo(() => {
    var _a;
    if (modeSet.has(modeState)) {
      return modeState;
    }
    return (_a = modeOptionList[0]) === null || _a === void 0 ? void 0 : _a.value;
  }, [modeSet, modeState, modeOptionList]);
  // ======================= Effect =======================
  // Dynamic update mode when color change
  react.useEffect(() => {
    setModeState(postColor.isGradient() ? 'gradient' : 'single');
  }, [postColor]);
  // ======================= Return =======================
  return [postColor, setColor, postMode, setModeState, modeOptionList];
}
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 32 modules
var cssinjs_es = __webpack_require__(36891);
// EXTERNAL MODULE: ./node_modules/antd/es/style/compact-item.js
var compact_item = __webpack_require__(55974);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/internal.js + 3 modules
var internal = __webpack_require__(51113);
;// ./node_modules/antd/es/color-picker/style/color-block.js

/**
 * @private Internal usage only
 * see: https://developer.mozilla.org/en-US/docs/Web/CSS/gradient/conic-gradient#checkerboard
 */
const getTransBg = (size, colorFill) => ({
  backgroundImage: `conic-gradient(${colorFill} 25%, transparent 25% 50%, ${colorFill} 50% 75%, transparent 75% 100%)`,
  backgroundSize: `${size} ${size}`
});
const genColorBlockStyle = (token, size) => {
  const {
    componentCls,
    borderRadiusSM,
    colorPickerInsetShadow,
    lineWidth,
    colorFillSecondary
  } = token;
  return {
    [`${componentCls}-color-block`]: Object.assign(Object.assign({
      position: 'relative',
      borderRadius: borderRadiusSM,
      width: size,
      height: size,
      boxShadow: colorPickerInsetShadow,
      flex: 'none'
    }, getTransBg('50%', token.colorFillSecondary)), {
      [`${componentCls}-color-block-inner`]: {
        width: '100%',
        height: '100%',
        boxShadow: `inset 0 0 0 ${(0,cssinjs_es/* unit */.zA)(lineWidth)} ${colorFillSecondary}`,
        borderRadius: 'inherit'
      }
    })
  };
};
/* harmony default export */ const color_block = (genColorBlockStyle);
;// ./node_modules/antd/es/color-picker/style/input.js

const genInputStyle = token => {
  const {
    componentCls,
    antCls,
    fontSizeSM,
    lineHeightSM,
    colorPickerAlphaInputWidth,
    marginXXS,
    paddingXXS,
    controlHeightSM,
    marginXS,
    fontSizeIcon,
    paddingXS,
    colorTextPlaceholder,
    colorPickerInputNumberHandleWidth,
    lineWidth
  } = token;
  return {
    [`${componentCls}-input-container`]: {
      display: 'flex',
      [`${componentCls}-steppers${antCls}-input-number`]: {
        fontSize: fontSizeSM,
        lineHeight: lineHeightSM,
        [`${antCls}-input-number-input`]: {
          paddingInlineStart: paddingXXS,
          paddingInlineEnd: 0
        },
        [`${antCls}-input-number-handler-wrap`]: {
          width: colorPickerInputNumberHandleWidth
        }
      },
      [`${componentCls}-steppers${componentCls}-alpha-input`]: {
        flex: `0 0 ${(0,cssinjs_es/* unit */.zA)(colorPickerAlphaInputWidth)}`,
        marginInlineStart: marginXXS
      },
      [`${componentCls}-format-select${antCls}-select`]: {
        marginInlineEnd: marginXS,
        width: 'auto',
        '&-single': {
          [`${antCls}-select-selector`]: {
            padding: 0,
            border: 0
          },
          [`${antCls}-select-arrow`]: {
            insetInlineEnd: 0
          },
          [`${antCls}-select-selection-item`]: {
            paddingInlineEnd: token.calc(fontSizeIcon).add(marginXXS).equal(),
            fontSize: fontSizeSM,
            lineHeight: (0,cssinjs_es/* unit */.zA)(controlHeightSM)
          },
          [`${antCls}-select-item-option-content`]: {
            fontSize: fontSizeSM,
            lineHeight: lineHeightSM
          },
          [`${antCls}-select-dropdown`]: {
            [`${antCls}-select-item`]: {
              minHeight: 'auto'
            }
          }
        }
      },
      [`${componentCls}-input`]: {
        gap: marginXXS,
        alignItems: 'center',
        flex: 1,
        width: 0,
        [`${componentCls}-hsb-input,${componentCls}-rgb-input`]: {
          display: 'flex',
          gap: marginXXS,
          alignItems: 'center'
        },
        [`${componentCls}-steppers`]: {
          flex: 1
        },
        [`${componentCls}-hex-input${antCls}-input-affix-wrapper`]: {
          flex: 1,
          padding: `0 ${(0,cssinjs_es/* unit */.zA)(paddingXS)}`,
          [`${antCls}-input`]: {
            fontSize: fontSizeSM,
            textTransform: 'uppercase',
            lineHeight: (0,cssinjs_es/* unit */.zA)(token.calc(controlHeightSM).sub(token.calc(lineWidth).mul(2)).equal())
          },
          [`${antCls}-input-prefix`]: {
            color: colorTextPlaceholder
          }
        }
      }
    }
  };
};
/* harmony default export */ const input = (genInputStyle);
;// ./node_modules/antd/es/color-picker/style/picker.js

const genPickerStyle = token => {
  const {
    componentCls,
    controlHeightLG,
    borderRadiusSM,
    colorPickerInsetShadow,
    marginSM,
    colorBgElevated,
    colorFillSecondary,
    lineWidthBold,
    colorPickerHandlerSize
  } = token;
  return {
    userSelect: 'none',
    [`${componentCls}-select`]: {
      [`${componentCls}-palette`]: {
        minHeight: token.calc(controlHeightLG).mul(4).equal(),
        overflow: 'hidden',
        borderRadius: borderRadiusSM
      },
      [`${componentCls}-saturation`]: {
        position: 'absolute',
        borderRadius: 'inherit',
        boxShadow: colorPickerInsetShadow,
        inset: 0
      },
      marginBottom: marginSM
    },
    // ======================== Panel =========================
    [`${componentCls}-handler`]: {
      width: colorPickerHandlerSize,
      height: colorPickerHandlerSize,
      border: `${(0,cssinjs_es/* unit */.zA)(lineWidthBold)} solid ${colorBgElevated}`,
      position: 'relative',
      borderRadius: '50%',
      cursor: 'pointer',
      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`
    }
  };
};
/* harmony default export */ const picker = (genPickerStyle);
;// ./node_modules/antd/es/color-picker/style/presets.js

const genPresetsStyle = token => {
  const {
    componentCls,
    antCls,
    colorTextQuaternary,
    paddingXXS,
    colorPickerPresetColorSize,
    fontSizeSM,
    colorText,
    lineHeightSM,
    lineWidth,
    borderRadius,
    colorFill,
    colorWhite,
    marginXXS,
    paddingXS,
    fontHeightSM
  } = token;
  return {
    [`${componentCls}-presets`]: {
      [`${antCls}-collapse-item > ${antCls}-collapse-header`]: {
        padding: 0,
        [`${antCls}-collapse-expand-icon`]: {
          height: fontHeightSM,
          color: colorTextQuaternary,
          paddingInlineEnd: paddingXXS
        }
      },
      [`${antCls}-collapse`]: {
        display: 'flex',
        flexDirection: 'column',
        gap: marginXXS
      },
      [`${antCls}-collapse-item > ${antCls}-collapse-content > ${antCls}-collapse-content-box`]: {
        padding: `${(0,cssinjs_es/* unit */.zA)(paddingXS)} 0`
      },
      '&-label': {
        fontSize: fontSizeSM,
        color: colorText,
        lineHeight: lineHeightSM
      },
      '&-items': {
        display: 'flex',
        flexWrap: 'wrap',
        gap: token.calc(marginXXS).mul(1.5).equal(),
        [`${componentCls}-presets-color`]: {
          position: 'relative',
          cursor: 'pointer',
          width: colorPickerPresetColorSize,
          height: colorPickerPresetColorSize,
          '&::before': {
            content: '""',
            pointerEvents: 'none',
            width: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),
            height: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),
            position: 'absolute',
            top: token.calc(lineWidth).mul(-2).equal(),
            insetInlineStart: token.calc(lineWidth).mul(-2).equal(),
            borderRadius,
            border: `${(0,cssinjs_es/* unit */.zA)(lineWidth)} solid transparent`,
            transition: `border-color ${token.motionDurationMid} ${token.motionEaseInBack}`
          },
          '&:hover::before': {
            borderColor: colorFill
          },
          '&::after': {
            boxSizing: 'border-box',
            position: 'absolute',
            top: '50%',
            insetInlineStart: '21.5%',
            display: 'table',
            width: token.calc(colorPickerPresetColorSize).div(13).mul(5).equal(),
            height: token.calc(colorPickerPresetColorSize).div(13).mul(8).equal(),
            border: `${(0,cssinjs_es/* unit */.zA)(token.lineWidthBold)} solid ${token.colorWhite}`,
            borderTop: 0,
            borderInlineStart: 0,
            transform: 'rotate(45deg) scale(0) translate(-50%,-50%)',
            opacity: 0,
            content: '""',
            transition: `all ${token.motionDurationFast} ${token.motionEaseInBack}, opacity ${token.motionDurationFast}`
          },
          [`&${componentCls}-presets-color-checked`]: {
            '&::after': {
              opacity: 1,
              borderColor: colorWhite,
              transform: 'rotate(45deg) scale(1) translate(-50%,-50%)',
              transition: `transform ${token.motionDurationMid} ${token.motionEaseOutBack} ${token.motionDurationFast}`
            },
            [`&${componentCls}-presets-color-bright`]: {
              '&::after': {
                borderColor: 'rgba(0, 0, 0, 0.45)'
              }
            }
          }
        }
      },
      '&-empty': {
        fontSize: fontSizeSM,
        color: colorTextQuaternary
      }
    }
  };
};
/* harmony default export */ const presets = (genPresetsStyle);
;// ./node_modules/antd/es/color-picker/style/slider.js


const genSliderStyle = token => {
  const {
    componentCls,
    colorPickerInsetShadow,
    colorBgElevated,
    colorFillSecondary,
    lineWidthBold,
    colorPickerHandlerSizeSM,
    colorPickerSliderHeight,
    marginSM,
    marginXS
  } = token;
  const handleInnerSize = token.calc(colorPickerHandlerSizeSM).sub(token.calc(lineWidthBold).mul(2).equal()).equal();
  const handleHoverSize = token.calc(colorPickerHandlerSizeSM).add(token.calc(lineWidthBold).mul(2).equal()).equal();
  const activeHandleStyle = {
    '&:after': {
      transform: 'scale(1)',
      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${token.colorPrimaryActive}`
    }
  };
  return {
    // ======================== Slider ========================
    [`${componentCls}-slider`]: [getTransBg((0,cssinjs_es/* unit */.zA)(colorPickerSliderHeight), token.colorFillSecondary), {
      margin: 0,
      padding: 0,
      height: colorPickerSliderHeight,
      borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),
      '&-rail': {
        height: colorPickerSliderHeight,
        borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),
        boxShadow: colorPickerInsetShadow
      },
      [`& ${componentCls}-slider-handle`]: {
        width: handleInnerSize,
        height: handleInnerSize,
        top: 0,
        borderRadius: '100%',
        '&:before': {
          display: 'block',
          position: 'absolute',
          background: 'transparent',
          left: {
            _skip_check_: true,
            value: '50%'
          },
          top: '50%',
          transform: 'translate(-50%, -50%)',
          width: handleHoverSize,
          height: handleHoverSize,
          borderRadius: '100%'
        },
        '&:after': {
          width: colorPickerHandlerSizeSM,
          height: colorPickerHandlerSizeSM,
          border: `${(0,cssinjs_es/* unit */.zA)(lineWidthBold)} solid ${colorBgElevated}`,
          boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`,
          outline: 'none',
          insetInlineStart: token.calc(lineWidthBold).mul(-1).equal(),
          top: token.calc(lineWidthBold).mul(-1).equal(),
          background: 'transparent',
          transition: 'none'
        },
        '&:focus': activeHandleStyle
      }
    }],
    // ======================== Layout ========================
    [`${componentCls}-slider-container`]: {
      display: 'flex',
      gap: marginSM,
      marginBottom: marginSM,
      // Group
      [`${componentCls}-slider-group`]: {
        flex: 1,
        flexDirection: 'column',
        justifyContent: 'space-between',
        display: 'flex',
        '&-disabled-alpha': {
          justifyContent: 'center'
        }
      }
    },
    [`${componentCls}-gradient-slider`]: {
      marginBottom: marginXS,
      [`& ${componentCls}-slider-handle`]: {
        '&:after': {
          transform: 'scale(0.8)'
        },
        '&-active, &:focus': activeHandleStyle
      }
    }
  };
};
/* harmony default export */ const style_slider = (genSliderStyle);
;// ./node_modules/antd/es/color-picker/style/index.js








const genActiveStyle = (token, borderColor, outlineColor) => ({
  borderInlineEndWidth: token.lineWidth,
  borderColor,
  boxShadow: `0 0 0 ${(0,cssinjs_es/* unit */.zA)(token.controlOutlineWidth)} ${outlineColor}`,
  outline: 0
});
const genRtlStyle = token => {
  const {
    componentCls
  } = token;
  return {
    '&-rtl': {
      [`${componentCls}-presets-color`]: {
        '&::after': {
          direction: 'ltr'
        }
      },
      [`${componentCls}-clear`]: {
        '&::after': {
          direction: 'ltr'
        }
      }
    }
  };
};
const genClearStyle = (token, size, extraStyle) => {
  const {
    componentCls,
    borderRadiusSM,
    lineWidth,
    colorSplit,
    colorBorder,
    red6
  } = token;
  return {
    [`${componentCls}-clear`]: Object.assign(Object.assign({
      width: size,
      height: size,
      borderRadius: borderRadiusSM,
      border: `${(0,cssinjs_es/* unit */.zA)(lineWidth)} solid ${colorSplit}`,
      position: 'relative',
      overflow: 'hidden',
      cursor: 'inherit',
      transition: `all ${token.motionDurationFast}`
    }, extraStyle), {
      '&::after': {
        content: '""',
        position: 'absolute',
        insetInlineEnd: token.calc(lineWidth).mul(-1).equal(),
        top: token.calc(lineWidth).mul(-1).equal(),
        display: 'block',
        width: 40,
        // maximum
        height: 2,
        // fixed
        transformOrigin: `calc(100% - 1px) 1px`,
        transform: 'rotate(-45deg)',
        backgroundColor: red6
      },
      '&:hover': {
        borderColor: colorBorder
      }
    })
  };
};
const genStatusStyle = token => {
  const {
    componentCls,
    colorError,
    colorWarning,
    colorErrorHover,
    colorWarningHover,
    colorErrorOutline,
    colorWarningOutline
  } = token;
  return {
    [`&${componentCls}-status-error`]: {
      borderColor: colorError,
      '&:hover': {
        borderColor: colorErrorHover
      },
      [`&${componentCls}-trigger-active`]: Object.assign({}, genActiveStyle(token, colorError, colorErrorOutline))
    },
    [`&${componentCls}-status-warning`]: {
      borderColor: colorWarning,
      '&:hover': {
        borderColor: colorWarningHover
      },
      [`&${componentCls}-trigger-active`]: Object.assign({}, genActiveStyle(token, colorWarning, colorWarningOutline))
    }
  };
};
const genSizeStyle = token => {
  const {
    componentCls,
    controlHeightLG,
    controlHeightSM,
    controlHeight,
    controlHeightXS,
    borderRadius,
    borderRadiusSM,
    borderRadiusXS,
    borderRadiusLG,
    fontSizeLG
  } = token;
  return {
    [`&${componentCls}-lg`]: {
      minWidth: controlHeightLG,
      minHeight: controlHeightLG,
      borderRadius: borderRadiusLG,
      [`${componentCls}-color-block, ${componentCls}-clear`]: {
        width: controlHeight,
        height: controlHeight,
        borderRadius
      },
      [`${componentCls}-trigger-text`]: {
        fontSize: fontSizeLG
      }
    },
    [`&${componentCls}-sm`]: {
      minWidth: controlHeightSM,
      minHeight: controlHeightSM,
      borderRadius: borderRadiusSM,
      [`${componentCls}-color-block, ${componentCls}-clear`]: {
        width: controlHeightXS,
        height: controlHeightXS,
        borderRadius: borderRadiusXS
      },
      [`${componentCls}-trigger-text`]: {
        lineHeight: (0,cssinjs_es/* unit */.zA)(controlHeightXS)
      }
    }
  };
};
const genColorPickerStyle = token => {
  const {
    antCls,
    componentCls,
    colorPickerWidth,
    colorPrimary,
    motionDurationMid,
    colorBgElevated,
    colorTextDisabled,
    colorText,
    colorBgContainerDisabled,
    borderRadius,
    marginXS,
    marginSM,
    controlHeight,
    controlHeightSM,
    colorBgTextActive,
    colorPickerPresetColorSize,
    colorPickerPreviewSize,
    lineWidth,
    colorBorder,
    paddingXXS,
    fontSize,
    colorPrimaryHover,
    controlOutline
  } = token;
  return [{
    [componentCls]: Object.assign({
      [`${componentCls}-inner`]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({
        '&-content': {
          display: 'flex',
          flexDirection: 'column',
          width: colorPickerWidth,
          [`& > ${antCls}-divider`]: {
            margin: `${(0,cssinjs_es/* unit */.zA)(marginSM)} 0 ${(0,cssinjs_es/* unit */.zA)(marginXS)}`
          }
        },
        [`${componentCls}-panel`]: Object.assign({}, picker(token))
      }, style_slider(token)), color_block(token, colorPickerPreviewSize)), input(token)), presets(token)), genClearStyle(token, colorPickerPresetColorSize, {
        marginInlineStart: 'auto'
      })), {
        // Operation bar
        [`${componentCls}-operation`]: {
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: marginXS
        }
      }),
      '&-trigger': Object.assign(Object.assign(Object.assign(Object.assign({
        minWidth: controlHeight,
        minHeight: controlHeight,
        borderRadius,
        border: `${(0,cssinjs_es/* unit */.zA)(lineWidth)} solid ${colorBorder}`,
        cursor: 'pointer',
        display: 'inline-flex',
        alignItems: 'flex-start',
        justifyContent: 'center',
        transition: `all ${motionDurationMid}`,
        background: colorBgElevated,
        padding: token.calc(paddingXXS).sub(lineWidth).equal(),
        [`${componentCls}-trigger-text`]: {
          marginInlineStart: marginXS,
          marginInlineEnd: token.calc(marginXS).sub(token.calc(paddingXXS).sub(lineWidth)).equal(),
          fontSize,
          color: colorText,
          alignSelf: 'center',
          '&-cell': {
            '&:not(:last-child):after': {
              content: '", "'
            },
            '&-inactive': {
              color: colorTextDisabled
            }
          }
        },
        '&:hover': {
          borderColor: colorPrimaryHover
        },
        [`&${componentCls}-trigger-active`]: Object.assign({}, genActiveStyle(token, colorPrimary, controlOutline)),
        '&-disabled': {
          color: colorTextDisabled,
          background: colorBgContainerDisabled,
          cursor: 'not-allowed',
          '&:hover': {
            borderColor: colorBgTextActive
          },
          [`${componentCls}-trigger-text`]: {
            color: colorTextDisabled
          }
        }
      }, genClearStyle(token, controlHeightSM)), color_block(token, controlHeightSM)), genStatusStyle(token)), genSizeStyle(token))
    }, genRtlStyle(token))
  }, (0,compact_item/* genCompactItemStyle */.G)(token, {
    focusElCls: `${componentCls}-trigger-active`
  })];
};
/* harmony default export */ const color_picker_style = ((0,internal/* genStyleHooks */.OF)('ColorPicker', token => {
  const {
    colorTextQuaternary,
    marginSM
  } = token;
  const colorPickerSliderHeight = 8;
  const colorPickerToken = (0,internal/* mergeToken */.oX)(token, {
    colorPickerWidth: 234,
    colorPickerHandlerSize: 16,
    colorPickerHandlerSizeSM: 12,
    colorPickerAlphaInputWidth: 44,
    colorPickerInputNumberHandleWidth: 16,
    colorPickerPresetColorSize: 24,
    colorPickerInsetShadow: `inset 0 0 1px 0 ${colorTextQuaternary}`,
    colorPickerSliderHeight,
    colorPickerPreviewSize: token.calc(colorPickerSliderHeight).mul(2).add(marginSM).equal()
  });
  return [genColorPickerStyle(colorPickerToken)];
}));
;// ./node_modules/antd/es/color-picker/ColorPicker.js
"use client";

var ColorPicker_rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};




















const ColorPicker = props => {
  const {
      mode,
      value,
      defaultValue,
      format,
      defaultFormat,
      allowClear = false,
      presets,
      children,
      trigger = 'click',
      open,
      disabled,
      placement = 'bottomLeft',
      arrow = true,
      panelRender,
      showText,
      style,
      className,
      size: customizeSize,
      rootClassName,
      prefixCls: customizePrefixCls,
      styles,
      disabledAlpha = false,
      onFormatChange,
      onChange,
      onClear,
      onOpenChange,
      onChangeComplete,
      getPopupContainer,
      autoAdjustOverflow = true,
      destroyTooltipOnHide,
      destroyOnHidden,
      disabledFormat
    } = props,
    rest = ColorPicker_rest(props, ["mode", "value", "defaultValue", "format", "defaultFormat", "allowClear", "presets", "children", "trigger", "open", "disabled", "placement", "arrow", "panelRender", "showText", "style", "className", "size", "rootClassName", "prefixCls", "styles", "disabledAlpha", "onFormatChange", "onChange", "onClear", "onOpenChange", "onChangeComplete", "getPopupContainer", "autoAdjustOverflow", "destroyTooltipOnHide", "destroyOnHidden", "disabledFormat"]);
  const {
    getPrefixCls,
    direction,
    colorPicker
  } = (0,react.useContext)(context/* ConfigContext */.QO);
  const contextDisabled = (0,react.useContext)(DisabledContext/* default */.A);
  const mergedDisabled = disabled !== null && disabled !== void 0 ? disabled : contextDisabled;
  const [popupOpen, setPopupOpen] = (0,useMergedState/* default */.A)(false, {
    value: open,
    postState: openData => !mergedDisabled && openData,
    onChange: onOpenChange
  });
  const [formatValue, setFormatValue] = (0,useMergedState/* default */.A)(format, {
    value: format,
    defaultValue: defaultFormat,
    onChange: onFormatChange
  });
  const prefixCls = getPrefixCls('color-picker', customizePrefixCls);
  // ================== Value & Mode =================
  const [mergedColor, setColor, modeState, setModeState, modeOptions] = useModeColor(defaultValue, value, mode);
  const isAlphaColor = (0,react.useMemo)(() => (0,util/* getColorAlpha */.Gp)(mergedColor) < 100, [mergedColor]);
  // ==================== Change =====================
  // To enhance user experience, we cache the gradient color when switch from gradient to single
  // If user not modify single color, we will use the cached gradient color.
  const [cachedGradientColor, setCachedGradientColor] = react.useState(null);
  const onInternalChangeComplete = color => {
    if (onChangeComplete) {
      let changeColor = (0,util/* generateColor */.Z6)(color);
      // ignore alpha color
      if (disabledAlpha && isAlphaColor) {
        changeColor = (0,util/* genAlphaColor */.E)(color);
      }
      onChangeComplete(changeColor);
    }
  };
  const onInternalChange = (data, changeFromPickerDrag) => {
    let color = (0,util/* generateColor */.Z6)(data);
    // ignore alpha color
    if (disabledAlpha && isAlphaColor) {
      color = (0,util/* genAlphaColor */.E)(color);
    }
    setColor(color);
    setCachedGradientColor(null);
    // Trigger change event
    if (onChange) {
      onChange(color, color.toCssString());
    }
    // Only for drag-and-drop color picking
    if (!changeFromPickerDrag) {
      onInternalChangeComplete(color);
    }
  };
  // =================== Gradient ====================
  const [activeIndex, setActiveIndex] = react.useState(0);
  const [gradientDragging, setGradientDragging] = react.useState(false);
  // Mode change should also trigger color change
  const onInternalModeChange = newMode => {
    setModeState(newMode);
    if (newMode === 'single' && mergedColor.isGradient()) {
      setActiveIndex(0);
      onInternalChange(new color/* AggregationColor */.kf(mergedColor.getColors()[0].color));
      // Should after `onInternalChange` since it will clear the cached color
      setCachedGradientColor(mergedColor);
    } else if (newMode === 'gradient' && !mergedColor.isGradient()) {
      const baseColor = isAlphaColor ? (0,util/* genAlphaColor */.E)(mergedColor) : mergedColor;
      onInternalChange(new color/* AggregationColor */.kf(cachedGradientColor || [{
        percent: 0,
        color: baseColor
      }, {
        percent: 100,
        color: baseColor
      }]));
    }
  };
  // ================== Form Status ==================
  const {
    status: contextStatus
  } = react.useContext(form_context/* FormItemInputContext */.$W);
  // ==================== Compact ====================
  const {
    compactSize,
    compactItemClassnames
  } = (0,Compact/* useCompactItemContext */.RQ)(prefixCls, direction);
  // ===================== Style =====================
  const mergedSize = (0,useSize/* default */.A)(ctx => {
    var _a;
    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : compactSize) !== null && _a !== void 0 ? _a : ctx;
  });
  const rootCls = (0,useCSSVarCls/* default */.A)(prefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = color_picker_style(prefixCls, rootCls);
  const rtlCls = {
    [`${prefixCls}-rtl`]: direction
  };
  const mergedRootCls = classnames_default()(rootClassName, cssVarCls, rootCls, rtlCls);
  const mergedCls = classnames_default()((0,statusUtils/* getStatusClassNames */.L)(prefixCls, contextStatus), {
    [`${prefixCls}-sm`]: mergedSize === 'small',
    [`${prefixCls}-lg`]: mergedSize === 'large'
  }, compactItemClassnames, colorPicker === null || colorPicker === void 0 ? void 0 : colorPicker.className, mergedRootCls, className, hashId);
  const mergedPopupCls = classnames_default()(prefixCls, mergedRootCls);
  // ===================== Warning ======================
  if (false) {}
  const popoverProps = {
    open: popupOpen,
    trigger,
    placement,
    arrow,
    rootClassName,
    getPopupContainer,
    autoAdjustOverflow,
    destroyOnHidden: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : !!destroyTooltipOnHide
  };
  const mergedStyle = Object.assign(Object.assign({}, colorPicker === null || colorPicker === void 0 ? void 0 : colorPicker.style), style);
  // ============================ zIndex ============================
  return wrapCSSVar(/*#__PURE__*/react.createElement(popover/* default */.A, Object.assign({
    style: styles === null || styles === void 0 ? void 0 : styles.popup,
    styles: {
      body: styles === null || styles === void 0 ? void 0 : styles.popupOverlayInner
    },
    onOpenChange: visible => {
      if (!visible || !mergedDisabled) {
        setPopupOpen(visible);
      }
    },
    content: /*#__PURE__*/react.createElement(ContextIsolator/* default */.A, {
      form: true
    }, /*#__PURE__*/react.createElement(color_picker_ColorPickerPanel, {
      mode: modeState,
      onModeChange: onInternalModeChange,
      modeOptions: modeOptions,
      prefixCls: prefixCls,
      value: mergedColor,
      allowClear: allowClear,
      disabled: mergedDisabled,
      disabledAlpha: disabledAlpha,
      presets: presets,
      panelRender: panelRender,
      format: formatValue,
      onFormatChange: setFormatValue,
      onChange: onInternalChange,
      onChangeComplete: onInternalChangeComplete,
      onClear: onClear,
      activeIndex: activeIndex,
      onActive: setActiveIndex,
      gradientDragging: gradientDragging,
      onGradientDragging: setGradientDragging,
      disabledFormat: disabledFormat
    })),
    classNames: {
      root: mergedPopupCls
    }
  }, popoverProps), children || (/*#__PURE__*/react.createElement(components_ColorTrigger, Object.assign({
    activeIndex: popupOpen ? activeIndex : -1,
    open: popupOpen,
    className: mergedCls,
    style: mergedStyle,
    prefixCls: prefixCls,
    disabled: mergedDisabled,
    showText: showText,
    format: formatValue
  }, rest, {
    color: mergedColor
  })))));
};
if (false) {}
const ColorPicker_PurePanel = (0,PurePanel/* default */.A)(ColorPicker, undefined, props => Object.assign(Object.assign({}, props), {
  placement: 'bottom',
  autoAdjustOverflow: false
}), 'color-picker', /* istanbul ignore next */
prefixCls => prefixCls);
ColorPicker._InternalPanelDoNotUseOrYouWillBeFired = ColorPicker_PurePanel;
/* harmony default export */ const color_picker_ColorPicker = (ColorPicker);
;// ./node_modules/antd/es/color-picker/index.js
"use client";


/* harmony default export */ const color_picker = (color_picker_ColorPicker);

/***/ }),

/***/ 36058:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   E: () => (/* binding */ genAlphaColor),
/* harmony export */   Gp: () => (/* binding */ getColorAlpha),
/* harmony export */   PU: () => (/* binding */ getGradientPercentColor),
/* harmony export */   W: () => (/* binding */ getRoundNumber),
/* harmony export */   Z6: () => (/* binding */ generateColor)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(71021);
/* harmony import */ var _color__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(19911);



const generateColor = color => {
  if (color instanceof _color__WEBPACK_IMPORTED_MODULE_2__/* .AggregationColor */ .kf) {
    return color;
  }
  return new _color__WEBPACK_IMPORTED_MODULE_2__/* .AggregationColor */ .kf(color);
};
const getRoundNumber = value => Math.round(Number(value || 0));
const getColorAlpha = color => getRoundNumber(color.toHsb().a * 100);
/** Return the color whose `alpha` is 1 */
const genAlphaColor = (color, alpha) => {
  const rgba = color.toRgb();
  // Color from hsb input may get `rgb` is (0/0/0) when `hsb.b` is 0
  // So if rgb is empty, we should get from hsb
  if (!rgba.r && !rgba.g && !rgba.b) {
    const hsba = color.toHsb();
    hsba.a = alpha || 1;
    return generateColor(hsba);
  }
  rgba.a = alpha || 1;
  return generateColor(rgba);
};
/**
 * Get percent position color. e.g. [10%-#fff, 20%-#000], 15% => #888
 */
const getGradientPercentColor = (colors, percent) => {
  const filledColors = [{
    percent: 0,
    color: colors[0].color
  }].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(colors), [{
    percent: 100,
    color: colors[colors.length - 1].color
  }]);
  for (let i = 0; i < filledColors.length - 1; i += 1) {
    const startPtg = filledColors[i].percent;
    const endPtg = filledColors[i + 1].percent;
    const startColor = filledColors[i].color;
    const endColor = filledColors[i + 1].color;
    if (startPtg <= percent && percent <= endPtg) {
      const dist = endPtg - startPtg;
      if (dist === 0) {
        return startColor;
      }
      const ratio = (percent - startPtg) / dist * 100;
      const startRcColor = new _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_1__/* .Color */ .Q1(startColor);
      const endRcColor = new _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_1__/* .Color */ .Q1(endColor);
      return startRcColor.mix(endRcColor, ratio).toRgbString();
    }
  }
  // This will never reach
  /* istanbul ignore next */
  return '';
};

/***/ }),

/***/ 39356:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ collapse)
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/RightOutlined.js
var RightOutlined = __webpack_require__(14588);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-collapse/es/index.js + 4 modules
var es = __webpack_require__(71057);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Children/toArray.js
var toArray = __webpack_require__(82546);
// EXTERNAL MODULE: ./node_modules/rc-util/es/omit.js
var omit = __webpack_require__(19853);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/motion.js
var motion = __webpack_require__(23723);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/reactNode.js
var reactNode = __webpack_require__(40682);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/warning.js
var warning = __webpack_require__(18877);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(62279);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useSize.js
var useSize = __webpack_require__(829);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(38674);
;// ./node_modules/antd/es/collapse/CollapsePanel.js
"use client";






const CollapsePanel = /*#__PURE__*/react.forwardRef((props, ref) => {
  if (false) {}
  const {
    getPrefixCls
  } = react.useContext(config_provider/* ConfigContext */.QO);
  const {
    prefixCls: customizePrefixCls,
    className,
    showArrow = true
  } = props;
  const prefixCls = getPrefixCls('collapse', customizePrefixCls);
  const collapsePanelClassName = classnames_default()({
    [`${prefixCls}-no-arrow`]: !showArrow
  }, className);
  return /*#__PURE__*/react.createElement(es/* default.Panel */.A.Panel, Object.assign({
    ref: ref
  }, props, {
    prefixCls: prefixCls,
    className: collapsePanelClassName
  }));
});
/* harmony default export */ const collapse_CollapsePanel = (CollapsePanel);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 32 modules
var cssinjs_es = __webpack_require__(36891);
// EXTERNAL MODULE: ./node_modules/antd/es/style/index.js
var style = __webpack_require__(25905);
// EXTERNAL MODULE: ./node_modules/antd/es/style/motion/index.js + 4 modules
var style_motion = __webpack_require__(38328);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/internal.js + 3 modules
var internal = __webpack_require__(51113);
;// ./node_modules/antd/es/collapse/style/index.js




const genBaseStyle = token => {
  const {
    componentCls,
    contentBg,
    padding,
    headerBg,
    headerPadding,
    collapseHeaderPaddingSM,
    collapseHeaderPaddingLG,
    collapsePanelBorderRadius,
    lineWidth,
    lineType,
    colorBorder,
    colorText,
    colorTextHeading,
    colorTextDisabled,
    fontSizeLG,
    lineHeight,
    lineHeightLG,
    marginSM,
    paddingSM,
    paddingLG,
    paddingXS,
    motionDurationSlow,
    fontSizeIcon,
    contentPadding,
    fontHeight,
    fontHeightLG
  } = token;
  const borderBase = `${(0,cssinjs_es/* unit */.zA)(lineWidth)} ${lineType} ${colorBorder}`;
  return {
    [componentCls]: Object.assign(Object.assign({}, (0,style/* resetComponent */.dF)(token)), {
      backgroundColor: headerBg,
      border: borderBase,
      borderRadius: collapsePanelBorderRadius,
      '&-rtl': {
        direction: 'rtl'
      },
      [`& > ${componentCls}-item`]: {
        borderBottom: borderBase,
        '&:first-child': {
          [`
            &,
            & > ${componentCls}-header`]: {
            borderRadius: `${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} 0 0`
          }
        },
        '&:last-child': {
          [`
            &,
            & > ${componentCls}-header`]: {
            borderRadius: `0 0 ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)}`
          }
        },
        [`> ${componentCls}-header`]: Object.assign(Object.assign({
          position: 'relative',
          display: 'flex',
          flexWrap: 'nowrap',
          alignItems: 'flex-start',
          padding: headerPadding,
          color: colorTextHeading,
          lineHeight,
          cursor: 'pointer',
          transition: `all ${motionDurationSlow}, visibility 0s`
        }, (0,style/* genFocusStyle */.K8)(token)), {
          [`> ${componentCls}-header-text`]: {
            flex: 'auto'
          },
          // >>>>> Arrow
          [`${componentCls}-expand-icon`]: {
            height: fontHeight,
            display: 'flex',
            alignItems: 'center',
            paddingInlineEnd: marginSM
          },
          [`${componentCls}-arrow`]: Object.assign(Object.assign({}, (0,style/* resetIcon */.Nk)()), {
            fontSize: fontSizeIcon,
            // when `transform: rotate()` is applied to icon's root element
            transition: `transform ${motionDurationSlow}`,
            // when `transform: rotate()` is applied to icon's child element
            svg: {
              transition: `transform ${motionDurationSlow}`
            }
          }),
          // >>>>> Text
          [`${componentCls}-header-text`]: {
            marginInlineEnd: 'auto'
          }
        }),
        [`${componentCls}-collapsible-header`]: {
          cursor: 'default',
          [`${componentCls}-header-text`]: {
            flex: 'none',
            cursor: 'pointer'
          }
        },
        [`${componentCls}-collapsible-icon`]: {
          cursor: 'unset',
          [`${componentCls}-expand-icon`]: {
            cursor: 'pointer'
          }
        }
      },
      [`${componentCls}-content`]: {
        color: colorText,
        backgroundColor: contentBg,
        borderTop: borderBase,
        [`& > ${componentCls}-content-box`]: {
          padding: contentPadding
        },
        '&-hidden': {
          display: 'none'
        }
      },
      '&-small': {
        [`> ${componentCls}-item`]: {
          [`> ${componentCls}-header`]: {
            padding: collapseHeaderPaddingSM,
            paddingInlineStart: paddingXS,
            [`> ${componentCls}-expand-icon`]: {
              // Arrow offset
              marginInlineStart: token.calc(paddingSM).sub(paddingXS).equal()
            }
          },
          [`> ${componentCls}-content > ${componentCls}-content-box`]: {
            padding: paddingSM
          }
        }
      },
      '&-large': {
        [`> ${componentCls}-item`]: {
          fontSize: fontSizeLG,
          lineHeight: lineHeightLG,
          [`> ${componentCls}-header`]: {
            padding: collapseHeaderPaddingLG,
            paddingInlineStart: padding,
            [`> ${componentCls}-expand-icon`]: {
              height: fontHeightLG,
              // Arrow offset
              marginInlineStart: token.calc(paddingLG).sub(padding).equal()
            }
          },
          [`> ${componentCls}-content > ${componentCls}-content-box`]: {
            padding: paddingLG
          }
        }
      },
      [`${componentCls}-item:last-child`]: {
        borderBottom: 0,
        [`> ${componentCls}-content`]: {
          borderRadius: `0 0 ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)}`
        }
      },
      [`& ${componentCls}-item-disabled > ${componentCls}-header`]: {
        [`
          &,
          & > .arrow
        `]: {
          color: colorTextDisabled,
          cursor: 'not-allowed'
        }
      },
      // ========================== Icon Position ==========================
      [`&${componentCls}-icon-position-end`]: {
        [`& > ${componentCls}-item`]: {
          [`> ${componentCls}-header`]: {
            [`${componentCls}-expand-icon`]: {
              order: 1,
              paddingInlineEnd: 0,
              paddingInlineStart: marginSM
            }
          }
        }
      }
    })
  };
};
const genArrowStyle = token => {
  const {
    componentCls
  } = token;
  const fixedSelector = `> ${componentCls}-item > ${componentCls}-header ${componentCls}-arrow`;
  return {
    [`${componentCls}-rtl`]: {
      [fixedSelector]: {
        transform: `rotate(180deg)`
      }
    }
  };
};
const genBorderlessStyle = token => {
  const {
    componentCls,
    headerBg,
    borderlessContentPadding,
    borderlessContentBg,
    colorBorder
  } = token;
  return {
    [`${componentCls}-borderless`]: {
      backgroundColor: headerBg,
      border: 0,
      [`> ${componentCls}-item`]: {
        borderBottom: `1px solid ${colorBorder}`
      },
      [`
        > ${componentCls}-item:last-child,
        > ${componentCls}-item:last-child ${componentCls}-header
      `]: {
        borderRadius: 0
      },
      [`> ${componentCls}-item:last-child`]: {
        borderBottom: 0
      },
      [`> ${componentCls}-item > ${componentCls}-content`]: {
        backgroundColor: borderlessContentBg,
        borderTop: 0
      },
      [`> ${componentCls}-item > ${componentCls}-content > ${componentCls}-content-box`]: {
        padding: borderlessContentPadding
      }
    }
  };
};
const genGhostStyle = token => {
  const {
    componentCls,
    paddingSM
  } = token;
  return {
    [`${componentCls}-ghost`]: {
      backgroundColor: 'transparent',
      border: 0,
      [`> ${componentCls}-item`]: {
        borderBottom: 0,
        [`> ${componentCls}-content`]: {
          backgroundColor: 'transparent',
          border: 0,
          [`> ${componentCls}-content-box`]: {
            paddingBlock: paddingSM
          }
        }
      }
    }
  };
};
const prepareComponentToken = token => ({
  headerPadding: `${token.paddingSM}px ${token.padding}px`,
  headerBg: token.colorFillAlter,
  contentPadding: `${token.padding}px 16px`,
  // Fixed Value
  contentBg: token.colorBgContainer,
  borderlessContentPadding: `${token.paddingXXS}px 16px ${token.padding}px`,
  borderlessContentBg: 'transparent'
});
/* harmony default export */ const collapse_style = ((0,internal/* genStyleHooks */.OF)('Collapse', token => {
  const collapseToken = (0,internal/* mergeToken */.oX)(token, {
    collapseHeaderPaddingSM: `${(0,cssinjs_es/* unit */.zA)(token.paddingXS)} ${(0,cssinjs_es/* unit */.zA)(token.paddingSM)}`,
    collapseHeaderPaddingLG: `${(0,cssinjs_es/* unit */.zA)(token.padding)} ${(0,cssinjs_es/* unit */.zA)(token.paddingLG)}`,
    collapsePanelBorderRadius: token.borderRadiusLG
  });
  return [genBaseStyle(collapseToken), genBorderlessStyle(collapseToken), genGhostStyle(collapseToken), genArrowStyle(collapseToken), (0,style_motion/* genCollapseMotion */.eG)(collapseToken)];
}, prepareComponentToken));
;// ./node_modules/antd/es/collapse/Collapse.js
"use client";














const Collapse = /*#__PURE__*/react.forwardRef((props, ref) => {
  const {
    getPrefixCls,
    direction,
    expandIcon: contextExpandIcon,
    className: contextClassName,
    style: contextStyle
  } = (0,context/* useComponentConfig */.TP)('collapse');
  const {
    prefixCls: customizePrefixCls,
    className,
    rootClassName,
    style,
    bordered = true,
    ghost,
    size: customizeSize,
    expandIconPosition = 'start',
    children,
    destroyInactivePanel,
    destroyOnHidden,
    expandIcon
  } = props;
  const mergedSize = (0,useSize/* default */.A)(ctx => {
    var _a;
    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : ctx) !== null && _a !== void 0 ? _a : 'middle';
  });
  const prefixCls = getPrefixCls('collapse', customizePrefixCls);
  const rootPrefixCls = getPrefixCls();
  const [wrapCSSVar, hashId, cssVarCls] = collapse_style(prefixCls);
  if (false) {}
  // Align with logic position
  const mergedExpandIconPosition = react.useMemo(() => {
    if (expandIconPosition === 'left') {
      return 'start';
    }
    return expandIconPosition === 'right' ? 'end' : expandIconPosition;
  }, [expandIconPosition]);
  const mergedExpandIcon = expandIcon !== null && expandIcon !== void 0 ? expandIcon : contextExpandIcon;
  const renderExpandIcon = react.useCallback((panelProps = {}) => {
    const icon = typeof mergedExpandIcon === 'function' ? mergedExpandIcon(panelProps) : (/*#__PURE__*/react.createElement(RightOutlined/* default */.A, {
      rotate: panelProps.isActive ? direction === 'rtl' ? -90 : 90 : undefined,
      "aria-label": panelProps.isActive ? 'expanded' : 'collapsed'
    }));
    return (0,reactNode/* cloneElement */.Ob)(icon, () => {
      var _a;
      return {
        className: classnames_default()((_a = icon === null || icon === void 0 ? void 0 : icon.props) === null || _a === void 0 ? void 0 : _a.className, `${prefixCls}-arrow`)
      };
    });
  }, [mergedExpandIcon, prefixCls]);
  const collapseClassName = classnames_default()(`${prefixCls}-icon-position-${mergedExpandIconPosition}`, {
    [`${prefixCls}-borderless`]: !bordered,
    [`${prefixCls}-rtl`]: direction === 'rtl',
    [`${prefixCls}-ghost`]: !!ghost,
    [`${prefixCls}-${mergedSize}`]: mergedSize !== 'middle'
  }, contextClassName, className, rootClassName, hashId, cssVarCls);
  const openMotion = Object.assign(Object.assign({}, (0,motion/* default */.A)(rootPrefixCls)), {
    motionAppear: false,
    leavedClassName: `${prefixCls}-content-hidden`
  });
  const items = react.useMemo(() => {
    if (children) {
      return (0,toArray/* default */.A)(children).map((child, index) => {
        var _a, _b;
        const childProps = child.props;
        if (childProps === null || childProps === void 0 ? void 0 : childProps.disabled) {
          const key = (_a = child.key) !== null && _a !== void 0 ? _a : String(index);
          const mergedChildProps = Object.assign(Object.assign({}, (0,omit/* default */.A)(child.props, ['disabled'])), {
            key,
            collapsible: (_b = childProps.collapsible) !== null && _b !== void 0 ? _b : 'disabled'
          });
          return (0,reactNode/* cloneElement */.Ob)(child, mergedChildProps);
        }
        return child;
      });
    }
    return null;
  }, [children]);
  return wrapCSSVar(
  /*#__PURE__*/
  // @ts-ignore
  react.createElement(es/* default */.A, Object.assign({
    ref: ref,
    openMotion: openMotion
  }, (0,omit/* default */.A)(props, ['rootClassName']), {
    expandIcon: renderExpandIcon,
    prefixCls: prefixCls,
    className: collapseClassName,
    style: Object.assign(Object.assign({}, contextStyle), style),
    // TODO: In the future, destroyInactivePanel in rc-collapse needs to be upgrade to destroyOnHidden
    destroyInactivePanel: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyInactivePanel
  }), items));
});
if (false) {}
/* harmony default export */ const collapse_Collapse = (Object.assign(Collapse, {
  Panel: collapse_CollapsePanel
}));
;// ./node_modules/antd/es/collapse/index.js
"use client";


/* harmony default export */ const collapse = (collapse_Collapse);

/***/ }),

/***/ 53596:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   z: () => (/* binding */ isBright)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96540);
/* harmony import */ var _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(71021);
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(46942);
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12533);
/* harmony import */ var _collapse__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(39356);
/* harmony import */ var _locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(21282);
/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(51113);
/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(36058);
"use client";









const genPresetColor = list => list.map(value => {
  value.colors = value.colors.map(_util__WEBPACK_IMPORTED_MODULE_7__/* .generateColor */ .Z6);
  return value;
});
const isBright = (value, bgColorToken) => {
  const {
    r,
    g,
    b,
    a
  } = value.toRgb();
  const hsv = new _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_1__/* .Color */ .Q1(value.toRgbString()).onBackground(bgColorToken).toHsv();
  if (a <= 0.5) {
    // Adapted to dark mode
    return hsv.v > 0.5;
  }
  return r * 0.299 + g * 0.587 + b * 0.114 > 192;
};
const genCollapsePanelKey = (preset, index) => {
  var _a;
  const mergedKey = (_a = preset.key) !== null && _a !== void 0 ? _a : index;
  return `panel-${mergedKey}`;
};
const ColorPresets = ({
  prefixCls,
  presets,
  value: color,
  onChange
}) => {
  const [locale] = (0,_locale__WEBPACK_IMPORTED_MODULE_5__/* .useLocale */ .Ym)('ColorPicker');
  const [, token] = (0,_theme_internal__WEBPACK_IMPORTED_MODULE_6__/* .useToken */ .rd)();
  const [presetsValue] = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(genPresetColor(presets), {
    value: genPresetColor(presets),
    postState: genPresetColor
  });
  const colorPresetsPrefixCls = `${prefixCls}-presets`;
  const activeKeys = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => presetsValue.reduce((acc, preset, index) => {
    const {
      defaultOpen = true
    } = preset;
    if (defaultOpen) {
      acc.push(genCollapsePanelKey(preset, index));
    }
    return acc;
  }, []), [presetsValue]);
  const handleClick = colorValue => {
    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);
  };
  const items = presetsValue.map((preset, index) => {
    var _a;
    return {
      key: genCollapsePanelKey(preset, index),
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        className: `${colorPresetsPrefixCls}-label`
      }, preset === null || preset === void 0 ? void 0 : preset.label),
      children: (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        className: `${colorPresetsPrefixCls}-items`
      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map((presetColor, index) => (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_rc_component_color_picker__WEBPACK_IMPORTED_MODULE_1__/* .ColorBlock */ .ZC
      // eslint-disable-next-line react/no-array-index-key
      , {
        // eslint-disable-next-line react/no-array-index-key
        key: `preset-${index}-${presetColor.toHexString()}`,
        color: (0,_util__WEBPACK_IMPORTED_MODULE_7__/* .generateColor */ .Z6)(presetColor).toRgbString(),
        prefixCls: prefixCls,
        className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(`${colorPresetsPrefixCls}-color`, {
          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),
          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, token.colorBgElevated)
        }),
        onClick: () => handleClick(presetColor)
      }))) : (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("span", {
        className: `${colorPresetsPrefixCls}-empty`
      }, locale.presetEmpty))))
    };
  });
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    className: colorPresetsPrefixCls
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_collapse__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    defaultActiveKey: activeKeys,
    ghost: true,
    items: items
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ColorPresets);

/***/ })

}]);