"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1375],{

/***/ 91375:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_SettingsPage)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./src/contexts/AuthContext.js + 1 modules
var AuthContext = __webpack_require__(49391);
// EXTERNAL MODULE: ./src/components/theme/ThemeManager.js
var ThemeManager = __webpack_require__(93385);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./src/utils/auth.js + 2 modules
var auth = __webpack_require__(69477);
;// ./src/components/auth/Profile.js



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * User Profile Component
 * 
 * This component displays and allows editing of the user's profile.
 */
var Profile = function Profile() {
  var _useState = (0,react.useState)({
      username: '',
      email: '',
      name: '',
      bio: '',
      avatar: ''
    }),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    profileData = _useState2[0],
    setProfileData = _useState2[1];
  var _useState3 = (0,react.useState)({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    passwordData = _useState4[0],
    setPasswordData = _useState4[1];
  var _useState5 = (0,react.useState)(false),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    loading = _useState6[0],
    setLoading = _useState6[1];
  var _useState7 = (0,react.useState)(null),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    profileError = _useState8[0],
    setProfileError = _useState8[1];
  var _useState9 = (0,react.useState)(null),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    passwordError = _useState0[0],
    setPasswordError = _useState0[1];
  var _useState1 = (0,react.useState)(false),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    profileSuccess = _useState10[0],
    setProfileSuccess = _useState10[1];
  var _useState11 = (0,react.useState)(false),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    passwordSuccess = _useState12[0],
    setPasswordSuccess = _useState12[1];
  var _useState13 = (0,react.useState)('profile'),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    activeTab = _useState14[0],
    setActiveTab = _useState14[1];

  // Fetch user profile
  (0,react.useEffect)(function () {
    var fetchProfile = /*#__PURE__*/function () {
      var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
        var profile;
        return regenerator_default().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              setLoading(true);
              _context.next = 4;
              return (0,auth/* getUserProfile */.VM)();
            case 4:
              profile = _context.sent;
              if (profile) {
                setProfileData({
                  username: profile.username || '',
                  email: profile.email || '',
                  name: profile.name || '',
                  bio: profile.bio || '',
                  avatar: profile.avatar || ''
                });
              }
              _context.next = 12;
              break;
            case 8:
              _context.prev = 8;
              _context.t0 = _context["catch"](0);
              console.error('Error fetching profile:', _context.t0);
              setProfileError('Failed to load profile data');
            case 12:
              _context.prev = 12;
              setLoading(false);
              return _context.finish(12);
            case 15:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 8, 12, 15]]);
      }));
      return function fetchProfile() {
        return _ref.apply(this, arguments);
      };
    }();
    fetchProfile();
  }, []);

  // Handle profile input change
  var handleProfileChange = function handleProfileChange(e) {
    var _e$target = e.target,
      name = _e$target.name,
      value = _e$target.value;
    setProfileData(_objectSpread(_objectSpread({}, profileData), {}, (0,defineProperty/* default */.A)({}, name, value)));

    // Clear success message when form is changed
    setProfileSuccess(false);
  };

  // Handle password input change
  var handlePasswordChange = function handlePasswordChange(e) {
    var _e$target2 = e.target,
      name = _e$target2.name,
      value = _e$target2.value;
    setPasswordData(_objectSpread(_objectSpread({}, passwordData), {}, (0,defineProperty/* default */.A)({}, name, value)));

    // Clear success message when form is changed
    setPasswordSuccess(false);
  };

  // Handle profile form submission
  var handleProfileSubmit = /*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(e) {
      var result;
      return regenerator_default().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            e.preventDefault();
            _context2.prev = 1;
            setLoading(true);
            setProfileError(null);
            setProfileSuccess(false);

            // Update profile
            _context2.next = 7;
            return (0,auth/* updateUserProfile */.eg)(profileData);
          case 7:
            result = _context2.sent;
            if (result.success) {
              setProfileSuccess(true);
            } else {
              setProfileError(result.error || 'Failed to update profile');
            }
            _context2.next = 15;
            break;
          case 11:
            _context2.prev = 11;
            _context2.t0 = _context2["catch"](1);
            console.error('Profile update error:', _context2.t0);
            setProfileError('An unexpected error occurred. Please try again.');
          case 15:
            _context2.prev = 15;
            setLoading(false);
            return _context2.finish(15);
          case 18:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 11, 15, 18]]);
    }));
    return function handleProfileSubmit(_x) {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle password form submission
  var handlePasswordSubmit = /*#__PURE__*/function () {
    var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3(e) {
      var result;
      return regenerator_default().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            e.preventDefault();

            // Validate password
            if (!(passwordData.newPassword !== passwordData.confirmPassword)) {
              _context3.next = 4;
              break;
            }
            setPasswordError('Passwords do not match');
            return _context3.abrupt("return");
          case 4:
            if (!(passwordData.newPassword.length < 8)) {
              _context3.next = 7;
              break;
            }
            setPasswordError('Password must be at least 8 characters long');
            return _context3.abrupt("return");
          case 7:
            _context3.prev = 7;
            setLoading(true);
            setPasswordError(null);
            setPasswordSuccess(false);

            // Change password
            _context3.next = 13;
            return (0,auth/* changePassword */.ec)(passwordData.currentPassword, passwordData.newPassword);
          case 13:
            result = _context3.sent;
            if (result.success) {
              setPasswordSuccess(true);

              // Clear password form
              setPasswordData({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
              });
            } else {
              setPasswordError(result.error || 'Failed to change password');
            }
            _context3.next = 21;
            break;
          case 17:
            _context3.prev = 17;
            _context3.t0 = _context3["catch"](7);
            console.error('Password change error:', _context3.t0);
            setPasswordError('An unexpected error occurred. Please try again.');
          case 21:
            _context3.prev = 21;
            setLoading(false);
            return _context3.finish(21);
          case 24:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[7, 17, 21, 24]]);
    }));
    return function handlePasswordSubmit(_x2) {
      return _ref3.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/react.createElement("div", {
    className: "profile-container"
  }, /*#__PURE__*/react.createElement("div", {
    className: "profile-header"
  }, /*#__PURE__*/react.createElement("h2", null, "User Profile")), /*#__PURE__*/react.createElement("div", {
    className: "profile-tabs"
  }, /*#__PURE__*/react.createElement("button", {
    className: "tab-button ".concat(activeTab === 'profile' ? 'active' : ''),
    onClick: function onClick() {
      return setActiveTab('profile');
    }
  }, "Profile"), /*#__PURE__*/react.createElement("button", {
    className: "tab-button ".concat(activeTab === 'password' ? 'active' : ''),
    onClick: function onClick() {
      return setActiveTab('password');
    }
  }, "Change Password")), /*#__PURE__*/react.createElement("div", {
    className: "profile-content"
  }, activeTab === 'profile' && /*#__PURE__*/react.createElement("div", {
    className: "profile-tab"
  }, profileError && /*#__PURE__*/react.createElement("div", {
    className: "error-message"
  }, profileError), profileSuccess && /*#__PURE__*/react.createElement("div", {
    className: "success-message"
  }, "Profile updated successfully"), /*#__PURE__*/react.createElement("form", {
    onSubmit: handleProfileSubmit,
    className: "profile-form"
  }, /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "username"
  }, "Username"), /*#__PURE__*/react.createElement("input", {
    type: "text",
    id: "username",
    name: "username",
    value: profileData.username,
    onChange: handleProfileChange,
    disabled: loading,
    readOnly: true
  }), /*#__PURE__*/react.createElement("div", {
    className: "field-hint"
  }, "Username cannot be changed")), /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "email"
  }, "Email"), /*#__PURE__*/react.createElement("input", {
    type: "email",
    id: "email",
    name: "email",
    value: profileData.email,
    onChange: handleProfileChange,
    disabled: loading,
    readOnly: true
  }), /*#__PURE__*/react.createElement("div", {
    className: "field-hint"
  }, "Email cannot be changed")), /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "name"
  }, "Full Name"), /*#__PURE__*/react.createElement("input", {
    type: "text",
    id: "name",
    name: "name",
    value: profileData.name,
    onChange: handleProfileChange,
    placeholder: "Enter your full name",
    disabled: loading
  })), /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "bio"
  }, "Bio"), /*#__PURE__*/react.createElement("textarea", {
    id: "bio",
    name: "bio",
    value: profileData.bio,
    onChange: handleProfileChange,
    placeholder: "Tell us about yourself",
    disabled: loading,
    rows: 4
  })), /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "avatar"
  }, "Avatar URL"), /*#__PURE__*/react.createElement("input", {
    type: "text",
    id: "avatar",
    name: "avatar",
    value: profileData.avatar,
    onChange: handleProfileChange,
    placeholder: "Enter avatar URL",
    disabled: loading
  })), /*#__PURE__*/react.createElement("button", {
    type: "submit",
    className: "save-button",
    disabled: loading
  }, loading ? 'Saving...' : 'Save Profile'))), activeTab === 'password' && /*#__PURE__*/react.createElement("div", {
    className: "password-tab"
  }, passwordError && /*#__PURE__*/react.createElement("div", {
    className: "error-message"
  }, passwordError), passwordSuccess && /*#__PURE__*/react.createElement("div", {
    className: "success-message"
  }, "Password changed successfully"), /*#__PURE__*/react.createElement("form", {
    onSubmit: handlePasswordSubmit,
    className: "password-form"
  }, /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "currentPassword"
  }, "Current Password"), /*#__PURE__*/react.createElement("input", {
    type: "password",
    id: "currentPassword",
    name: "currentPassword",
    value: passwordData.currentPassword,
    onChange: handlePasswordChange,
    placeholder: "Enter current password",
    disabled: loading,
    required: true
  })), /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "newPassword"
  }, "New Password"), /*#__PURE__*/react.createElement("input", {
    type: "password",
    id: "newPassword",
    name: "newPassword",
    value: passwordData.newPassword,
    onChange: handlePasswordChange,
    placeholder: "Enter new password",
    disabled: loading,
    required: true
  }), /*#__PURE__*/react.createElement("div", {
    className: "password-requirements"
  }, "Password must be at least 8 characters long")), /*#__PURE__*/react.createElement("div", {
    className: "form-group"
  }, /*#__PURE__*/react.createElement("label", {
    htmlFor: "confirmPassword"
  }, "Confirm Password"), /*#__PURE__*/react.createElement("input", {
    type: "password",
    id: "confirmPassword",
    name: "confirmPassword",
    value: passwordData.confirmPassword,
    onChange: handlePasswordChange,
    placeholder: "Confirm new password",
    disabled: loading,
    required: true
  })), /*#__PURE__*/react.createElement("button", {
    type: "submit",
    className: "save-button",
    disabled: loading
  }, loading ? 'Changing Password...' : 'Change Password')))), /*#__PURE__*/react.createElement("style", {
    jsx: true
  }, "\n        .profile-container {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: var(--spacing-lg);\n        }\n        \n        .profile-header {\n          margin-bottom: var(--spacing-lg);\n          text-align: center;\n        }\n        \n        .profile-tabs {\n          display: flex;\n          border-bottom: 1px solid var(--color-border);\n          margin-bottom: var(--spacing-lg);\n        }\n        \n        .tab-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background: none;\n          border: none;\n          border-bottom: 2px solid transparent;\n          color: var(--color-textSecondary);\n          cursor: pointer;\n          font-size: var(--font-size-md);\n          transition: all var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .tab-button:hover {\n          color: var(--color-text);\n        }\n        \n        .tab-button.active {\n          color: var(--color-primary);\n          border-bottom-color: var(--color-primary);\n        }\n        \n        .profile-content {\n          background-color: var(--color-surface);\n          border-radius: var(--border-radius-lg);\n          box-shadow: var(--shadow-sm);\n          padding: var(--spacing-lg);\n        }\n        \n        .error-message {\n          padding: var(--spacing-sm);\n          margin-bottom: var(--spacing-md);\n          background-color: rgba(var(--color-error-rgb), 0.1);\n          border: 1px solid var(--color-error);\n          border-radius: var(--border-radius-md);\n          color: var(--color-error);\n          font-size: var(--font-size-sm);\n        }\n        \n        .success-message {\n          padding: var(--spacing-sm);\n          margin-bottom: var(--spacing-md);\n          background-color: rgba(var(--color-success-rgb), 0.1);\n          border: 1px solid var(--color-success);\n          border-radius: var(--border-radius-md);\n          color: var(--color-success);\n          font-size: var(--font-size-sm);\n        }\n        \n        .profile-form, .password-form {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-md);\n        }\n        \n        .form-group {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-xs);\n        }\n        \n        .form-group label {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n        }\n        \n        .form-group input, .form-group textarea {\n          padding: var(--spacing-sm) var(--spacing-md);\n          border: 1px solid var(--color-border);\n          border-radius: var(--border-radius-md);\n          background-color: var(--color-background);\n          color: var(--color-text);\n          font-size: var(--font-size-md);\n          transition: border-color var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .form-group input:focus, .form-group textarea:focus {\n          outline: none;\n          border-color: var(--color-primary);\n        }\n        \n        .form-group input[readonly] {\n          background-color: var(--color-surface);\n          color: var(--color-textSecondary);\n          cursor: not-allowed;\n        }\n        \n        .field-hint, .password-requirements {\n          font-size: var(--font-size-xs);\n          color: var(--color-textSecondary);\n          margin-top: var(--spacing-xs);\n        }\n        \n        .save-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background-color: var(--color-primary);\n          color: white;\n          border: none;\n          border-radius: var(--border-radius-md);\n          font-size: var(--font-size-md);\n          font-weight: var(--font-weight-medium);\n          cursor: pointer;\n          transition: background-color var(--transition-fast) var(--transition-timing-function);\n          align-self: flex-start;\n        }\n        \n        .save-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 80%, black);\n        }\n        \n        .save-button:disabled {\n          background-color: var(--color-border);\n          color: var(--color-textSecondary);\n          cursor: not-allowed;\n        }\n      "));
};
/* harmony default export */ const auth_Profile = (Profile);
;// ./src/pages/SettingsPage.js






/**
 * Settings Page
 * 
 * This page allows users to configure application settings.
 */
var SettingsPage = function SettingsPage() {
  var _useAuth = (0,AuthContext/* useAuth */.As)(),
    user = _useAuth.user;
  var _useState = (0,react.useState)('profile'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];

  // Settings tabs
  var tabs = [{
    id: 'profile',
    label: 'Profile',
    icon: '👤'
  }, {
    id: 'appearance',
    label: 'Appearance',
    icon: '🎨'
  }, {
    id: 'notifications',
    label: 'Notifications',
    icon: '🔔'
  }, {
    id: 'security',
    label: 'Security',
    icon: '🔒'
  }, {
    id: 'integrations',
    label: 'Integrations',
    icon: '🔌'
  }];
  return /*#__PURE__*/react.createElement("div", {
    className: "settings-container"
  }, /*#__PURE__*/react.createElement("div", {
    className: "settings-header"
  }, /*#__PURE__*/react.createElement("h1", null, "Settings"), /*#__PURE__*/react.createElement("p", {
    className: "settings-subtitle"
  }, "Manage your account and application preferences")), /*#__PURE__*/react.createElement("div", {
    className: "settings-content"
  }, /*#__PURE__*/react.createElement("div", {
    className: "settings-sidebar"
  }, /*#__PURE__*/react.createElement("div", {
    className: "user-info"
  }, /*#__PURE__*/react.createElement("div", {
    className: "user-avatar"
  }, user !== null && user !== void 0 && user.avatar ? /*#__PURE__*/react.createElement("img", {
    src: user.avatar,
    alt: user.name || user.username
  }) : /*#__PURE__*/react.createElement("div", {
    className: "avatar-placeholder"
  }, ((user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username) || 'User').charAt(0).toUpperCase())), /*#__PURE__*/react.createElement("div", {
    className: "user-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "user-name"
  }, (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username) || 'User'), /*#__PURE__*/react.createElement("div", {
    className: "user-email"
  }, (user === null || user === void 0 ? void 0 : user.email) || 'No email'))), /*#__PURE__*/react.createElement("nav", {
    className: "settings-nav"
  }, /*#__PURE__*/react.createElement("ul", {
    className: "nav-list"
  }, tabs.map(function (tab) {
    return /*#__PURE__*/react.createElement("li", {
      key: tab.id,
      className: "nav-item"
    }, /*#__PURE__*/react.createElement("button", {
      className: "nav-button ".concat(activeTab === tab.id ? 'active' : ''),
      onClick: function onClick() {
        return setActiveTab(tab.id);
      }
    }, /*#__PURE__*/react.createElement("span", {
      className: "nav-icon"
    }, tab.icon), /*#__PURE__*/react.createElement("span", {
      className: "nav-label"
    }, tab.label)));
  })))), /*#__PURE__*/react.createElement("div", {
    className: "settings-main"
  }, activeTab === 'profile' && /*#__PURE__*/react.createElement("div", {
    className: "settings-tab"
  }, /*#__PURE__*/react.createElement("h2", null, "Profile Settings"), /*#__PURE__*/react.createElement(auth_Profile, null)), activeTab === 'appearance' && /*#__PURE__*/react.createElement("div", {
    className: "settings-tab"
  }, /*#__PURE__*/react.createElement("h2", null, "Appearance Settings"), /*#__PURE__*/react.createElement("div", {
    className: "appearance-settings"
  }, /*#__PURE__*/react.createElement(ThemeManager/* ThemeCustomizer */.rV, null))), activeTab === 'notifications' && /*#__PURE__*/react.createElement("div", {
    className: "settings-tab"
  }, /*#__PURE__*/react.createElement("h2", null, "Notification Settings"), /*#__PURE__*/react.createElement("div", {
    className: "notification-settings"
  }, /*#__PURE__*/react.createElement("div", {
    className: "settings-section"
  }, /*#__PURE__*/react.createElement("h3", null, "Email Notifications"), /*#__PURE__*/react.createElement("div", {
    className: "settings-options"
  }, /*#__PURE__*/react.createElement("div", {
    className: "settings-option"
  }, /*#__PURE__*/react.createElement("label", {
    className: "toggle-switch"
  }, /*#__PURE__*/react.createElement("input", {
    type: "checkbox",
    defaultChecked: true
  }), /*#__PURE__*/react.createElement("span", {
    className: "toggle-slider"
  })), /*#__PURE__*/react.createElement("div", {
    className: "option-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "option-label"
  }, "Project updates"), /*#__PURE__*/react.createElement("div", {
    className: "option-description"
  }, "Receive emails about updates to your projects"))), /*#__PURE__*/react.createElement("div", {
    className: "settings-option"
  }, /*#__PURE__*/react.createElement("label", {
    className: "toggle-switch"
  }, /*#__PURE__*/react.createElement("input", {
    type: "checkbox",
    defaultChecked: true
  }), /*#__PURE__*/react.createElement("span", {
    className: "toggle-slider"
  })), /*#__PURE__*/react.createElement("div", {
    className: "option-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "option-label"
  }, "Security alerts"), /*#__PURE__*/react.createElement("div", {
    className: "option-description"
  }, "Receive emails about security issues"))), /*#__PURE__*/react.createElement("div", {
    className: "settings-option"
  }, /*#__PURE__*/react.createElement("label", {
    className: "toggle-switch"
  }, /*#__PURE__*/react.createElement("input", {
    type: "checkbox"
  }), /*#__PURE__*/react.createElement("span", {
    className: "toggle-slider"
  })), /*#__PURE__*/react.createElement("div", {
    className: "option-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "option-label"
  }, "Marketing emails"), /*#__PURE__*/react.createElement("div", {
    className: "option-description"
  }, "Receive emails about new features and offers"))))), /*#__PURE__*/react.createElement("div", {
    className: "settings-section"
  }, /*#__PURE__*/react.createElement("h3", null, "In-App Notifications"), /*#__PURE__*/react.createElement("div", {
    className: "settings-options"
  }, /*#__PURE__*/react.createElement("div", {
    className: "settings-option"
  }, /*#__PURE__*/react.createElement("label", {
    className: "toggle-switch"
  }, /*#__PURE__*/react.createElement("input", {
    type: "checkbox",
    defaultChecked: true
  }), /*#__PURE__*/react.createElement("span", {
    className: "toggle-slider"
  })), /*#__PURE__*/react.createElement("div", {
    className: "option-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "option-label"
  }, "Project comments"), /*#__PURE__*/react.createElement("div", {
    className: "option-description"
  }, "Receive notifications when someone comments on your projects"))), /*#__PURE__*/react.createElement("div", {
    className: "settings-option"
  }, /*#__PURE__*/react.createElement("label", {
    className: "toggle-switch"
  }, /*#__PURE__*/react.createElement("input", {
    type: "checkbox",
    defaultChecked: true
  }), /*#__PURE__*/react.createElement("span", {
    className: "toggle-slider"
  })), /*#__PURE__*/react.createElement("div", {
    className: "option-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "option-label"
  }, "Project shares"), /*#__PURE__*/react.createElement("div", {
    className: "option-description"
  }, "Receive notifications when someone shares a project with you"))))))), activeTab === 'security' && /*#__PURE__*/react.createElement("div", {
    className: "settings-tab"
  }, /*#__PURE__*/react.createElement("h2", null, "Security Settings"), /*#__PURE__*/react.createElement("div", {
    className: "security-settings"
  }, /*#__PURE__*/react.createElement("div", {
    className: "settings-section"
  }, /*#__PURE__*/react.createElement("h3", null, "Two-Factor Authentication"), /*#__PURE__*/react.createElement("p", {
    className: "section-description"
  }, "Add an extra layer of security to your account by requiring a verification code in addition to your password."), /*#__PURE__*/react.createElement("button", {
    className: "primary-button"
  }, "Enable 2FA")), /*#__PURE__*/react.createElement("div", {
    className: "settings-section"
  }, /*#__PURE__*/react.createElement("h3", null, "Session Management"), /*#__PURE__*/react.createElement("p", {
    className: "section-description"
  }, "Manage your active sessions and sign out from other devices."), /*#__PURE__*/react.createElement("div", {
    className: "session-list"
  }, /*#__PURE__*/react.createElement("div", {
    className: "session-item current"
  }, /*#__PURE__*/react.createElement("div", {
    className: "session-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "session-device"
  }, "Current Browser (Chrome)"), /*#__PURE__*/react.createElement("div", {
    className: "session-info"
  }, "Windows \u2022 Last active: Just now")), /*#__PURE__*/react.createElement("div", {
    className: "session-actions"
  }, /*#__PURE__*/react.createElement("span", {
    className: "current-label"
  }, "Current"))), /*#__PURE__*/react.createElement("div", {
    className: "session-item"
  }, /*#__PURE__*/react.createElement("div", {
    className: "session-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "session-device"
  }, "Mobile App"), /*#__PURE__*/react.createElement("div", {
    className: "session-info"
  }, "Android \u2022 Last active: 2 hours ago")), /*#__PURE__*/react.createElement("div", {
    className: "session-actions"
  }, /*#__PURE__*/react.createElement("button", {
    className: "text-button"
  }, "Sign Out")))), /*#__PURE__*/react.createElement("button", {
    className: "secondary-button"
  }, "Sign Out All Other Devices")))), activeTab === 'integrations' && /*#__PURE__*/react.createElement("div", {
    className: "settings-tab"
  }, /*#__PURE__*/react.createElement("h2", null, "Integrations"), /*#__PURE__*/react.createElement("div", {
    className: "integrations-settings"
  }, /*#__PURE__*/react.createElement("div", {
    className: "settings-section"
  }, /*#__PURE__*/react.createElement("h3", null, "Connected Services"), /*#__PURE__*/react.createElement("p", {
    className: "section-description"
  }, "Connect your account to other services to import or export data."), /*#__PURE__*/react.createElement("div", {
    className: "integration-list"
  }, /*#__PURE__*/react.createElement("div", {
    className: "integration-item"
  }, /*#__PURE__*/react.createElement("div", {
    className: "integration-icon"
  }, /*#__PURE__*/react.createElement("span", {
    className: "github-icon"
  }, "G")), /*#__PURE__*/react.createElement("div", {
    className: "integration-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "integration-name"
  }, "GitHub"), /*#__PURE__*/react.createElement("div", {
    className: "integration-status"
  }, "Not connected")), /*#__PURE__*/react.createElement("div", {
    className: "integration-actions"
  }, /*#__PURE__*/react.createElement("button", {
    className: "primary-button"
  }, "Connect"))), /*#__PURE__*/react.createElement("div", {
    className: "integration-item"
  }, /*#__PURE__*/react.createElement("div", {
    className: "integration-icon"
  }, /*#__PURE__*/react.createElement("span", {
    className: "google-icon"
  }, "G")), /*#__PURE__*/react.createElement("div", {
    className: "integration-details"
  }, /*#__PURE__*/react.createElement("div", {
    className: "integration-name"
  }, "Google Drive"), /*#__PURE__*/react.createElement("div", {
    className: "integration-status"
  }, "Not connected")), /*#__PURE__*/react.createElement("div", {
    className: "integration-actions"
  }, /*#__PURE__*/react.createElement("button", {
    className: "primary-button"
  }, "Connect"))))), /*#__PURE__*/react.createElement("div", {
    className: "settings-section"
  }, /*#__PURE__*/react.createElement("h3", null, "API Access"), /*#__PURE__*/react.createElement("p", {
    className: "section-description"
  }, "Manage your API keys and access tokens."), /*#__PURE__*/react.createElement("button", {
    className: "primary-button"
  }, "Generate API Key")))))), /*#__PURE__*/react.createElement("style", {
    jsx: true
  }, "\n        .settings-container {\n          padding: var(--spacing-lg);\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n        \n        .settings-header {\n          margin-bottom: var(--spacing-lg);\n        }\n        \n        .settings-subtitle {\n          color: var(--color-textSecondary);\n          margin-top: var(--spacing-xs);\n        }\n        \n        .settings-content {\n          display: grid;\n          grid-template-columns: 250px 1fr;\n          gap: var(--spacing-lg);\n        }\n        \n        .settings-sidebar {\n          background-color: var(--color-surface);\n          border-radius: var(--border-radius-md);\n          box-shadow: var(--shadow-sm);\n          overflow: hidden;\n        }\n        \n        .user-info {\n          display: flex;\n          align-items: center;\n          padding: var(--spacing-md);\n          border-bottom: 1px solid var(--color-border);\n        }\n        \n        .user-avatar {\n          width: 50px;\n          height: 50px;\n          border-radius: 50%;\n          overflow: hidden;\n          margin-right: var(--spacing-md);\n        }\n        \n        .user-avatar img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n        \n        .avatar-placeholder {\n          width: 100%;\n          height: 100%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background-color: var(--color-primary);\n          color: white;\n          font-size: var(--font-size-lg);\n          font-weight: var(--font-weight-bold);\n        }\n        \n        .user-details {\n          overflow: hidden;\n        }\n        \n        .user-name {\n          font-weight: var(--font-weight-medium);\n          margin-bottom: var(--spacing-xs);\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n        \n        .user-email {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n        \n        .settings-nav {\n          padding: var(--spacing-md);\n        }\n        \n        .nav-list {\n          list-style: none;\n          padding: 0;\n          margin: 0;\n        }\n        \n        .nav-item {\n          margin-bottom: var(--spacing-xs);\n        }\n        \n        .nav-button {\n          display: flex;\n          align-items: center;\n          width: 100%;\n          padding: var(--spacing-sm) var(--spacing-md);\n          background: none;\n          border: none;\n          border-radius: var(--border-radius-md);\n          color: var(--color-text);\n          cursor: pointer;\n          text-align: left;\n          transition: background-color var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .nav-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);\n        }\n        \n        .nav-button.active {\n          background-color: color-mix(in srgb, var(--color-primary) 15%, transparent);\n          color: var(--color-primary);\n          font-weight: var(--font-weight-medium);\n        }\n        \n        .nav-icon {\n          margin-right: var(--spacing-sm);\n          font-size: var(--font-size-lg);\n        }\n        \n        .settings-main {\n          background-color: var(--color-surface);\n          border-radius: var(--border-radius-md);\n          box-shadow: var(--shadow-sm);\n          padding: var(--spacing-lg);\n        }\n        \n        .settings-tab h2 {\n          margin-top: 0;\n          margin-bottom: var(--spacing-lg);\n          padding-bottom: var(--spacing-sm);\n          border-bottom: 1px solid var(--color-border);\n        }\n        \n        .settings-section {\n          margin-bottom: var(--spacing-lg);\n        }\n        \n        .settings-section h3 {\n          margin-bottom: var(--spacing-sm);\n        }\n        \n        .section-description {\n          color: var(--color-textSecondary);\n          margin-bottom: var(--spacing-md);\n        }\n        \n        .settings-options {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-md);\n        }\n        \n        .settings-option {\n          display: flex;\n          align-items: flex-start;\n          gap: var(--spacing-md);\n        }\n        \n        .option-details {\n          flex: 1;\n        }\n        \n        .option-label {\n          font-weight: var(--font-weight-medium);\n          margin-bottom: var(--spacing-xs);\n        }\n        \n        .option-description {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n        }\n        \n        .toggle-switch {\n          position: relative;\n          display: inline-block;\n          width: 40px;\n          height: 24px;\n        }\n        \n        .toggle-switch input {\n          opacity: 0;\n          width: 0;\n          height: 0;\n        }\n        \n        .toggle-slider {\n          position: absolute;\n          cursor: pointer;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background-color: var(--color-border);\n          transition: var(--transition-fast);\n          border-radius: 34px;\n        }\n        \n        .toggle-slider:before {\n          position: absolute;\n          content: \"\";\n          height: 16px;\n          width: 16px;\n          left: 4px;\n          bottom: 4px;\n          background-color: white;\n          transition: var(--transition-fast);\n          border-radius: 50%;\n        }\n        \n        input:checked + .toggle-slider {\n          background-color: var(--color-primary);\n        }\n        \n        input:checked + .toggle-slider:before {\n          transform: translateX(16px);\n        }\n        \n        .primary-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background-color: var(--color-primary);\n          color: white;\n          border: none;\n          border-radius: var(--border-radius-md);\n          cursor: pointer;\n          transition: background-color var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .primary-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 80%, black);\n        }\n        \n        .secondary-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background-color: transparent;\n          color: var(--color-primary);\n          border: 1px solid var(--color-primary);\n          border-radius: var(--border-radius-md);\n          cursor: pointer;\n          transition: all var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .secondary-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);\n        }\n        \n        .text-button {\n          background: none;\n          border: none;\n          color: var(--color-primary);\n          cursor: pointer;\n          padding: 0;\n        }\n        \n        .text-button:hover {\n          text-decoration: underline;\n        }\n        \n        .session-list, .integration-list {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-md);\n          margin-bottom: var(--spacing-md);\n        }\n        \n        .session-item, .integration-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: var(--spacing-md);\n          border: 1px solid var(--color-border);\n          border-radius: var(--border-radius-md);\n        }\n        \n        .session-item.current {\n          border-color: var(--color-primary);\n          background-color: color-mix(in srgb, var(--color-primary) 5%, transparent);\n        }\n        \n        .session-device, .integration-name {\n          font-weight: var(--font-weight-medium);\n          margin-bottom: var(--spacing-xs);\n        }\n        \n        .session-info, .integration-status {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n        }\n        \n        .current-label {\n          font-size: var(--font-size-xs);\n          color: var(--color-primary);\n          border: 1px solid var(--color-primary);\n          padding: 2px 8px;\n          border-radius: var(--border-radius-sm);\n        }\n        \n        .integration-icon {\n          width: 40px;\n          height: 40px;\n          border-radius: var(--border-radius-md);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background-color: var(--color-border);\n          margin-right: var(--spacing-md);\n        }\n        \n        .github-icon, .google-icon {\n          font-weight: var(--font-weight-bold);\n        }\n        \n        @media (max-width: 768px) {\n          .settings-content {\n            grid-template-columns: 1fr;\n          }\n          \n          .settings-sidebar {\n            margin-bottom: var(--spacing-md);\n          }\n        }\n      "));
};
/* harmony default export */ const pages_SettingsPage = (SettingsPage);

/***/ })

}]);