"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4415],{

/***/ 43771:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ pages_AppBuilderEnhanced)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./src/redux/reducers/uiReducer.js
var uiReducer = __webpack_require__(85331);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./src/contexts/EnhancedThemeContext.js
var EnhancedThemeContext = __webpack_require__(82569);
// EXTERNAL MODULE: ./src/components/layout/EnhancedHeader.js
var layout_EnhancedHeader = __webpack_require__(6827);
;// ./src/components/layout/EnhancedLayout.js

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;






var Content = es/* Layout */.PE.Content,
  Footer = es/* Layout */.PE.Footer;

// Styled components
var StyledLayout = (0,styled_components_browser_esm/* default */.Ay)(es/* Layout */.PE)(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  min-height: 100vh;\n  background-color: var(--color-background);\n  transition: background-color 0.3s ease;\n"])));
var StyledContent = (0,styled_components_browser_esm/* default */.Ay)(Content)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 24px;\n  background-color: var(--color-background);\n  min-height: calc(100vh - 64px - 70px);\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 12px;\n  }\n"])));
var StyledFooter = (0,styled_components_browser_esm/* default */.Ay)(Footer)(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  text-align: center;\n  padding: 24px;\n  background-color: var(--color-surface);\n  border-top: 1px solid var(--color-border-light);\n  color: var(--color-text-secondary);\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  .footer-content {\n    max-width: 1200px;\n    margin: 0 auto;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-wrap: wrap;\n    gap: 16px;\n\n    @media (max-width: 768px) {\n      flex-direction: column;\n      text-align: center;\n    }\n  }\n\n  .footer-links {\n    display: flex;\n    gap: 24px;\n    align-items: center;\n\n    @media (max-width: 768px) {\n      gap: 16px;\n    }\n\n    a {\n      color: var(--color-text-secondary);\n      text-decoration: none;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: var(--color-primary);\n      }\n    }\n  }\n\n  .footer-info {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: var(--color-text-tertiary);\n    font-size: 12px;\n  }\n"])));
var BackToTopButton = (0,styled_components_browser_esm/* default */.Ay)(es/* BackTop */.XT)(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  .ant-back-top-content {\n    background-color: var(--color-primary);\n    color: white;\n    border-radius: 50%;\n    width: 48px;\n    height: 48px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: var(--shadow-lg);\n    transition: all 0.3s ease;\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      transform: scale(1.1);\n    }\n\n    .anticon {\n      font-size: 16px;\n    }\n  }\n"])));
var MainContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n"])));
var ContentWrapper = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  background-color: var(--color-background);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    border-radius: var(--border-radius-md);\n  }\n"])));
var EnhancedLayout = function EnhancedLayout(_ref) {
  var children = _ref.children,
    headerTitle = _ref.headerTitle,
    _ref$showStatus = _ref.showStatus,
    showStatus = _ref$showStatus === void 0 ? true : _ref$showStatus,
    onLogoClick = _ref.onLogoClick,
    headerActions = _ref.headerActions,
    _ref$footerLinks = _ref.footerLinks,
    footerLinks = _ref$footerLinks === void 0 ? [] : _ref$footerLinks,
    _ref$showBackToTop = _ref.showBackToTop,
    showBackToTop = _ref$showBackToTop === void 0 ? true : _ref$showBackToTop;
  var _useEnhancedTheme = useEnhancedTheme(),
    isDarkMode = _useEnhancedTheme.isDarkMode;
  var defaultFooterLinks = [{
    href: '/about',
    label: 'About'
  }, {
    href: '/docs',
    label: 'Documentation'
  }, {
    href: '/support',
    label: 'Support'
  }, {
    href: '/privacy',
    label: 'Privacy'
  }];
  var links = footerLinks.length > 0 ? footerLinks : defaultFooterLinks;
  return /*#__PURE__*/React.createElement(StyledLayout, null, /*#__PURE__*/React.createElement(EnhancedHeader, {
    title: headerTitle,
    showStatus: showStatus,
    onLogoClick: onLogoClick
  }, headerActions), /*#__PURE__*/React.createElement(StyledContent, {
    id: "main-content"
  }, /*#__PURE__*/React.createElement(MainContainer, null, /*#__PURE__*/React.createElement(ContentWrapper, null, children))), /*#__PURE__*/React.createElement(StyledFooter, null, /*#__PURE__*/React.createElement("div", {
    className: "footer-content"
  }, /*#__PURE__*/React.createElement("div", null, "App Builder \xA9", new Date().getFullYear(), " - Build with ease"), /*#__PURE__*/React.createElement("div", {
    className: "footer-links"
  }, links.map(function (link, index) {
    return /*#__PURE__*/React.createElement("a", {
      key: index,
      href: link.href,
      target: link.external ? '_blank' : undefined,
      rel: link.external ? 'noopener noreferrer' : undefined
    }, link.label);
  })), /*#__PURE__*/React.createElement("div", {
    className: "footer-info"
  }, /*#__PURE__*/React.createElement("span", null, "Theme: ", isDarkMode ? 'Dark' : 'Light')))), showBackToTop && /*#__PURE__*/React.createElement(BackToTopButton, null, /*#__PURE__*/React.createElement("div", {
    className: "ant-back-top-content"
  }, /*#__PURE__*/React.createElement(UpOutlined, null))));
};
/* harmony default export */ const layout_EnhancedLayout = ((/* unused pure expression or super */ null && (EnhancedLayout)));
// EXTERNAL MODULE: ./src/hooks/useRealTimePreview.js
var useRealTimePreview = __webpack_require__(79459);
// EXTERNAL MODULE: ./src/hooks/useResponsivePreview.js
var useResponsivePreview = __webpack_require__(17050);
// EXTERNAL MODULE: ./src/hooks/useCollaborativePreview.js
var useCollaborativePreview = __webpack_require__(25577);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
;// ./src/components/performance/MemoizedComponent.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/**
 * MemoizedComponent higher-order component
 * Wraps a component with React.memo and provides optimized props
 * 
 * @param {React.ComponentType} Component - The component to memoize
 * @param {Object} options - Options for memoization
 * @param {Function} options.areEqual - Custom comparison function for React.memo
 * @param {Array} options.memoizedProps - List of prop names to memoize
 * @param {Array} options.callbackProps - List of prop names to wrap with useCallback
 * @returns {React.MemoExoticComponent} Memoized component
 */
var MemoizedComponent = function MemoizedComponent(Component) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var areEqual = options.areEqual,
    _options$memoizedProp = options.memoizedProps,
    memoizedProps = _options$memoizedProp === void 0 ? [] : _options$memoizedProp,
    _options$callbackProp = options.callbackProps,
    callbackProps = _options$callbackProp === void 0 ? [] : _options$callbackProp;

  // Create memoized component with custom comparison function
  var MemoComponent = /*#__PURE__*/memo(Component, areEqual);

  // Return a wrapper component that memoizes props
  return function (props) {
    // Memoize specified props
    var memoizedPropValues = useMemo(function () {
      var result = {};
      memoizedProps.forEach(function (propName) {
        if (props[propName] !== undefined) {
          result[propName] = props[propName];
        }
      });
      return result;
    }, [props].concat(_toConsumableArray(memoizedProps.map(function (propName) {
      return props[propName];
    }))));

    // Wrap callback props with useCallback
    var callbackPropValues = {};
    callbackProps.forEach(function (propName) {
      if (typeof props[propName] === 'function') {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        callbackPropValues[propName] = useCallback(props[propName], [props[propName]]);
      }
    });

    // Combine all props
    var optimizedProps = _objectSpread(_objectSpread(_objectSpread({}, props), memoizedPropValues), callbackPropValues);
    return /*#__PURE__*/React.createElement(MemoComponent, optimizedProps);
  };
};
/* harmony default export */ const performance_MemoizedComponent = ((/* unused pure expression or super */ null && (MemoizedComponent)));
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
;// ./src/components/performance/LazyLoadComponent.js




var _excluded = (/* unused pure expression or super */ null && (["importFunc", "fallback", "minHeight", "visibilityThreshold", "loadImmediately"]));
var LazyLoadComponent_templateObject, LazyLoadComponent_templateObject2, LazyLoadComponent_templateObject3;





// Styled components
var LoadingContainer = styled_components_browser_esm/* default */.Ay.div(LazyLoadComponent_templateObject || (LazyLoadComponent_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: ", ";\n  width: 100%;\n"])), function (props) {
  return props.minHeight || '200px';
});
var ErrorContainer = styled_components_browser_esm/* default */.Ay.div(LazyLoadComponent_templateObject2 || (LazyLoadComponent_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: ", ";\n  width: 100%;\n  color: ", ";\n  text-align: center;\n  padding: 16px;\n"])), function (props) {
  return props.minHeight || '200px';
}, function (props) {
  return props.theme.colorPalette.error;
});
var RetryButton = styled_components_browser_esm/* default */.Ay.button(LazyLoadComponent_templateObject3 || (LazyLoadComponent_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-top: 16px;\n  padding: 8px 16px;\n  background-color: ", ";\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  \n  &:hover {\n    background-color: ", ";\n  }\n"])), function (props) {
  return props.theme.colorPalette.primary;
}, function (props) {
  return props.theme.colorPalette.primaryDark;
});

/**
 * LazyLoadComponent
 * A component that lazily loads another component when it becomes visible in the viewport
 * 
 * @param {Object} props - Component props
 * @param {Function} props.importFunc - Function that returns a dynamic import (e.g., () => import('./MyComponent'))
 * @param {React.ReactNode} props.fallback - Fallback UI to show while loading
 * @param {string} props.minHeight - Minimum height of the loading container
 * @param {boolean} props.visibilityThreshold - Visibility threshold for intersection observer (0-1)
 * @param {boolean} props.loadImmediately - Whether to load the component immediately without waiting for visibility
 * @returns {React.ReactElement} Lazy loaded component
 */
var LazyLoadComponent = function LazyLoadComponent(_ref) {
  var importFunc = _ref.importFunc,
    _ref$fallback = _ref.fallback,
    fallback = _ref$fallback === void 0 ? null : _ref$fallback,
    _ref$minHeight = _ref.minHeight,
    minHeight = _ref$minHeight === void 0 ? '200px' : _ref$minHeight,
    _ref$visibilityThresh = _ref.visibilityThreshold,
    visibilityThreshold = _ref$visibilityThresh === void 0 ? 0.1 : _ref$visibilityThresh,
    _ref$loadImmediately = _ref.loadImmediately,
    loadImmediately = _ref$loadImmediately === void 0 ? false : _ref$loadImmediately,
    props = _objectWithoutProperties(_ref, _excluded);
  var _useState = useState(loadImmediately),
    _useState2 = _slicedToArray(_useState, 2),
    shouldLoad = _useState2[0],
    setShouldLoad = _useState2[1];
  var _useState3 = useState(null),
    _useState4 = _slicedToArray(_useState3, 2),
    Component = _useState4[0],
    setComponent = _useState4[1];
  var _useState5 = useState(null),
    _useState6 = _slicedToArray(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];
  var _useState7 = useState(0),
    _useState8 = _slicedToArray(_useState7, 2),
    retryCount = _useState8[0],
    setRetryCount = _useState8[1];

  // Create ref for the container element
  var containerRef = React.useRef(null);

  // Set up intersection observer to detect when component is visible
  useEffect(function () {
    if (loadImmediately) return;
    var observer = new IntersectionObserver(function (_ref2) {
      var _ref3 = _slicedToArray(_ref2, 1),
        entry = _ref3[0];
      if (entry.isIntersecting) {
        setShouldLoad(true);
        observer.disconnect();
      }
    }, {
      threshold: visibilityThreshold
    });
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }
    return function () {
      observer.disconnect();
    };
  }, [loadImmediately, visibilityThreshold]);

  // Load the component when it should be loaded
  useEffect(function () {
    if (!shouldLoad) return;
    var isMounted = true;
    var loadComponent = /*#__PURE__*/function () {
      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {
        var module, LoadedComponent;
        return _regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return importFunc();
            case 3:
              module = _context.sent;
              LoadedComponent = module["default"] || module;
              if (isMounted) {
                setComponent(function () {
                  return LoadedComponent;
                });
                setError(null);
              }
              _context.next = 11;
              break;
            case 8:
              _context.prev = 8;
              _context.t0 = _context["catch"](0);
              if (isMounted) {
                console.error('Error loading component:', _context.t0);
                setError(_context.t0);
              }
            case 11:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 8]]);
      }));
      return function loadComponent() {
        return _ref4.apply(this, arguments);
      };
    }();
    loadComponent();
    return function () {
      isMounted = false;
    };
  }, [importFunc, shouldLoad, retryCount]);

  // Handle retry
  var handleRetry = function handleRetry() {
    setError(null);
    setRetryCount(function (count) {
      return count + 1;
    });
  };

  // If the component shouldn't load yet, show a placeholder
  if (!shouldLoad) {
    return /*#__PURE__*/React.createElement("div", {
      ref: containerRef,
      style: {
        minHeight: minHeight
      }
    }, fallback || /*#__PURE__*/React.createElement(LoadingContainer, {
      minHeight: minHeight
    }, /*#__PURE__*/React.createElement(Spin, {
      size: "large"
    })));
  }

  // If there was an error loading the component, show an error message
  if (error) {
    return /*#__PURE__*/React.createElement(ErrorContainer, {
      minHeight: minHeight
    }, /*#__PURE__*/React.createElement("div", null, "Failed to load component"), /*#__PURE__*/React.createElement(RetryButton, {
      onClick: handleRetry
    }, "Retry"));
  }

  // If the component is still loading, show a loading indicator
  if (!Component) {
    return /*#__PURE__*/React.createElement(LoadingContainer, {
      minHeight: minHeight
    }, /*#__PURE__*/React.createElement(Spin, {
      size: "large"
    }));
  }

  // Render the loaded component
  return /*#__PURE__*/React.createElement(Component, props);
};
/* harmony default export */ const performance_LazyLoadComponent = ((/* unused pure expression or super */ null && (LazyLoadComponent)));
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
;// ./src/components/performance/VirtualList.js





var VirtualList_excluded = (/* unused pure expression or super */ null && (["items", "renderItem", "itemHeight", "height", "overscan"]));
var VirtualList_templateObject, VirtualList_templateObject2;
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }



// Styled components
var VirtualListContainer = styled_components_browser_esm/* default */.Ay.div(VirtualList_templateObject || (VirtualList_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  overflow-y: auto;\n  width: 100%;\n  height: ", ";\n"])), function (props) {
  return props.height || '400px';
});
var VirtualListContent = styled_components_browser_esm/* default */.Ay.div(VirtualList_templateObject2 || (VirtualList_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n"])));

/**
 * VirtualList component
 * Efficiently renders large lists by only rendering items that are visible in the viewport
 *
 * @param {Object} props - Component props
 * @param {Array} props.items - Array of items to render
 * @param {Function} props.renderItem - Function to render each item
 * @param {number} props.itemHeight - Height of each item in pixels
 * @param {string} props.height - Height of the list container
 * @param {number} props.overscan - Number of items to render outside of the visible area
 * @returns {React.ReactElement} Virtual list component
 */
var VirtualList = function VirtualList(_ref) {
  var _ref$items = _ref.items,
    items = _ref$items === void 0 ? [] : _ref$items,
    renderItem = _ref.renderItem,
    _ref$itemHeight = _ref.itemHeight,
    itemHeight = _ref$itemHeight === void 0 ? 50 : _ref$itemHeight,
    _ref$height = _ref.height,
    height = _ref$height === void 0 ? '400px' : _ref$height,
    _ref$overscan = _ref.overscan,
    overscan = _ref$overscan === void 0 ? 5 : _ref$overscan,
    props = _objectWithoutProperties(_ref, VirtualList_excluded);
  var _useState = useState(0),
    _useState2 = _slicedToArray(_useState, 2),
    scrollTop = _useState2[0],
    setScrollTop = _useState2[1];
  var _useState3 = useState(0),
    _useState4 = _slicedToArray(_useState3, 2),
    containerHeight = _useState4[0],
    setContainerHeight = _useState4[1];
  var containerRef = useRef(null);

  // Calculate total height of all items
  var totalHeight = items.length * itemHeight;

  // Calculate range of visible items
  var startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  var endIndex = Math.min(items.length - 1, Math.floor((scrollTop + containerHeight) / itemHeight) + overscan);

  // Get visible items
  var visibleItems = items.slice(startIndex, endIndex + 1);

  // Handle scroll event
  var handleScroll = useCallback(function () {
    if (containerRef.current) {
      setScrollTop(containerRef.current.scrollTop);
    }
  }, []);

  // Measure container height on mount and resize
  useEffect(function () {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight);
      var resizeObserver = new ResizeObserver(function (entries) {
        var _iterator = _createForOfIteratorHelper(entries),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var entry = _step.value;
            setContainerHeight(entry.contentRect.height);
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      });
      resizeObserver.observe(containerRef.current);
      return function () {
        resizeObserver.disconnect();
      };
    }
  }, []);

  // Add scroll event listener
  useEffect(function () {
    var container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return function () {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);
  return /*#__PURE__*/React.createElement(VirtualListContainer, _extends({
    ref: containerRef,
    height: height
  }, props), /*#__PURE__*/React.createElement(VirtualListContent, {
    style: {
      height: "".concat(totalHeight, "px"),
      pointerEvents: 'none'
    }
  }, /*#__PURE__*/React.createElement("div", {
    style: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      transform: "translateY(".concat(startIndex * itemHeight, "px)"),
      pointerEvents: 'auto'
    }
  }, visibleItems.map(function (item, index) {
    // Generate a more unique key using item properties if available, or a combination of indices
    var itemKey = item.id || (_typeof(item) === 'object' && item !== null && 'key' in item ? item.key : null) || "item-".concat(startIndex + index, "-").concat(_typeof(item) === 'object' ? JSON.stringify(item).slice(0, 20) : item);
    return /*#__PURE__*/React.createElement("div", {
      key: itemKey,
      style: {
        height: "".concat(itemHeight, "px")
      }
    }, renderItem(item, startIndex + index));
  }))));
};
/* harmony default export */ const performance_VirtualList = ((/* unused pure expression or super */ null && (VirtualList)));
// EXTERNAL MODULE: ./src/utils/optimizedAntdImports.js
var optimizedAntdImports = __webpack_require__(16918);
// EXTERNAL MODULE: ./src/styles/components.js
var components = __webpack_require__(57749);
;// ./src/styles/optimizedComponents.js

var optimizedComponents_templateObject, optimizedComponents_templateObject2, optimizedComponents_templateObject3, optimizedComponents_templateObject4, optimizedComponents_templateObject5, optimizedComponents_templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14, _templateObject15, _templateObject16, _templateObject17, _templateObject18, _templateObject19, _templateObject20, _templateObject21, _templateObject22, _templateObject23;



// Optimized Ant Design imports for better tree-shaking



/**
 * Optimized styled components for the application
 * These components are memoized and use shouldForwardProp to prevent
 * unnecessary re-renders and prop forwarding
 */

var Title = optimizedAntdImports/* Typography */.o5.Title,
  Text = optimizedAntdImports/* Typography */.o5.Text,
  Paragraph = optimizedAntdImports/* Typography */.o5.Paragraph;

// Helper to filter out custom props
var customProps = ['flex', 'flexMd', 'flexLg', 'status', 'messageType'];
var shouldForwardCustomProp = function shouldForwardCustomProp(prop) {
  return !customProps.includes(prop);
};

// Optimized Typography components
var PageTitle = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* PageTitle */.sT).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(optimizedComponents_templateObject || (optimizedComponents_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var SectionTitle = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* SectionTitle */._x).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(optimizedComponents_templateObject2 || (optimizedComponents_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var SubTitle = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* SubTitle */.tK).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(optimizedComponents_templateObject3 || (optimizedComponents_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Layout components
var Container = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Container */.mc).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(optimizedComponents_templateObject4 || (optimizedComponents_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Section = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Section */.wn).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(optimizedComponents_templateObject5 || (optimizedComponents_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Row = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Row */.fI).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(optimizedComponents_templateObject6 || (optimizedComponents_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Column = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Column */.VP).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Grid = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Grid */.xA).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Card components
var StyledCard = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledCard */.ee).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var FeatureCard = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* FeatureCard */.dd).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var DashboardCard = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* DashboardCard */.pK).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject1 || (_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Button components
var PrimaryButton = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* PrimaryButton */.jn).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject10 || (_templateObject10 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var SecondaryButton = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* SecondaryButton */.tA).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject11 || (_templateObject11 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var IconButton = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* IconButton */.K0).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject12 || (_templateObject12 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Form components
var FormGroup = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* FormGroup */.gE).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject13 || (_templateObject13 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StyledInput = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledInput */.sQ).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject14 || (_templateObject14 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StyledTextArea = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledTextArea */.aQ).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject15 || (_templateObject15 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Alert components
var StyledAlert = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledAlert */.cN).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject16 || (_templateObject16 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Loading components
var optimizedComponents_LoadingContainer = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* LoadingContainer */.YM).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject17 || (_templateObject17 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StyledSpin = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledSpin */.IO).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject18 || (_templateObject18 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Tag components
var StyledTag = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledTag */.ih).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject19 || (_templateObject19 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StatusTag = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StatusTag */.hC).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject20 || (_templateObject20 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Divider
var Divider = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Divider */.cG).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject21 || (_templateObject21 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Badge
var Badge = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Badge */.Ex).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject22 || (_templateObject22 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Avatar
var Avatar = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Avatar */.eu).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject23 || (_templateObject23 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Export all components
/* harmony default export */ const optimizedComponents = ({
  PageTitle: PageTitle,
  SectionTitle: SectionTitle,
  SubTitle: SubTitle,
  Container: Container,
  Section: Section,
  Row: Row,
  Column: Column,
  Grid: Grid,
  StyledCard: StyledCard,
  FeatureCard: FeatureCard,
  DashboardCard: DashboardCard,
  PrimaryButton: PrimaryButton,
  SecondaryButton: SecondaryButton,
  IconButton: IconButton,
  FormGroup: FormGroup,
  StyledInput: StyledInput,
  StyledTextArea: StyledTextArea,
  StyledAlert: StyledAlert,
  LoadingContainer: optimizedComponents_LoadingContainer,
  StyledSpin: StyledSpin,
  StyledTag: StyledTag,
  StatusTag: StatusTag,
  Divider: Divider,
  Badge: Badge,
  Avatar: Avatar
});
;// ./src/components/performance/PerformanceMonitor.js




var PerformanceMonitor_templateObject, PerformanceMonitor_templateObject2, PerformanceMonitor_templateObject3, PerformanceMonitor_templateObject4, PerformanceMonitor_templateObject5, PerformanceMonitor_templateObject6, PerformanceMonitor_templateObject7;
function PerformanceMonitor_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function PerformanceMonitor_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? PerformanceMonitor_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : PerformanceMonitor_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





var PerformanceMonitor_Title = es/* Typography */.o5.Title,
  PerformanceMonitor_Text = es/* Typography */.o5.Text,
  PerformanceMonitor_Paragraph = es/* Typography */.o5.Paragraph;

// Styled components
var MonitorCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(PerformanceMonitor_templateObject || (PerformanceMonitor_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: fixed;\n  bottom: ", ";\n  top: ", ";\n  right: 20px;\n  width: ", ";\n  z-index: 1000;\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\n  transition: all 0.3s ease;\n"])), function (props) {
  return props.expanded ? '20px' : 'auto';
}, function (props) {
  return props.expanded ? 'auto' : '20px';
}, function (props) {
  return props.expanded ? '600px' : '300px';
});
var MonitorHeader = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject2 || (PerformanceMonitor_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"])));
var ButtonGroup = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject3 || (PerformanceMonitor_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n"])));
var LongTasksList = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject4 || (PerformanceMonitor_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  max-height: 150px;\n  overflow-y: auto;\n"])));
var LongTaskItem = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject5 || (PerformanceMonitor_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 8px;\n"])));
var TaskTime = (0,styled_components_browser_esm/* default */.Ay)(PerformanceMonitor_Text)(PerformanceMonitor_templateObject6 || (PerformanceMonitor_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n"])));
var FooterText = (0,styled_components_browser_esm/* default */.Ay)(PerformanceMonitor_Paragraph)(PerformanceMonitor_templateObject7 || (PerformanceMonitor_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n  text-align: center;\n"])));

/**
 * PerformanceMonitor component
 * Displays performance metrics for the application
 */
var PerformanceMonitor = function PerformanceMonitor(_ref) {
  var _ref$visible = _ref.visible,
    visible = _ref$visible === void 0 ? false : _ref$visible;
  var _useState = (0,react.useState)({
      fps: 0,
      memory: {
        used: 0,
        total: 0,
        limit: 0
      },
      timing: {
        domComplete: 0,
        domInteractive: 0,
        loadEvent: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0
      },
      resources: {
        count: 0,
        totalSize: 0
      },
      longTasks: []
    }),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    metrics = _useState2[0],
    setMetrics = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    expanded = _useState4[0],
    setExpanded = _useState4[1];
  var _useState5 = (0,react.useState)(visible),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    isVisible = _useState6[0],
    setIsVisible = _useState6[1];

  // Collect performance metrics
  (0,react.useEffect)(function () {
    if (!isVisible) return;

    // Function to collect metrics
    var collectMetrics = function collectMetrics() {
      // FPS calculation
      var frameCount = 0;
      var lastTime = performance.now();
      var _calculateFPS = function calculateFPS() {
        var now = performance.now();
        var delta = now - lastTime;
        if (delta >= 1000) {
          var fps = Math.round(frameCount * 1000 / delta);
          setMetrics(function (prev) {
            return PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev), {}, {
              fps: fps
            });
          });
          frameCount = 0;
          lastTime = now;
        }
        frameCount++;
        requestAnimationFrame(_calculateFPS);
      };

      // Start FPS calculation
      var fpsId = requestAnimationFrame(_calculateFPS);

      // Collect memory usage if available
      if (window.performance && window.performance.memory) {
        setMetrics(function (prev) {
          return PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev), {}, {
            memory: {
              used: Math.round(window.performance.memory.usedJSHeapSize / (1024 * 1024)),
              total: Math.round(window.performance.memory.totalJSHeapSize / (1024 * 1024)),
              limit: Math.round(window.performance.memory.jsHeapSizeLimit / (1024 * 1024))
            }
          });
        });
      }

      // Collect navigation timing metrics
      if (performance.timing) {
        var timing = performance.timing;
        setMetrics(function (prev) {
          return PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev), {}, {
            timing: {
              domComplete: timing.domComplete - timing.navigationStart,
              domInteractive: timing.domInteractive - timing.navigationStart,
              loadEvent: timing.loadEventEnd - timing.navigationStart,
              firstContentfulPaint: 0,
              // Will be set later
              largestContentfulPaint: 0 // Will be set later
            }
          });
        });
      }

      // Collect resource metrics
      var resources = performance.getEntriesByType('resource');
      setMetrics(function (prev) {
        return PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev), {}, {
          resources: {
            count: resources.length,
            totalSize: resources.reduce(function (total, resource) {
              return total + (resource.transferSize || 0);
            }, 0) / (1024 * 1024)
          }
        });
      });

      // Collect paint metrics
      var paintMetrics = performance.getEntriesByType('paint');
      paintMetrics.forEach(function (metric) {
        if (metric.name === 'first-contentful-paint') {
          setMetrics(function (prev) {
            return PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev), {}, {
              timing: PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev.timing), {}, {
                firstContentfulPaint: metric.startTime
              })
            });
          });
        }
      });

      // Observe Largest Contentful Paint
      var observeLCP = new PerformanceObserver(function (entryList) {
        var entries = entryList.getEntries();
        var lastEntry = entries[entries.length - 1];
        setMetrics(function (prev) {
          return PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev), {}, {
            timing: PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev.timing), {}, {
              largestContentfulPaint: lastEntry.startTime
            })
          });
        });
      });
      observeLCP.observe({
        type: 'largest-contentful-paint',
        buffered: true
      });

      // Observe Long Tasks
      var observeLongTasks = new PerformanceObserver(function (entryList) {
        var entries = entryList.getEntries();
        setMetrics(function (prev) {
          return PerformanceMonitor_objectSpread(PerformanceMonitor_objectSpread({}, prev), {}, {
            longTasks: [].concat((0,toConsumableArray/* default */.A)(prev.longTasks), (0,toConsumableArray/* default */.A)(entries)).slice(-10) // Keep last 10 long tasks
          });
        });
      });
      observeLongTasks.observe({
        type: 'longtask',
        buffered: true
      });

      // Cleanup function
      return function () {
        cancelAnimationFrame(fpsId);
        observeLCP.disconnect();
        observeLongTasks.disconnect();
      };
    };

    // Start collecting metrics
    var cleanup = collectMetrics();

    // Cleanup function
    return cleanup;
  }, [isVisible]);

  // Toggle visibility
  var toggleVisibility = function toggleVisibility() {
    setIsVisible(!isVisible);
  };

  // Toggle expanded view
  var toggleExpanded = function toggleExpanded() {
    setExpanded(!expanded);
  };

  // Reset metrics
  var resetMetrics = function resetMetrics() {
    setMetrics({
      fps: 0,
      memory: {
        used: 0,
        total: 0,
        limit: 0
      },
      timing: {
        domComplete: 0,
        domInteractive: 0,
        loadEvent: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0
      },
      resources: {
        count: 0,
        totalSize: 0
      },
      longTasks: []
    });

    // Clear performance entries
    performance.clearMarks();
    performance.clearMeasures();
    performance.clearResourceTimings();
  };

  // If not visible, render only the toggle button
  if (!isVisible) {
    return /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Show Performance Monitor"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "primary",
      shape: "circle",
      icon: /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null),
      onClick: toggleVisibility,
      style: {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 1000
      }
    }));
  }

  // Get FPS color based on value
  var getFPSColor = function getFPSColor(fps) {
    if (fps >= 55) return '#52c41a'; // Green
    if (fps >= 30) return '#faad14'; // Yellow
    return '#f5222d'; // Red
  };

  // Get memory usage percentage
  var getMemoryPercentage = function getMemoryPercentage() {
    if (!metrics.memory.total) return 0;
    return Math.round(metrics.memory.used / metrics.memory.total * 100);
  };

  // Get memory usage color
  var getMemoryColor = function getMemoryColor() {
    var percentage = getMemoryPercentage();
    if (percentage < 70) return '#52c41a'; // Green
    if (percentage < 90) return '#faad14'; // Yellow
    return '#f5222d'; // Red
  };
  return /*#__PURE__*/react.createElement("div", {
    className: "performance-monitor"
  }, /*#__PURE__*/react.createElement(MonitorCard, {
    expanded: expanded,
    title: /*#__PURE__*/react.createElement(MonitorHeader, null, /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null), " Performance Monitor"), /*#__PURE__*/react.createElement(ButtonGroup, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
      onClick: resetMetrics,
      style: {
        marginRight: '8px'
      },
      "aria-label": "Reset metrics"
    }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      icon: expanded ? /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null) : /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null),
      onClick: toggleExpanded,
      style: {
        marginRight: '8px'
      },
      "aria-label": expanded ? "Collapse view" : "Expand view"
    }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      icon: /*#__PURE__*/react.createElement(icons_es/* CloseOutlined */.r$3, null),
      onClick: toggleVisibility,
      "aria-label": "Close performance monitor"
    }))),
    bodyStyle: {
      padding: expanded ? '16px' : '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: expanded ? 12 : 24
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "FPS",
    value: metrics.fps,
    suffix: "fps",
    valueStyle: {
      color: getFPSColor(metrics.fps)
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* ClockCircleOutlined */.L8Y, null)
  }), expanded && /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: metrics.fps / 60 * 100,
    strokeColor: getFPSColor(metrics.fps),
    showInfo: false,
    size: "small",
    style: {
      marginTop: '8px'
    }
  })), metrics.memory.used > 0 && /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: expanded ? 12 : 24
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Memory Usage",
    value: metrics.memory.used,
    suffix: "MB",
    valueStyle: {
      color: getMemoryColor()
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null)
  }), expanded && /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "".concat(metrics.memory.used, "MB / ").concat(metrics.memory.total, "MB")
  }, /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: getMemoryPercentage(),
    strokeColor: getMemoryColor(),
    showInfo: false,
    size: "small",
    style: {
      marginTop: '8px'
    }
  }))), expanded && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    orientation: "left"
  }, "Page Load Timing")), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "DOM Interactive",
    value: metrics.timing.domInteractive,
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "DOM Complete",
    value: metrics.timing.domComplete,
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "First Contentful Paint",
    value: Math.round(metrics.timing.firstContentfulPaint),
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Largest Contentful Paint",
    value: Math.round(metrics.timing.largestContentfulPaint),
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    orientation: "left"
  }, "Resources")), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Resource Count",
    value: metrics.resources.count,
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Total Size",
    value: metrics.resources.totalSize.toFixed(2),
    suffix: "MB",
    valueStyle: {
      fontSize: '16px'
    }
  })), metrics.longTasks.length > 0 && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    orientation: "left"
  }, "Long Tasks"), /*#__PURE__*/react.createElement(LongTasksList, null, metrics.longTasks.map(function (task, index) {
    return /*#__PURE__*/react.createElement(LongTaskItem, {
      key: index
    }, /*#__PURE__*/react.createElement(PerformanceMonitor_Text, {
      type: "danger"
    }, /*#__PURE__*/react.createElement(icons_es/* WarningOutlined */.v7y, null), " Task blocked for ", task.duration.toFixed(2), "ms"), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(TaskTime, {
      type: "secondary"
    }, new Date(task.startTime).toLocaleTimeString())));
  })))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(FooterText, {
    type: "secondary"
  }, /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null), " Performance monitoring active"))))));
};
/* harmony default export */ const performance_PerformanceMonitor = (PerformanceMonitor);
// EXTERNAL MODULE: ./src/hooks/usePerformanceOptimization.js
var usePerformanceOptimization = __webpack_require__(81415);
;// ./src/components/performance/index.js






// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js
var possibleConstructorReturn = __webpack_require__(56822);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js
var getPrototypeOf = __webpack_require__(53954);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/inherits.js
var inherits = __webpack_require__(85501);
;// ./src/components/enhanced/SafeComponentWrapper.js





function _callSuper(t, o, e) { return o = (0,getPrototypeOf/* default */.A)(o), (0,possibleConstructorReturn/* default */.A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,getPrototypeOf/* default */.A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }



/**
 * Safe wrapper component that catches errors and provides fallback UI
 */
var SafeComponentWrapper = /*#__PURE__*/function (_React$Component) {
  function SafeComponentWrapper(props) {
    var _this;
    (0,classCallCheck/* default */.A)(this, SafeComponentWrapper);
    _this = _callSuper(this, SafeComponentWrapper, [props]);
    _this.state = {
      hasError: false,
      error: null
    };
    return _this;
  }
  (0,inherits/* default */.A)(SafeComponentWrapper, _React$Component);
  return (0,createClass/* default */.A)(SafeComponentWrapper, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      console.error('Component error caught by SafeComponentWrapper:', error, errorInfo);
    }
  }, {
    key: "render",
    value: function render() {
      var _this2 = this;
      if (this.state.hasError) {
        var _this$state$error;
        return /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px'
          }
        }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
          message: "Component Loading Error",
          description: /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("p", null, "This component failed to load properly. This might be due to missing dependencies or configuration issues."), /*#__PURE__*/react.createElement("p", null, /*#__PURE__*/react.createElement("strong", null, "Error:"), " ", ((_this$state$error = this.state.error) === null || _this$state$error === void 0 ? void 0 : _this$state$error.message) || 'Unknown error'), this.props.fallback && /*#__PURE__*/react.createElement("div", {
            style: {
              marginTop: '16px'
            }
          }, /*#__PURE__*/react.createElement("p", null, "Using fallback component:"), this.props.fallback)),
          type: "warning",
          showIcon: true,
          action: /*#__PURE__*/react.createElement(es/* Button */.$n, {
            size: "small",
            onClick: function onClick() {
              return _this2.setState({
                hasError: false,
                error: null
              });
            }
          }, "Retry")
        }));
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      return {
        hasError: true,
        error: error
      };
    }
  }]);
}(react.Component);
/* harmony default export */ const enhanced_SafeComponentWrapper = (SafeComponentWrapper);
;// ./src/pages/AppBuilderEnhanced.js




var AppBuilderEnhanced_templateObject, AppBuilderEnhanced_templateObject2, AppBuilderEnhanced_templateObject3, AppBuilderEnhanced_templateObject4, AppBuilderEnhanced_templateObject5, AppBuilderEnhanced_templateObject6, AppBuilderEnhanced_templateObject7;

function AppBuilderEnhanced_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function AppBuilderEnhanced_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? AppBuilderEnhanced_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : AppBuilderEnhanced_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









// Import new preview functionality





// Import safe wrapper


// Import the actual components using lazy loading for better performance with error handling
var ComponentBuilder = /*#__PURE__*/(0,react.lazy)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(4043), __webpack_require__.e(4309), __webpack_require__.e(5897), __webpack_require__.e(7594)]).then(__webpack_require__.bind(__webpack_require__, 4309))["catch"](function (err) {
    console.warn('Failed to load ComponentBuilder:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Component Builder not available");
      }
    };
  });
});
var LayoutDesigner = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 5505).then(__webpack_require__.bind(__webpack_require__, 95505))["catch"](function (err) {
    console.warn('Failed to load LayoutDesigner:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Layout Designer not available");
      }
    };
  });
});
var ThemeManager = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 1667).then(__webpack_require__.bind(__webpack_require__, 71667))["catch"](function (err) {
    console.warn('Failed to load ThemeManager:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Theme Manager not available");
      }
    };
  });
});
var FixedWebSocketManager = /*#__PURE__*/(0,react.lazy)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(1470), __webpack_require__.e(5366)]).then(__webpack_require__.bind(__webpack_require__, 65366))["catch"](function (err) {
    console.warn('Failed to load FixedWebSocketManager:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "WebSocket Manager not available");
      }
    };
  });
});
var ProjectManager = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 1680).then(__webpack_require__.bind(__webpack_require__, 51680))["catch"](function (err) {
    console.warn('Failed to load ProjectManager:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Project Manager not available");
      }
    };
  });
});
var CodeExporter = /*#__PURE__*/(/* unused pure expression or super */ null && (lazy(function () {
  return __webpack_require__.e(/* import() */ 5008).then(__webpack_require__.bind(__webpack_require__, 75008))["catch"](function (err) {
    console.warn('Failed to load CodeExporter:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/React.createElement("div", null, "Code Exporter not available");
      }
    };
  });
})));
var EnhancedCodeExporter = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 9205).then(__webpack_require__.bind(__webpack_require__, 19205))["catch"](function (err) {
    console.warn('Failed to load EnhancedCodeExporter:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Enhanced Code Exporter not available");
      }
    };
  });
});
var AppBuilderEnhanced_PerformanceMonitor = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 672).then(__webpack_require__.bind(__webpack_require__, 40672))["catch"](function (err) {
    console.warn('Failed to load PerformanceMonitor:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Performance Monitor not available");
      }
    };
  });
});
var DataManagementDemo = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 7481).then(__webpack_require__.bind(__webpack_require__, 97481))["catch"](function (err) {
    console.warn('Failed to load DataManagementDemo:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Data Management not available");
      }
    };
  });
});
var TestingTools = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 4605).then(__webpack_require__.bind(__webpack_require__, 54605))["catch"](function (err) {
    console.warn('Failed to load TestingTools:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Testing Tools not available");
      }
    };
  });
});
var TutorialAIPlugin = /*#__PURE__*/(0,react.lazy)(function () {
  return __webpack_require__.e(/* import() */ 3726).then(__webpack_require__.bind(__webpack_require__, 23726))["catch"](function (err) {
    console.warn('Failed to load TutorialAIPlugin:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", null, "Tutorial Assistant not available");
      }
    };
  });
});
var AppBuilderEnhanced_Title = es/* Typography */.o5.Title,
  AppBuilderEnhanced_Paragraph = es/* Typography */.o5.Paragraph;
var AppBuilderContainer = styled_components_browser_esm/* default */.Ay.div(AppBuilderEnhanced_templateObject || (AppBuilderEnhanced_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: var(--spacing-lg);\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: var(--spacing-md);\n  }\n\n  @media (max-width: 480px) {\n    padding: var(--spacing-sm);\n  }\n"])));
var AppHeader = styled_components_browser_esm/* default */.Ay.div(AppBuilderEnhanced_templateObject2 || (AppBuilderEnhanced_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: var(--spacing-xl);\n  padding: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  border: 1px solid var(--color-border-light);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-md);\n  }\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-md);\n\n    h2 {\n      margin-bottom: var(--spacing-sm);\n    }\n  }\n"])));
var StyledTabs = (0,styled_components_browser_esm/* default */.Ay)(es/* Tabs */.tU)(AppBuilderEnhanced_templateObject3 || (AppBuilderEnhanced_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  .ant-tabs-nav {\n    margin-bottom: var(--spacing-xl);\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    padding: var(--spacing-sm);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n  }\n\n  .ant-tabs-tab {\n    padding: var(--spacing-md) var(--spacing-lg);\n    transition: all 0.3s ease;\n    border-radius: var(--border-radius-md);\n    margin: 0 var(--spacing-xs);\n    color: var(--color-text-secondary);\n    font-weight: 500;\n\n    &:hover {\n      color: var(--color-primary);\n      background-color: var(--color-background-secondary);\n      transform: translateY(-2px);\n    }\n\n    .anticon {\n      margin-right: var(--spacing-sm);\n      font-size: 16px;\n    }\n  }\n\n  .ant-tabs-tab-active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-md);\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      color: white;\n    }\n  }\n\n  .ant-tabs-content-holder {\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n    overflow: hidden;\n  }\n\n  .ant-tabs-tabpane {\n    padding: var(--spacing-lg);\n  }\n"])));
var ComponentCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(AppBuilderEnhanced_templateObject4 || (AppBuilderEnhanced_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  height: 100%;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: var(--shadow-lg);\n    border-color: var(--color-primary);\n  }\n\n  &:active {\n    transform: translateY(-4px);\n  }\n\n  .ant-card-head {\n    background-color: ", ";\n    color: ", ";\n    border-bottom: 1px solid var(--color-border-light);\n    transition: all 0.3s ease;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-lg);\n    text-align: center;\n    background-color: var(--color-surface);\n  }\n"])), function (props) {
  return props.active ? 'var(--color-primary)' : 'var(--color-background-secondary)';
}, function (props) {
  return props.active ? 'white' : 'var(--color-text)';
});
var ComponentIcon = styled_components_browser_esm/* default */.Ay.div(AppBuilderEnhanced_templateObject5 || (AppBuilderEnhanced_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 32px;\n  margin-bottom: var(--spacing-md);\n  color: ", ";\n  transition: all 0.3s ease;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  background-color: ", ";\n  margin: 0 auto var(--spacing-md);\n"])), function (props) {
  return props.active ? 'var(--color-primary)' : 'var(--color-text-secondary)';
}, function (props) {
  return props.active ? 'rgba(24, 144, 255, 0.1)' : 'var(--color-background-secondary)';
});
var WelcomeCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(AppBuilderEnhanced_templateObject6 || (AppBuilderEnhanced_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: var(--spacing-xl);\n  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);\n  border: none;\n  border-radius: var(--border-radius-xl);\n  overflow: hidden;\n  box-shadow: var(--shadow-lg);\n  position: relative;\n\n  /* Add overlay for better text contrast */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(0, 0, 0, 0.1);\n    z-index: 1;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-xxl);\n    color: white;\n    position: relative;\n    z-index: 2;\n  }\n\n  h4, p {\n    color: white !important;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n    position: relative;\n    z-index: 2;\n  }\n\n  .ant-btn-primary {\n    background-color: white;\n    border-color: white;\n    color: var(--color-primary);\n    font-weight: 600;\n    position: relative;\n    z-index: 2;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    &:hover {\n      background-color: rgba(255, 255, 255, 0.95);\n      border-color: rgba(255, 255, 255, 0.95);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);\n    }\n  }\n"])));

// Define the spin animation
var SpinAnimation = styled_components_browser_esm/* default */.Ay.div(AppBuilderEnhanced_templateObject7 || (AppBuilderEnhanced_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n"])));

/**
 * Enhanced App Builder with improved accessibility, performance, and code organization
 */
var AppBuilderEnhanced = function AppBuilderEnhanced() {
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var _useEnhancedTheme = (0,EnhancedThemeContext/* useEnhancedTheme */.ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    colors = _useEnhancedTheme.colors;
  var _useState = (0,react.useState)(true),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];
  var _useState5 = (0,react.useState)({
      api: 'checking',
      websocket: 'checking'
    }),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    connectionStatus = _useState6[0],
    setConnectionStatus = _useState6[1];
  // Initialize with the current view from Redux or default to 'components'
  var currentViewFromStore = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$ui;
    return ((_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.currentView) || 'components';
  });
  var _useState7 = (0,react.useState)(currentViewFromStore),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    activeComponent = _useState8[0],
    setActiveComponent = _useState8[1];
  var _useState9 = (0,react.useState)(false),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    showTutorialModal = _useState0[0],
    setShowTutorialModal = _useState0[1];

  // Enhanced preview state
  var _useState1 = (0,react.useState)({
      realTimeEnabled: true,
      collaborationEnabled: false,
      performanceMonitoring: true,
      deviceSync: true
    }),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    previewSettings = _useState10[0],
    setPreviewSettings = _useState10[1];
  var _useState11 = (0,react.useState)(function () {
      return "session_".concat(Date.now(), "_").concat(Math.random().toString(36).substring(2, 11));
    }),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 1),
    sessionId = _useState12[0];
  var _useState13 = (0,react.useState)(function () {
      return "user_".concat(Math.random().toString(36).substring(2, 11));
    }),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 1),
    userId = _useState14[0];

  // WebSocket and collaboration state
  var websocketConnected = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$websocket;
    return ((_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });
  var components = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$app;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || [];
  });

  // Initialize enhanced preview hooks
  var responsivePreview = (0,useResponsivePreview/* default */.A)({
    initialDevice: 'desktop',
    enableBreakpointDetection: true
  });
  var collaborativePreview = (0,useCollaborativePreview/* default */.A)({
    sessionId: sessionId,
    userId: userId,
    username: 'App Builder User',
    enableCollaboration: previewSettings.collaborationEnabled && websocketConnected,
    enableCursorTracking: true,
    enableDeviceSync: previewSettings.deviceSync
  });
  var realTimePreview = (0,useRealTimePreview/* default */.A)({
    components: components,
    websocketService: collaborativePreview.wsService,
    enableWebSocket: previewSettings.realTimeEnabled && websocketConnected,
    updateDelay: 300
  });

  // Handle preview settings changes
  var handlePreviewSettingChange = (0,react.useCallback)(function (setting, value) {
    setPreviewSettings(function (prev) {
      return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, (0,defineProperty/* default */.A)({}, setting, value));
    });
  }, []);
  (0,react.useEffect)(function () {
    // Initialize the app
    var initApp = /*#__PURE__*/function () {
      var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
        var isDev, apiResponse, ws, timeoutId;
        return regenerator_default().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              // Set initial view in Redux
              dispatch((0,uiReducer/* setCurrentView */.tI)(activeComponent));
              console.log('App Builder initialized successfully');

              // Check if we're in development mode
              isDev = "production" === 'development'; // In development mode, we can continue even if connections fail
              if (isDev) {
                console.log('Development mode: App will continue to load even if connections fail');
              }

              // Quick API connection check
              _context.prev = 5;
              console.log('Checking API connection...');
              _context.next = 9;
              return Promise.race([fetch('/api/status/'), new Promise(function (_, reject) {
                return setTimeout(function () {
                  return reject(new Error('API timeout'));
                }, 1000);
              })]);
            case 9:
              apiResponse = _context.sent;
              if (apiResponse.ok) {
                setConnectionStatus(function (prev) {
                  return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                    api: 'connected'
                  });
                });
                console.log('API connection successful');
              } else {
                setConnectionStatus(function (prev) {
                  return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                    api: isDev ? 'warning' : 'error'
                  });
                });
                console.warn('API connection failed');
              }
              _context.next = 18;
              break;
            case 13:
              _context.prev = 13;
              _context.t0 = _context["catch"](5);
              setConnectionStatus(function (prev) {
                return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                  api: isDev ? 'warning' : 'error'
                });
              });
              console.warn('API connection error:', _context.t0.message);
              if (isDev) {
                console.log('Development mode: Continuing with mock API');
              }
            case 18:
              // Quick WebSocket connection check
              try {
                console.log('Checking WebSocket connection...');
                // Skip WebSocket check in development mode to speed up loading
                if (isDev) {
                  setConnectionStatus(function (prev) {
                    return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                      websocket: 'warning'
                    });
                  });
                  console.log('Development mode: Skipping WebSocket check');
                } else {
                  // Quick WebSocket test
                  ws = new WebSocket('ws://localhost:8000/ws/app_builder/');
                  timeoutId = setTimeout(function () {
                    ws.close();
                    setConnectionStatus(function (prev) {
                      return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                        websocket: 'error'
                      });
                    });
                  }, 1000);
                  ws.onopen = function () {
                    clearTimeout(timeoutId);
                    setConnectionStatus(function (prev) {
                      return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                        websocket: 'connected'
                      });
                    });
                    console.log('WebSocket connection successful');
                    ws.close();
                  };
                  ws.onerror = function () {
                    clearTimeout(timeoutId);
                    setConnectionStatus(function (prev) {
                      return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                        websocket: 'error'
                      });
                    });
                    console.warn('WebSocket connection failed');
                    ws.close();
                  };
                }
              } catch (wsError) {
                setConnectionStatus(function (prev) {
                  return AppBuilderEnhanced_objectSpread(AppBuilderEnhanced_objectSpread({}, prev), {}, {
                    websocket: isDev ? 'warning' : 'error'
                  });
                });
                console.warn('WebSocket connection error:', wsError.message);
              }
              _context.next = 26;
              break;
            case 21:
              _context.prev = 21;
              _context.t1 = _context["catch"](0);
              console.error('Failed to initialize App Builder:', _context.t1);
              es/* message */.iU.error('Failed to initialize App Builder. Please try refreshing the page.');
              setError('Failed to initialize App Builder. Please try refreshing the page.');
            case 26:
              _context.prev = 26;
              // Always set loading to false after initialization
              setTimeout(function () {
                setLoading(false);
              }, 500);
              return _context.finish(26);
            case 29:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 21, 26, 29], [5, 13]]);
      }));
      return function initApp() {
        return _ref.apply(this, arguments);
      };
    }();

    // Set a timeout to ensure loading state is not stuck
    var timer = setTimeout(function () {
      setLoading(false);
      console.log('Loading timeout triggered - forcing app to load');
    }, 3000);
    initApp();

    // Clean up the timer
    return function () {
      return clearTimeout(timer);
    };
  }, [dispatch, activeComponent]);

  // Handle component selection - memoized for better performance
  var handleComponentSelect = (0,react.useCallback)(function (component) {
    setActiveComponent(component);
    dispatch((0,uiReducer/* setCurrentView */.tI)(component));
  }, [dispatch]);

  // Define tab items - memoized for better performance
  // This must be defined before any conditional returns to follow Rules of Hooks
  var tabItems = (0,react.useMemo)(function () {
    return [{
      key: 'projects',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* ProjectOutlined */.KGW, null), " Projects"),
      children: /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Projects..."))
      }, /*#__PURE__*/react.createElement(ProjectManager, null))
    }, {
      key: 'components',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null), " Component Builder"),
      children: /*#__PURE__*/react.createElement(enhanced_SafeComponentWrapper, {
        fallback: /*#__PURE__*/react.createElement("div", null, "Using basic component builder...")
      }, /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Component Builder..."))
      }, /*#__PURE__*/react.createElement(ComponentBuilder, null)))
    }, {
      key: 'layouts',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* LayoutOutlined */.hy2, null), " Layout Designer"),
      children: /*#__PURE__*/react.createElement(enhanced_SafeComponentWrapper, {
        fallback: /*#__PURE__*/react.createElement("div", null, "Using basic layout designer...")
      }, /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Layout Designer..."))
      }, /*#__PURE__*/react.createElement(LayoutDesigner, null)))
    }, {
      key: 'themes',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* BgColorsOutlined */.Ebl, null), " Theme Manager"),
      children: /*#__PURE__*/react.createElement(enhanced_SafeComponentWrapper, {
        fallback: /*#__PURE__*/react.createElement("div", null, "Using basic theme manager...")
      }, /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Theme Manager..."))
      }, /*#__PURE__*/react.createElement(ThemeManager, null)))
    }, {
      key: 'export',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* CodeOutlined */.C$o, null), " Enhanced Export"),
      children: /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Enhanced Code Exporter..."))
      }, /*#__PURE__*/react.createElement(EnhancedCodeExporter, null))
    }, {
      key: 'performance',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null), " Performance"),
      children: /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Performance Monitor..."))
      }, /*#__PURE__*/react.createElement(AppBuilderEnhanced_PerformanceMonitor, null))
    }, {
      key: 'websocket',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* ApiOutlined */.bfv, null), " WebSocket Manager"),
      children: /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading WebSocket Manager..."))
      }, /*#__PURE__*/react.createElement(FixedWebSocketManager, null))
    }, {
      key: 'data',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null), " Data Management"),
      children: /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Data Management..."))
      }, /*#__PURE__*/react.createElement(DataManagementDemo, null))
    }, {
      key: 'testing',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null), " Testing Tools"),
      children: /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Testing Tools..."))
      }, /*#__PURE__*/react.createElement(TestingTools, null))
    }, {
      key: 'tutorial',
      label: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* QuestionCircleOutlined */.faO, null), " Tutorial Assistant"),
      children: /*#__PURE__*/react.createElement(react.Suspense, {
        fallback: /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
          size: "large"
        }), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Tutorial Assistant..."))
      }, /*#__PURE__*/react.createElement("div", {
        style: {
          padding: '20px'
        }
      }, /*#__PURE__*/react.createElement(AppBuilderEnhanced_Title, {
        level: 3
      }, "Tutorial Assistant"), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "Get help and learn how to use App Builder with our AI-powered tutorial assistant."), /*#__PURE__*/react.createElement(TutorialAIPlugin, null)))
    }];
  }, []);
  if (loading) {
    return /*#__PURE__*/react.createElement(SpinAnimation, null, /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '70vh',
        flexDirection: 'column'
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        width: '50px',
        height: '50px',
        border: '5px solid #f3f3f3',
        borderTop: '5px solid #3498db',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }
    }), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Title, {
      level: 3,
      style: {
        marginTop: '20px'
      }
    }, "Loading App Builder..."), /*#__PURE__*/react.createElement("div", {
      style: {
        marginTop: '20px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        color: 'var(--color-text)',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '8px 16px',
        borderRadius: '8px',
        marginBottom: '8px',
        border: '1px solid var(--color-border-light)'
      }
    }, "API Connection: ", ' ', /*#__PURE__*/react.createElement("span", {
      style: {
        color: connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'error' ? '#ff4d4f' : connectionStatus.api === 'warning' ? '#faad14' : '#1890ff',
        fontWeight: '600'
      }
    }, connectionStatus.api === 'connected' ? 'Connected' : connectionStatus.api === 'error' ? 'Failed' : connectionStatus.api === 'warning' ? 'Limited (Mock)' : 'Checking...')), /*#__PURE__*/react.createElement("div", {
      style: {
        color: 'var(--color-text)',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '8px 16px',
        borderRadius: '8px',
        border: '1px solid var(--color-border-light)'
      }
    }, "WebSocket Connection: ", ' ', /*#__PURE__*/react.createElement("span", {
      style: {
        color: connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'error' ? '#ff4d4f' : connectionStatus.websocket === 'warning' ? '#faad14' : '#1890ff',
        fontWeight: '600'
      }
    }, connectionStatus.websocket === 'connected' ? 'Connected' : connectionStatus.websocket === 'error' ? 'Failed' : connectionStatus.websocket === 'warning' ? 'Limited (Mock)' : 'Checking...')), (connectionStatus.api === 'error' || connectionStatus.websocket === 'error') && /*#__PURE__*/react.createElement("div", {
      style: {
        marginTop: '20px',
        color: '#ff4d4f',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '12px 16px',
        borderRadius: '8px',
        border: '1px solid #ff4d4f',
        fontWeight: '500'
      }
    }, /*#__PURE__*/react.createElement("p", {
      style: {
        margin: '0 0 8px 0'
      }
    }, "Some connections failed. The app will continue to load with limited functionality."), /*#__PURE__*/react.createElement("p", {
      style: {
        margin: 0
      }
    }, "Please ensure the backend server is running at http://localhost:8000")), (connectionStatus.api === 'warning' || connectionStatus.websocket === 'warning') && /*#__PURE__*/react.createElement("div", {
      style: {
        marginTop: '20px',
        color: '#faad14',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '12px 16px',
        borderRadius: '8px',
        border: '1px solid #faad14',
        fontWeight: '500'
      }
    }, /*#__PURE__*/react.createElement("p", {
      style: {
        margin: '0 0 8px 0'
      }
    }, "Some connections are in limited mode. The app will use mock data."), /*#__PURE__*/react.createElement("p", {
      style: {
        margin: 0
      }
    }, "This is normal in development mode when the backend is not running.")))));
  }

  // Show error state if there's an error
  if (error) {
    return /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '70vh',
        flexDirection: 'column'
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        color: 'red',
        fontSize: '48px',
        marginBottom: '20px'
      }
    }, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Title, {
      level: 3,
      style: {
        color: 'red'
      }
    }, "Error"), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, {
      style: {
        textAlign: 'center',
        maxWidth: '600px',
        marginTop: '20px'
      }
    }, error), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "primary",
      style: {
        marginTop: '20px'
      },
      onClick: function onClick() {
        return window.location.reload();
      }
    }, "Refresh Page"));
  }
  return /*#__PURE__*/react.createElement(AppBuilderContainer, {
    className: "app-builder-enhanced"
  }, /*#__PURE__*/react.createElement(AppHeader, null, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(AppBuilderEnhanced_Title, {
    level: 2
  }, "App Builder Enhanced"), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "Create and manage your application components with ease"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      gap: '10px',
      marginTop: '5px'
    }
  }, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: connectionStatus.api === 'connected' ? 'API Connected' : connectionStatus.api === 'warning' ? 'API in Limited Mode (Mock)' : 'API Connection Failed'
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'inline-flex',
      alignItems: 'center',
      fontSize: '12px',
      color: connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f',
      backgroundColor: 'var(--color-background-secondary)',
      padding: '4px 8px',
      borderRadius: '12px',
      border: '1px solid var(--color-border-light)',
      fontWeight: '500'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      backgroundColor: connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f',
      marginRight: '6px',
      boxShadow: "0 0 4px ".concat(connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f')
    }
  }), "API")), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: connectionStatus.websocket === 'connected' ? 'WebSocket Connected' : connectionStatus.websocket === 'warning' ? 'WebSocket in Limited Mode (Mock)' : 'WebSocket Connection Failed'
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'inline-flex',
      alignItems: 'center',
      fontSize: '12px',
      color: connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f',
      backgroundColor: 'var(--color-background-secondary)',
      padding: '4px 8px',
      borderRadius: '12px',
      border: '1px solid var(--color-border-light)',
      fontWeight: '500'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      backgroundColor: connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f',
      marginRight: '6px',
      boxShadow: "0 0 4px ".concat(connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f')
    }
  }), "WebSocket")))), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '8px 12px',
      background: 'rgba(255, 255, 255, 0.1)',
      borderRadius: '8px',
      border: '1px solid rgba(255, 255, 255, 0.2)'
    }
  }, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Real-time Preview"
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    size: "small",
    checked: previewSettings.realTimeEnabled,
    onChange: function onChange(checked) {
      return handlePreviewSettingChange('realTimeEnabled', checked);
    },
    checkedChildren: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
    unCheckedChildren: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null)
  })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Collaboration"
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    size: "small",
    checked: previewSettings.collaborationEnabled && websocketConnected,
    onChange: function onChange(checked) {
      return handlePreviewSettingChange('collaborationEnabled', checked);
    },
    disabled: !websocketConnected,
    checkedChildren: /*#__PURE__*/react.createElement(icons_es/* SyncOutlined */.OmY, null),
    unCheckedChildren: /*#__PURE__*/react.createElement(icons_es/* SyncOutlined */.OmY, null)
  })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Performance Monitoring"
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    size: "small",
    checked: previewSettings.performanceMonitoring,
    onChange: function onChange(checked) {
      return handlePreviewSettingChange('performanceMonitoring', checked);
    },
    checkedChildren: /*#__PURE__*/react.createElement(icons_es/* ThunderboltOutlined */.CwG, null),
    unCheckedChildren: /*#__PURE__*/react.createElement(icons_es/* ThunderboltOutlined */.CwG, null)
  })), previewSettings.collaborationEnabled && collaborativePreview.isConnected && /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
    count: collaborativePreview.collaborators.length,
    showZero: false,
    style: {
      backgroundColor: '#52c41a'
    }
  }, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Active collaborators"
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      width: '20px',
      height: '20px',
      borderRadius: '50%',
      background: '#52c41a',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  }, /*#__PURE__*/react.createElement(icons_es/* SyncOutlined */.OmY, {
    style: {
      fontSize: '10px',
      color: 'white'
    }
  }))))), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Open Tutorial Assistant"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "default",
    icon: /*#__PURE__*/react.createElement(icons_es/* QuestionCircleOutlined */.faO, null),
    style: {
      marginRight: '10px'
    },
    onClick: function onClick() {
      return setShowTutorialModal(true);
    }
  }, "Help")), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Refresh connections"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "default",
    onClick: function onClick() {
      return window.location.reload();
    }
  }, "Refresh")))), /*#__PURE__*/react.createElement(WelcomeCard, null, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    md: 16
  }, /*#__PURE__*/react.createElement(AppBuilderEnhanced_Title, {
    level: 4
  }, "Welcome to App Builder Enhanced"), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "This tool helps you create and manage your application components with ease. Use the tabs below to navigate between different features."), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    size: "large",
    onClick: function onClick() {
      return handleComponentSelect('components');
    }
  }, "Start Building")), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react.createElement("img", {
    src: "/static/images/app-builder-logo.svg",
    alt: "App Builder Logo",
    style: {
      maxWidth: '100%',
      height: 'auto'
    },
    onError: function onError(e) {
      e.target.onerror = null;
      e.target.style.display = 'none';
    }
  }))))), /*#__PURE__*/react.createElement(StyledTabs, {
    activeKey: activeComponent,
    onChange: handleComponentSelect,
    type: "card",
    size: "large",
    items: tabItems
  }), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Getting Started",
    style: {
      marginTop: '24px'
    }
  }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react.createElement(ComponentCard, {
    title: "Step 1",
    active: activeComponent === 'components',
    onClick: function onClick() {
      return handleComponentSelect('components');
    }
  }, /*#__PURE__*/react.createElement(ComponentIcon, {
    active: activeComponent === 'components'
  }, /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null)), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "Use the Component Builder to create UI components"))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react.createElement(ComponentCard, {
    title: "Step 2",
    active: activeComponent === 'layouts',
    onClick: function onClick() {
      return handleComponentSelect('layouts');
    }
  }, /*#__PURE__*/react.createElement(ComponentIcon, {
    active: activeComponent === 'layouts'
  }, /*#__PURE__*/react.createElement(icons_es/* LayoutOutlined */.hy2, null)), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "Design your layout with the Layout Designer"))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react.createElement(ComponentCard, {
    title: "Step 3",
    active: activeComponent === 'themes',
    onClick: function onClick() {
      return handleComponentSelect('themes');
    }
  }, /*#__PURE__*/react.createElement(ComponentIcon, {
    active: activeComponent === 'themes'
  }, /*#__PURE__*/react.createElement(icons_es/* BgColorsOutlined */.Ebl, null)), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "Customize your theme with the Theme Manager"))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react.createElement(ComponentCard, {
    title: "Step 4",
    active: activeComponent === 'websocket',
    onClick: function onClick() {
      return handleComponentSelect('websocket');
    }
  }, /*#__PURE__*/react.createElement(ComponentIcon, {
    active: activeComponent === 'websocket'
  }, /*#__PURE__*/react.createElement(icons_es/* ApiOutlined */.bfv, null)), /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "Set up real-time communication with WebSocket Manager"))))), previewSettings.performanceMonitoring && /*#__PURE__*/react.createElement(performance_PerformanceMonitor, {
    renderTime: realTimePreview.isUpdating ? 16 : 8,
    frameRate: 60,
    memoryUsage: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 0,
    componentCount: components.length,
    visibleComponents: components.length,
    cacheSize: 0,
    updateFrequency: realTimePreview.hasPendingUpdates ? 30 : 0,
    floating: true,
    showAlerts: true,
    optimizationsEnabled: previewSettings.realTimeEnabled,
    onToggleOptimizations: function onToggleOptimizations(enabled) {
      return handlePreviewSettingChange('realTimeEnabled', enabled);
    }
  }), /*#__PURE__*/react.createElement(es/* FloatButton */.ff, {
    icon: /*#__PURE__*/react.createElement(icons_es/* QuestionCircleOutlined */.faO, null),
    type: "primary",
    style: {
      right: previewSettings.performanceMonitoring ? 340 : 24,
      bottom: 24
    },
    onClick: function onClick() {
      return setShowTutorialModal(true);
    },
    tooltip: "Tutorial Assistant"
  }), /*#__PURE__*/react.createElement(es/* Modal */.aF, {
    title: "Tutorial Assistant",
    open: showTutorialModal,
    onCancel: function onCancel() {
      return setShowTutorialModal(false);
    },
    footer: null,
    width: 800,
    style: {
      top: 20
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      padding: '10px 0'
    }
  }, /*#__PURE__*/react.createElement(AppBuilderEnhanced_Paragraph, null, "Get help and learn how to use App Builder with our AI-powered tutorial assistant."), /*#__PURE__*/react.createElement(react.Suspense, {
    fallback: /*#__PURE__*/react.createElement("div", {
      style: {
        padding: '20px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
      size: "large"
    }), /*#__PURE__*/react.createElement("div", {
      style: {
        marginTop: '10px'
      }
    }, "Loading Tutorial Assistant..."))
  }, /*#__PURE__*/react.createElement(TutorialAIPlugin, null)))));
};
/* harmony default export */ const pages_AppBuilderEnhanced = (AppBuilderEnhanced);

/***/ }),

/***/ 70405:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   c: () => (/* binding */ DEVICE_PRESETS)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;

// Device configurations with detailed specifications
var DEVICE_PRESETS = {
  mobile: {
    name: 'Mobile',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MobileOutlined */ .jHj, null),
    variants: {
      'iphone-se': {
        name: 'iPhone SE',
        width: 375,
        height: 667,
        scale: 0.8
      },
      'iphone-12': {
        name: 'iPhone 12',
        width: 390,
        height: 844,
        scale: 0.7
      },
      'pixel-5': {
        name: 'Pixel 5',
        width: 393,
        height: 851,
        scale: 0.7
      },
      'samsung-s21': {
        name: 'Samsung S21',
        width: 384,
        height: 854,
        scale: 0.7
      }
    },
    defaultVariant: 'iphone-12',
    frame: true,
    category: 'mobile'
  },
  tablet: {
    name: 'Tablet',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .TabletOutlined */ .pLH, null),
    variants: {
      'ipad': {
        name: 'iPad',
        width: 768,
        height: 1024,
        scale: 0.6
      },
      'ipad-pro': {
        name: 'iPad Pro',
        width: 1024,
        height: 1366,
        scale: 0.5
      },
      'surface': {
        name: 'Surface Pro',
        width: 912,
        height: 1368,
        scale: 0.5
      },
      'galaxy-tab': {
        name: 'Galaxy Tab',
        width: 800,
        height: 1280,
        scale: 0.6
      }
    },
    defaultVariant: 'ipad',
    frame: true,
    category: 'tablet'
  },
  desktop: {
    name: 'Desktop',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DesktopOutlined */ .zlw, null),
    variants: {
      'laptop': {
        name: 'Laptop',
        width: 1366,
        height: 768,
        scale: 0.7
      },
      'desktop': {
        name: 'Desktop',
        width: 1920,
        height: 1080,
        scale: 0.5
      },
      'ultrawide': {
        name: 'Ultrawide',
        width: 2560,
        height: 1080,
        scale: 0.4
      },
      'custom': {
        name: 'Custom',
        width: 1200,
        height: 800,
        scale: 0.8
      }
    },
    defaultVariant: 'laptop',
    frame: false,
    category: 'desktop'
  }
};

// Styled components
var DeviceFrameContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  margin: 20px auto;\n  transition: all 0.3s ease;\n  transform: ", ";\n"])), function (props) {
  return props.orientation === 'landscape' ? 'rotate(0deg)' : 'rotate(0deg)';
});
var DeviceFrame = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  background: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  box-shadow: ", ";\n  transition: all 0.3s ease;\n  \n  ", "\n  \n  ", "\n"])), function (props) {
  if (props.category === 'mobile') return '#333';
  if (props.category === 'tablet') return '#444';
  return 'transparent';
}, function (props) {
  if (props.category === 'mobile') return '25px';
  if (props.category === 'tablet') return '15px';
  return '8px';
}, function (props) {
  if (props.category === 'mobile') return '20px 10px';
  if (props.category === 'tablet') return '15px';
  return '0';
}, function (props) {
  return props.frame ? '0 8px 32px rgba(0, 0, 0, 0.3)' : 'none';
}, function (props) {
  return props.category === 'mobile' && "\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n    \n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  ";
}, function (props) {
  return props.category === 'tablet' && "\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  ";
});
var DeviceScreen = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: ", "px;\n  height: ", "px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ", ";\n  overflow: auto;\n  position: relative;\n  transform: scale(", ");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n  \n  @media (max-width: 1200px) {\n    transform: scale(", ");\n  }\n  \n  @media (max-width: 768px) {\n    transform: scale(", ");\n  }\n"])), function (props) {
  return props.orientation === 'landscape' ? props.height : props.width;
}, function (props) {
  return props.orientation === 'landscape' ? props.width : props.height;
}, function (props) {
  if (props.category === 'mobile') return '8px';
  if (props.category === 'tablet') return '6px';
  return '4px';
}, function (props) {
  return props.scale;
}, function (props) {
  return Math.min(props.scale, 0.8);
}, function (props) {
  return Math.min(props.scale, 0.6);
});
var DeviceControls = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n"])));
var DeviceInfo = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -30px;\n  left: 0;\n  font-size: 12px;\n  color: #666;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 4px 8px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n"])));

/**
 * DevicePreviewFrame Component
 * Provides device-specific preview frames with orientation controls
 */
var DevicePreviewFrame = function DevicePreviewFrame(_ref) {
  var children = _ref.children,
    onDeviceChange = _ref.onDeviceChange,
    _ref$initialDevice = _ref.initialDevice,
    initialDevice = _ref$initialDevice === void 0 ? 'desktop' : _ref$initialDevice,
    _ref$initialVariant = _ref.initialVariant,
    initialVariant = _ref$initialVariant === void 0 ? null : _ref$initialVariant,
    _ref$showControls = _ref.showControls,
    showControls = _ref$showControls === void 0 ? true : _ref$showControls,
    _ref$showInfo = _ref.showInfo,
    showInfo = _ref$showInfo === void 0 ? true : _ref$showInfo,
    className = _ref.className;
  var _useState = useState(initialDevice),
    _useState2 = _slicedToArray(_useState, 2),
    currentDevice = _useState2[0],
    setCurrentDevice = _useState2[1];
  var _useState3 = useState(initialVariant || DEVICE_PRESETS[initialDevice].defaultVariant),
    _useState4 = _slicedToArray(_useState3, 2),
    currentVariant = _useState4[0],
    setCurrentVariant = _useState4[1];
  var _useState5 = useState('portrait'),
    _useState6 = _slicedToArray(_useState5, 2),
    orientation = _useState6[0],
    setOrientation = _useState6[1];
  var _useState7 = useState(false),
    _useState8 = _slicedToArray(_useState7, 2),
    isFullscreen = _useState8[0],
    setIsFullscreen = _useState8[1];

  // Get current device configuration
  var deviceConfig = useMemo(function () {
    var device = DEVICE_PRESETS[currentDevice];
    var variant = device.variants[currentVariant];
    return _objectSpread(_objectSpread(_objectSpread({}, device), variant), {}, {
      category: device.category
    });
  }, [currentDevice, currentVariant]);

  // Handle device type change
  var handleDeviceChange = useCallback(function (newDevice) {
    setCurrentDevice(newDevice);
    var newVariant = DEVICE_PRESETS[newDevice].defaultVariant;
    setCurrentVariant(newVariant);
    if (onDeviceChange) {
      onDeviceChange({
        device: newDevice,
        variant: newVariant,
        config: _objectSpread(_objectSpread({}, DEVICE_PRESETS[newDevice]), DEVICE_PRESETS[newDevice].variants[newVariant])
      });
    }
  }, [onDeviceChange]);

  // Handle variant change
  var handleVariantChange = useCallback(function (newVariant) {
    setCurrentVariant(newVariant);
    if (onDeviceChange) {
      onDeviceChange({
        device: currentDevice,
        variant: newVariant,
        config: _objectSpread(_objectSpread({}, DEVICE_PRESETS[currentDevice]), DEVICE_PRESETS[currentDevice].variants[newVariant])
      });
    }
  }, [currentDevice, onDeviceChange]);

  // Handle orientation change
  var handleOrientationChange = useCallback(function () {
    var newOrientation = orientation === 'portrait' ? 'landscape' : 'portrait';
    setOrientation(newOrientation);
  }, [orientation]);

  // Handle fullscreen toggle
  var handleFullscreenToggle = useCallback(function () {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);
  return /*#__PURE__*/React.createElement("div", {
    className: className
  }, showControls && /*#__PURE__*/React.createElement(DeviceControls, null, /*#__PURE__*/React.createElement(Space, null, Object.entries(DEVICE_PRESETS).map(function (_ref2) {
    var _ref3 = _slicedToArray(_ref2, 2),
      key = _ref3[0],
      device = _ref3[1];
    return /*#__PURE__*/React.createElement(Button, {
      key: key,
      type: currentDevice === key ? 'primary' : 'default',
      icon: device.icon,
      size: "small",
      onClick: function onClick() {
        return handleDeviceChange(key);
      }
    }, device.name);
  })), /*#__PURE__*/React.createElement(Select, {
    value: currentVariant,
    onChange: handleVariantChange,
    size: "small",
    style: {
      minWidth: 120
    }
  }, Object.entries(deviceConfig.variants || {}).map(function (_ref4) {
    var _ref5 = _slicedToArray(_ref4, 2),
      key = _ref5[0],
      variant = _ref5[1];
    return /*#__PURE__*/React.createElement(Option, {
      key: key,
      value: key
    }, variant.name);
  })), deviceConfig.category !== 'desktop' && /*#__PURE__*/React.createElement(Tooltip, {
    title: "Switch to ".concat(orientation === 'portrait' ? 'landscape' : 'portrait')
  }, /*#__PURE__*/React.createElement(Button, {
    icon: orientation === 'portrait' ? /*#__PURE__*/React.createElement(RotateRightOutlined, null) : /*#__PURE__*/React.createElement(RotateLeftOutlined, null),
    size: "small",
    onClick: handleOrientationChange
  })), /*#__PURE__*/React.createElement(Tooltip, {
    title: "Toggle Fullscreen"
  }, /*#__PURE__*/React.createElement(Button, {
    icon: /*#__PURE__*/React.createElement(FullscreenOutlined, null),
    size: "small",
    onClick: handleFullscreenToggle,
    type: isFullscreen ? 'primary' : 'default'
  })), /*#__PURE__*/React.createElement(Badge, {
    count: "".concat(deviceConfig.width, "\xD7").concat(deviceConfig.height),
    style: {
      backgroundColor: '#108ee9'
    }
  })), /*#__PURE__*/React.createElement(DeviceFrameContainer, {
    orientation: orientation
  }, showInfo && /*#__PURE__*/React.createElement(DeviceInfo, null, deviceConfig.name, " - ", deviceConfig.width, "\xD7", deviceConfig.height, orientation === 'landscape' && ' (Landscape)'), /*#__PURE__*/React.createElement(DeviceFrame, {
    category: deviceConfig.category,
    frame: deviceConfig.frame
  }, /*#__PURE__*/React.createElement(DeviceScreen, {
    width: deviceConfig.width,
    height: deviceConfig.height,
    scale: deviceConfig.scale,
    orientation: orientation,
    category: deviceConfig.category
  }, children))));
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (DevicePreviewFrame)));

/***/ }),

/***/ 75121:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__);






function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Enhanced WebSocket service for real-time preview synchronization
 * Handles collaborative editing, preview state sync, and real-time updates
 */
var PreviewWebSocketService = /*#__PURE__*/function () {
  function PreviewWebSocketService() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(this, PreviewWebSocketService);
    // Basic WebSocket properties
    this.socket = null;
    this.connected = false;
    this.connecting = false;
    this.eventListeners = new Map();
    this.options = _objectSpread({
      autoConnect: false,
      autoReconnect: true,
      reconnectInterval: 2000,
      maxReconnectAttempts: 10,
      debug: false
    }, options);

    // Preview-specific state
    this.previewState = {
      components: new Map(),
      deviceSettings: {},
      viewportState: {},
      collaborators: new Map(),
      cursors: new Map()
    };

    // Event handlers for preview-specific events
    this.previewHandlers = new Map();

    // Debounce settings for different update types
    this.debounceSettings = {
      componentUpdate: 300,
      deviceChange: 100,
      cursorMove: 50,
      viewportChange: 200
    };

    // Initialize preview-specific message handlers
    this.initializePreviewHandlers();
  }

  /**
   * Basic WebSocket connection methods
   */
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(PreviewWebSocketService, [{
    key: "connect",
    value: function connect() {
      var _this = this;
      var url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'ws://localhost:8000/ws/collaboration/';
      return new Promise(function (resolve, reject) {
        if (_this.connected) {
          resolve(_this);
          return;
        }
        if (_this.connecting) {
          reject(new Error('Connection already in progress'));
          return;
        }
        _this.connecting = true;
        try {
          _this.socket = new WebSocket(url);
          _this.socket.onopen = function (event) {
            _this.connected = true;
            _this.connecting = false;
            _this.triggerEvent('connect', event);
            resolve(_this);
          };
          _this.socket.onclose = function (event) {
            _this.connected = false;
            _this.connecting = false;
            _this.triggerEvent('disconnect', event);
          };
          _this.socket.onerror = function (event) {
            _this.connecting = false;
            _this.triggerEvent('error', event);
            reject(new Error('WebSocket connection failed'));
          };
          _this.socket.onmessage = function (event) {
            try {
              var data = JSON.parse(event.data);
              _this.triggerEvent('message', data);
              if (data.type) {
                _this.triggerEvent(data.type, data);
              }
            } catch (error) {
              console.error('Error parsing WebSocket message:', error);
            }
          };
        } catch (error) {
          _this.connecting = false;
          reject(error);
        }
      });
    }
  }, {
    key: "disconnect",
    value: function disconnect() {
      if (this.socket) {
        this.socket.close();
        this.socket = null;
        this.connected = false;
      }
    }
  }, {
    key: "send",
    value: function send(message) {
      var _this2 = this;
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      return new Promise(function (resolve, reject) {
        if (!_this2.connected) {
          reject(new Error('WebSocket not connected'));
          return;
        }
        try {
          var messageStr = typeof message === 'string' ? message : JSON.stringify(message);
          _this2.socket.send(messageStr);
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    }
  }, {
    key: "on",
    value: function on(eventType, handler) {
      if (!this.eventListeners.has(eventType)) {
        this.eventListeners.set(eventType, []);
      }
      this.eventListeners.get(eventType).push(handler);
    }
  }, {
    key: "triggerEvent",
    value: function triggerEvent(eventType, data) {
      var handlers = this.eventListeners.get(eventType) || [];
      handlers.forEach(function (handler) {
        try {
          handler(data);
        } catch (error) {
          console.error("Error in event handler for ".concat(eventType, ":"), error);
        }
      });
    }

    /**
     * Initialize preview-specific WebSocket message handlers
     */
  }, {
    key: "initializePreviewHandlers",
    value: function initializePreviewHandlers() {
      // Component update handlers
      this.on('component_updated', this.handleComponentUpdate.bind(this));
      this.on('component_added', this.handleComponentAdd.bind(this));
      this.on('component_deleted', this.handleComponentDelete.bind(this));
      this.on('component_moved', this.handleComponentMove.bind(this));

      // Device and viewport handlers
      this.on('device_changed', this.handleDeviceChange.bind(this));
      this.on('viewport_changed', this.handleViewportChange.bind(this));

      // Collaboration handlers
      this.on('collaborator_joined', this.handleCollaboratorJoin.bind(this));
      this.on('collaborator_left', this.handleCollaboratorLeave.bind(this));
      this.on('cursor_moved', this.handleCursorMove.bind(this));
      this.on('selection_changed', this.handleSelectionChange.bind(this));

      // Preview state sync
      this.on('preview_state_sync', this.handlePreviewStateSync.bind(this));
      this.on('preview_state_request', this.handlePreviewStateRequest.bind(this));
    }

    /**
     * Send component update with real-time synchronization
     */
  }, {
    key: "sendComponentUpdate",
    value: (function () {
      var _sendComponentUpdate = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee(componentId, componentData) {
        var options,
          message,
          _args = arguments;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              options = _args.length > 2 && _args[2] !== undefined ? _args[2] : {};
              message = {
                type: 'component_update',
                component_id: componentId,
                component_data: componentData,
                timestamp: new Date().toISOString(),
                user_id: options.userId || 'anonymous',
                session_id: options.sessionId,
                immediate: options.immediate || false
              }; // Update local state immediately for optimistic updates
              if (options.immediate) {
                this.previewState.components.set(componentId, componentData);
                this.triggerPreviewEvent('component_updated_local', message);
              }
              return _context.abrupt("return", this.send(message, {
                priority: options.immediate ? 'high' : 'normal',
                compress: true
              }));
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee, this);
      }));
      function sendComponentUpdate(_x, _x2) {
        return _sendComponentUpdate.apply(this, arguments);
      }
      return sendComponentUpdate;
    }()
    /**
     * Send device change notification
     */
    )
  }, {
    key: "sendDeviceChange",
    value: (function () {
      var _sendDeviceChange = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee2(deviceType, deviceConfig) {
        var options,
          message,
          _args2 = arguments;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              options = _args2.length > 2 && _args2[2] !== undefined ? _args2[2] : {};
              message = {
                type: 'device_change',
                device_type: deviceType,
                device_config: deviceConfig,
                timestamp: new Date().toISOString(),
                user_id: options.userId || 'anonymous',
                session_id: options.sessionId
              }; // Update local device state
              this.previewState.deviceSettings = {
                type: deviceType,
                config: deviceConfig,
                timestamp: new Date()
              };
              return _context2.abrupt("return", this.send(message, {
                compress: false
              }));
            case 4:
            case "end":
              return _context2.stop();
          }
        }, _callee2, this);
      }));
      function sendDeviceChange(_x3, _x4) {
        return _sendDeviceChange.apply(this, arguments);
      }
      return sendDeviceChange;
    }()
    /**
     * Send cursor position for collaborative editing
     */
    )
  }, {
    key: "sendCursorPosition",
    value: (function () {
      var _sendCursorPosition = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee3(position) {
        var options,
          message,
          _args3 = arguments;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              options = _args3.length > 1 && _args3[1] !== undefined ? _args3[1] : {};
              message = {
                type: 'cursor_move',
                position: position,
                timestamp: new Date().toISOString(),
                user_id: options.userId || 'anonymous',
                session_id: options.sessionId
              };
              return _context3.abrupt("return", this.send(message, {
                priority: 'low',
                compress: false,
                throttle: this.debounceSettings.cursorMove
              }));
            case 3:
            case "end":
              return _context3.stop();
          }
        }, _callee3, this);
      }));
      function sendCursorPosition(_x5) {
        return _sendCursorPosition.apply(this, arguments);
      }
      return sendCursorPosition;
    }()
    /**
     * Send viewport change notification
     */
    )
  }, {
    key: "sendViewportChange",
    value: (function () {
      var _sendViewportChange = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee4(viewport) {
        var options,
          message,
          _args4 = arguments;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              options = _args4.length > 1 && _args4[1] !== undefined ? _args4[1] : {};
              message = {
                type: 'viewport_change',
                viewport: viewport,
                timestamp: new Date().toISOString(),
                user_id: options.userId || 'anonymous',
                session_id: options.sessionId
              }; // Update local viewport state
              this.previewState.viewportState = _objectSpread(_objectSpread({}, viewport), {}, {
                timestamp: new Date()
              });
              return _context4.abrupt("return", this.send(message, {
                compress: true
              }));
            case 4:
            case "end":
              return _context4.stop();
          }
        }, _callee4, this);
      }));
      function sendViewportChange(_x6) {
        return _sendViewportChange.apply(this, arguments);
      }
      return sendViewportChange;
    }()
    /**
     * Request current preview state from server
     */
    )
  }, {
    key: "requestPreviewState",
    value: (function () {
      var _requestPreviewState = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee5(sessionId) {
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee5$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              return _context5.abrupt("return", this.send({
                type: 'preview_state_request',
                session_id: sessionId,
                timestamp: new Date().toISOString()
              }));
            case 1:
            case "end":
              return _context5.stop();
          }
        }, _callee5, this);
      }));
      function requestPreviewState(_x7) {
        return _requestPreviewState.apply(this, arguments);
      }
      return requestPreviewState;
    }()
    /**
     * Sync preview state with other collaborators
     */
    )
  }, {
    key: "syncPreviewState",
    value: (function () {
      var _syncPreviewState = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee6(sessionId) {
        var state;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee6$(_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              state = {
                components: Array.from(this.previewState.components.entries()),
                deviceSettings: this.previewState.deviceSettings,
                viewportState: this.previewState.viewportState,
                timestamp: new Date().toISOString()
              };
              return _context6.abrupt("return", this.send({
                type: 'preview_state_sync',
                session_id: sessionId,
                state: state
              }, {
                compress: true
              }));
            case 2:
            case "end":
              return _context6.stop();
          }
        }, _callee6, this);
      }));
      function syncPreviewState(_x8) {
        return _syncPreviewState.apply(this, arguments);
      }
      return syncPreviewState;
    }() // Message handlers
    )
  }, {
    key: "handleComponentUpdate",
    value: function handleComponentUpdate(data) {
      var component_id = data.component_id,
        component_data = data.component_data,
        user_id = data.user_id,
        timestamp = data.timestamp;

      // Update local state
      this.previewState.components.set(component_id, _objectSpread(_objectSpread({}, component_data), {}, {
        lastUpdatedBy: user_id,
        lastUpdated: timestamp
      }));

      // Trigger preview event
      this.triggerPreviewEvent('component_updated', data);
    }
  }, {
    key: "handleComponentAdd",
    value: function handleComponentAdd(data) {
      var component = data.component;
      if (component && component.id) {
        this.previewState.components.set(component.id, component);
        this.triggerPreviewEvent('component_added', data);
      }
    }
  }, {
    key: "handleComponentDelete",
    value: function handleComponentDelete(data) {
      var component_id = data.component_id;
      if (component_id) {
        this.previewState.components["delete"](component_id);
        this.triggerPreviewEvent('component_deleted', data);
      }
    }
  }, {
    key: "handleComponentMove",
    value: function handleComponentMove(data) {
      var component_id = data.component_id,
        new_position = data.new_position;
      var component = this.previewState.components.get(component_id);
      if (component) {
        this.previewState.components.set(component_id, _objectSpread(_objectSpread({}, component), {}, {
          position: new_position
        }));
        this.triggerPreviewEvent('component_moved', data);
      }
    }
  }, {
    key: "handleDeviceChange",
    value: function handleDeviceChange(data) {
      var device_type = data.device_type,
        device_config = data.device_config,
        user_id = data.user_id;
      this.previewState.deviceSettings = {
        type: device_type,
        config: device_config,
        changedBy: user_id,
        timestamp: new Date()
      };
      this.triggerPreviewEvent('device_changed', data);
    }
  }, {
    key: "handleViewportChange",
    value: function handleViewportChange(data) {
      var viewport = data.viewport,
        user_id = data.user_id;
      this.previewState.viewportState = _objectSpread(_objectSpread({}, viewport), {}, {
        changedBy: user_id,
        timestamp: new Date()
      });
      this.triggerPreviewEvent('viewport_changed', data);
    }
  }, {
    key: "handleCollaboratorJoin",
    value: function handleCollaboratorJoin(data) {
      var user_id = data.user_id,
        username = data.username,
        avatar = data.avatar;
      this.previewState.collaborators.set(user_id, {
        username: username,
        avatar: avatar,
        joinedAt: new Date(),
        isActive: true
      });
      this.triggerPreviewEvent('collaborator_joined', data);
    }
  }, {
    key: "handleCollaboratorLeave",
    value: function handleCollaboratorLeave(data) {
      var user_id = data.user_id;
      this.previewState.collaborators["delete"](user_id);
      this.previewState.cursors["delete"](user_id);
      this.triggerPreviewEvent('collaborator_left', data);
    }
  }, {
    key: "handleCursorMove",
    value: function handleCursorMove(data) {
      var user_id = data.user_id,
        position = data.position;
      this.previewState.cursors.set(user_id, {
        position: position,
        timestamp: new Date()
      });
      this.triggerPreviewEvent('cursor_moved', data);
    }
  }, {
    key: "handleSelectionChange",
    value: function handleSelectionChange(data) {
      var user_id = data.user_id,
        selection = data.selection;
      var collaborator = this.previewState.collaborators.get(user_id);
      if (collaborator) {
        this.previewState.collaborators.set(user_id, _objectSpread(_objectSpread({}, collaborator), {}, {
          selection: selection,
          lastActivity: new Date()
        }));
      }
      this.triggerPreviewEvent('selection_changed', data);
    }
  }, {
    key: "handlePreviewStateSync",
    value: function handlePreviewStateSync(data) {
      var _this3 = this;
      var state = data.state;
      if (state) {
        // Update components
        if (state.components) {
          this.previewState.components.clear();
          state.components.forEach(function (_ref) {
            var _ref2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref, 2),
              id = _ref2[0],
              component = _ref2[1];
            _this3.previewState.components.set(id, component);
          });
        }

        // Update device settings
        if (state.deviceSettings) {
          this.previewState.deviceSettings = state.deviceSettings;
        }

        // Update viewport state
        if (state.viewportState) {
          this.previewState.viewportState = state.viewportState;
        }
      }
      this.triggerPreviewEvent('preview_state_synced', data);
    }
  }, {
    key: "handlePreviewStateRequest",
    value: function handlePreviewStateRequest(data) {
      // Respond with current state
      this.syncPreviewState(data.session_id);
    }

    /**
     * Trigger preview-specific events
     */
  }, {
    key: "triggerPreviewEvent",
    value: function triggerPreviewEvent(eventType, data) {
      var handlers = this.previewHandlers.get(eventType) || [];
      handlers.forEach(function (handler) {
        try {
          handler(data);
        } catch (error) {
          console.error("Error in preview event handler for ".concat(eventType, ":"), error);
        }
      });
    }

    /**
     * Add preview event listener
     */
  }, {
    key: "onPreviewEvent",
    value: function onPreviewEvent(eventType, handler) {
      var _this4 = this;
      if (!this.previewHandlers.has(eventType)) {
        this.previewHandlers.set(eventType, []);
      }
      this.previewHandlers.get(eventType).push(handler);
      return function () {
        var handlers = _this4.previewHandlers.get(eventType) || [];
        var index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      };
    }

    /**
     * Get current preview state
     */
  }, {
    key: "getPreviewState",
    value: function getPreviewState() {
      return {
        components: Array.from(this.previewState.components.entries()),
        deviceSettings: this.previewState.deviceSettings,
        viewportState: this.previewState.viewportState,
        collaborators: Array.from(this.previewState.collaborators.entries()),
        cursors: Array.from(this.previewState.cursors.entries())
      };
    }

    /**
     * Clear preview state
     */
  }, {
    key: "clearPreviewState",
    value: function clearPreviewState() {
      this.previewState.components.clear();
      this.previewState.collaborators.clear();
      this.previewState.cursors.clear();
      this.previewState.deviceSettings = {};
      this.previewState.viewportState = {};
    }

    /**
     * Join a collaborative session
     */
  }, {
    key: "joinSession",
    value: (function () {
      var _joinSession = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee7(sessionId) {
        var userInfo,
          _args7 = arguments;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee7$(_context7) {
          while (1) switch (_context7.prev = _context7.next) {
            case 0:
              userInfo = _args7.length > 1 && _args7[1] !== undefined ? _args7[1] : {};
              return _context7.abrupt("return", this.send({
                type: 'join_session',
                session_id: sessionId,
                user_info: {
                  username: userInfo.username || 'Anonymous',
                  avatar: userInfo.avatar || null,
                  timestamp: new Date().toISOString()
                }
              }));
            case 2:
            case "end":
              return _context7.stop();
          }
        }, _callee7, this);
      }));
      function joinSession(_x9) {
        return _joinSession.apply(this, arguments);
      }
      return joinSession;
    }()
    /**
     * Leave a collaborative session
     */
    )
  }, {
    key: "leaveSession",
    value: (function () {
      var _leaveSession = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee8(sessionId) {
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function _callee8$(_context8) {
          while (1) switch (_context8.prev = _context8.next) {
            case 0:
              return _context8.abrupt("return", this.send({
                type: 'leave_session',
                session_id: sessionId,
                timestamp: new Date().toISOString()
              }));
            case 1:
            case "end":
              return _context8.stop();
          }
        }, _callee8, this);
      }));
      function leaveSession(_x0) {
        return _leaveSession.apply(this, arguments);
      }
      return leaveSession;
    }())
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PreviewWebSocketService);

/***/ }),

/***/ 85331:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   tI: () => (/* binding */ setCurrentView)
/* harmony export */ });
/* unused harmony exports TOGGLE_SIDEBAR, SET_CURRENT_VIEW, TOGGLE_PREVIEW_MODE, UI_LOADING_START, UI_LOADING_COMPLETE, toggleSidebar, togglePreviewMode, startLoading, completeLoading */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UI Reducer
 *
 * This reducer handles UI state, including sidebar, current view, and preview mode.
 */

// Action types
var TOGGLE_SIDEBAR = 'TOGGLE_SIDEBAR';
var SET_CURRENT_VIEW = 'SET_CURRENT_VIEW';
var TOGGLE_PREVIEW_MODE = 'TOGGLE_PREVIEW_MODE';
var UI_LOADING_START = 'UI_LOADING_START';
var UI_LOADING_COMPLETE = 'UI_LOADING_COMPLETE';

// Initial state
var initialState = {
  sidebarOpen: true,
  currentView: 'components',
  previewMode: false,
  loading: false
};

/**
 * UI reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
var uiReducer = function uiReducer() {
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;
  switch (action.type) {
    case TOGGLE_SIDEBAR:
      return _objectSpread(_objectSpread({}, state), {}, {
        sidebarOpen: !state.sidebarOpen
      });
    case SET_CURRENT_VIEW:
      return _objectSpread(_objectSpread({}, state), {}, {
        currentView: action.payload
      });
    case TOGGLE_PREVIEW_MODE:
      return _objectSpread(_objectSpread({}, state), {}, {
        previewMode: !state.previewMode
      });
    case UI_LOADING_START:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: true
      });
    case UI_LOADING_COMPLETE:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: false
      });
    default:
      return state;
  }
};

// Action creators
var toggleSidebar = function toggleSidebar() {
  return {
    type: TOGGLE_SIDEBAR
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: SET_CURRENT_VIEW,
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: TOGGLE_PREVIEW_MODE
  };
};
var startLoading = function startLoading() {
  return {
    type: UI_LOADING_START
  };
};
var completeLoading = function completeLoading() {
  return {
    type: UI_LOADING_COMPLETE
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (uiReducer)));

/***/ })

}]);