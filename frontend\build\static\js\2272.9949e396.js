"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[2272],{

/***/ 34735:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ tour_es)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(89379);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/@rc-component/portal/es/index.js + 6 modules
var es = __webpack_require__(45062);
// EXTERNAL MODULE: ./node_modules/@rc-component/trigger/es/index.js + 11 modules
var trigger_es = __webpack_require__(62427);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useLayoutEffect.js
var useLayoutEffect = __webpack_require__(30981);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(12533);
;// ./node_modules/@rc-component/tour/es/hooks/useClosable.js



function isConfigObj(closable) {
  return closable !== null && (0,esm_typeof/* default */.A)(closable) === 'object';
}
/**
 * Convert `closable` to ClosableConfig.
 * When `preset` is true, will auto fill ClosableConfig with default value.
 */
function getClosableConfig(closable, closeIcon, preset) {
  if (closable === false || closeIcon === false && (!isConfigObj(closable) || !closable.closeIcon)) {
    return null;
  }
  var mergedCloseIcon = typeof closeIcon !== 'boolean' ? closeIcon : undefined;
  if (isConfigObj(closable)) {
    var _closable$closeIcon;
    return (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, closable), {}, {
      closeIcon: (_closable$closeIcon = closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : mergedCloseIcon
    });
  }

  // When StepClosable no need auto fill, but RootClosable need this.
  return preset || closable || closeIcon ? {
    closeIcon: mergedCloseIcon
  } : 'empty';
}
function useClosable(stepClosable, stepCloseIcon, closable, closeIcon) {
  return react.useMemo(function () {
    var stepClosableConfig = getClosableConfig(stepClosable, stepCloseIcon, false);
    var rootClosableConfig = getClosableConfig(closable, closeIcon, true);
    if (stepClosableConfig !== 'empty') {
      return stepClosableConfig;
    }
    return rootClosableConfig;
  }, [closable, closeIcon, stepClosable, stepCloseIcon]);
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useEvent.js
var useEvent = __webpack_require__(26956);
;// ./node_modules/@rc-component/tour/es/util.js
function isInViewPort(element) {
  var viewWidth = window.innerWidth || document.documentElement.clientWidth;
  var viewHeight = window.innerHeight || document.documentElement.clientHeight;
  var _element$getBoundingC = element.getBoundingClientRect(),
    top = _element$getBoundingC.top,
    right = _element$getBoundingC.right,
    bottom = _element$getBoundingC.bottom,
    left = _element$getBoundingC.left;
  return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight;
}
function getPlacement(targetElement, placement, stepPlacement) {
  var _ref;
  return (_ref = stepPlacement !== null && stepPlacement !== void 0 ? stepPlacement : placement) !== null && _ref !== void 0 ? _ref : targetElement === null ? 'center' : 'bottom';
}
;// ./node_modules/@rc-component/tour/es/hooks/useTarget.js





function isValidNumber(val) {
  return typeof val === 'number' && !Number.isNaN(val);
}
function useTarget(target, open, gap, scrollIntoViewOptions) {
  // ========================= Target =========================
  // We trade `undefined` as not get target by function yet.
  // `null` as empty target.
  var _useState = (0,react.useState)(undefined),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    targetElement = _useState2[0],
    setTargetElement = _useState2[1];
  (0,useLayoutEffect/* default */.A)(function () {
    var nextElement = typeof target === 'function' ? target() : target;
    setTargetElement(nextElement || null);
  });

  // ========================= Align ==========================
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    posInfo = _useState4[0],
    setPosInfo = _useState4[1];
  var updatePos = (0,useEvent/* default */.A)(function () {
    if (targetElement) {
      // Exist target element. We should scroll and get target position
      if (!isInViewPort(targetElement) && open) {
        targetElement.scrollIntoView(scrollIntoViewOptions);
      }
      var _targetElement$getBou = targetElement.getBoundingClientRect(),
        left = _targetElement$getBou.left,
        top = _targetElement$getBou.top,
        width = _targetElement$getBou.width,
        height = _targetElement$getBou.height;
      var nextPosInfo = {
        left: left,
        top: top,
        width: width,
        height: height,
        radius: 0
      };
      setPosInfo(function (origin) {
        if (JSON.stringify(origin) !== JSON.stringify(nextPosInfo)) {
          return nextPosInfo;
        }
        return origin;
      });
    } else {
      // Not exist target which means we just show in center
      setPosInfo(null);
    }
  });
  var getGapOffset = function getGapOffset(index) {
    var _ref;
    return (_ref = Array.isArray(gap === null || gap === void 0 ? void 0 : gap.offset) ? gap === null || gap === void 0 ? void 0 : gap.offset[index] : gap === null || gap === void 0 ? void 0 : gap.offset) !== null && _ref !== void 0 ? _ref : 6;
  };
  (0,useLayoutEffect/* default */.A)(function () {
    updatePos();
    // update when window resize
    window.addEventListener('resize', updatePos);
    return function () {
      window.removeEventListener('resize', updatePos);
    };
  }, [targetElement, open, updatePos]);

  // ======================== PosInfo =========================
  var mergedPosInfo = (0,react.useMemo)(function () {
    if (!posInfo) {
      return posInfo;
    }
    var gapOffsetX = getGapOffset(0);
    var gapOffsetY = getGapOffset(1);
    var gapRadius = isValidNumber(gap === null || gap === void 0 ? void 0 : gap.radius) ? gap === null || gap === void 0 ? void 0 : gap.radius : 2;
    return {
      left: posInfo.left - gapOffsetX,
      top: posInfo.top - gapOffsetY,
      width: posInfo.width + gapOffsetX * 2,
      height: posInfo.height + gapOffsetY * 2,
      radius: gapRadius
    };
  }, [posInfo, gap]);
  return [mergedPosInfo, targetElement];
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useId.js
var useId = __webpack_require__(56855);
;// ./node_modules/@rc-component/tour/es/Mask.js







var COVER_PROPS = {
  fill: 'transparent',
  pointerEvents: 'auto'
};
var Mask = function Mask(props) {
  var prefixCls = props.prefixCls,
    rootClassName = props.rootClassName,
    pos = props.pos,
    showMask = props.showMask,
    _props$style = props.style,
    style = _props$style === void 0 ? {} : _props$style,
    _props$fill = props.fill,
    fill = _props$fill === void 0 ? "rgba(0,0,0,0.5)" : _props$fill,
    open = props.open,
    animated = props.animated,
    zIndex = props.zIndex,
    disabledInteraction = props.disabledInteraction;
  var id = (0,useId/* default */.A)();
  var maskId = "".concat(prefixCls, "-mask-").concat(id);
  var mergedAnimated = (0,esm_typeof/* default */.A)(animated) === 'object' ? animated === null || animated === void 0 ? void 0 : animated.placeholder : animated;
  var isSafari = typeof navigator !== 'undefined' && /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  var maskRectSize = isSafari ? {
    width: '100%',
    height: '100%'
  } : {
    width: '100vw',
    height: '100vh'
  };
  return /*#__PURE__*/react.createElement(es/* default */.A, {
    open: open,
    autoLock: true
  }, /*#__PURE__*/react.createElement("div", {
    className: classnames_default()("".concat(prefixCls, "-mask"), rootClassName),
    style: (0,objectSpread2/* default */.A)({
      position: 'fixed',
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      zIndex: zIndex,
      pointerEvents: pos && !disabledInteraction ? 'none' : 'auto'
    }, style)
  }, showMask ? /*#__PURE__*/react.createElement("svg", {
    style: {
      width: '100%',
      height: '100%'
    }
  }, /*#__PURE__*/react.createElement("defs", null, /*#__PURE__*/react.createElement("mask", {
    id: maskId
  }, /*#__PURE__*/react.createElement("rect", (0,esm_extends/* default */.A)({
    x: "0",
    y: "0"
  }, maskRectSize, {
    fill: "white"
  })), pos && /*#__PURE__*/react.createElement("rect", {
    x: pos.left,
    y: pos.top,
    rx: pos.radius,
    width: pos.width,
    height: pos.height,
    fill: "black",
    className: mergedAnimated ? "".concat(prefixCls, "-placeholder-animated") : ''
  }))), /*#__PURE__*/react.createElement("rect", {
    x: "0",
    y: "0",
    width: "100%",
    height: "100%",
    fill: fill,
    mask: "url(#".concat(maskId, ")")
  }), pos && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("rect", (0,esm_extends/* default */.A)({}, COVER_PROPS, {
    x: "0",
    y: "0",
    width: "100%",
    height: pos.top
  })), /*#__PURE__*/react.createElement("rect", (0,esm_extends/* default */.A)({}, COVER_PROPS, {
    x: "0",
    y: "0",
    width: pos.left,
    height: "100%"
  })), /*#__PURE__*/react.createElement("rect", (0,esm_extends/* default */.A)({}, COVER_PROPS, {
    x: "0",
    y: pos.top + pos.height,
    width: "100%",
    height: "calc(100vh - ".concat(pos.top + pos.height, "px)")
  })), /*#__PURE__*/react.createElement("rect", (0,esm_extends/* default */.A)({}, COVER_PROPS, {
    x: pos.left + pos.width,
    y: "0",
    width: "calc(100vw - ".concat(pos.left + pos.width, "px)"),
    height: "100%"
  })))) : null));
};
/* harmony default export */ const es_Mask = (Mask);
;// ./node_modules/@rc-component/tour/es/placements.js

var autoAdjustOverflow = {
  adjustX: 1,
  adjustY: 1
};
var targetOffset = [0, 0];
var basePlacements = {
  left: {
    points: ['cr', 'cl'],
    offset: [-8, 0]
  },
  right: {
    points: ['cl', 'cr'],
    offset: [8, 0]
  },
  top: {
    points: ['bc', 'tc'],
    offset: [0, -8]
  },
  bottom: {
    points: ['tc', 'bc'],
    offset: [0, 8]
  },
  topLeft: {
    points: ['bl', 'tl'],
    offset: [0, -8]
  },
  leftTop: {
    points: ['tr', 'tl'],
    offset: [-8, 0]
  },
  topRight: {
    points: ['br', 'tr'],
    offset: [0, -8]
  },
  rightTop: {
    points: ['tl', 'tr'],
    offset: [8, 0]
  },
  bottomRight: {
    points: ['tr', 'br'],
    offset: [0, 8]
  },
  rightBottom: {
    points: ['bl', 'br'],
    offset: [8, 0]
  },
  bottomLeft: {
    points: ['tl', 'bl'],
    offset: [0, 8]
  },
  leftBottom: {
    points: ['br', 'bl'],
    offset: [-8, 0]
  }
};
function getPlacements() {
  var arrowPointAtCenter = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
  var placements = {};
  Object.keys(basePlacements).forEach(function (key) {
    placements[key] = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, basePlacements[key]), {}, {
      autoArrow: arrowPointAtCenter,
      targetOffset: targetOffset
    });
  });
  return placements;
}
var placements = getPlacements();
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/rc-util/es/pickAttrs.js
var pickAttrs = __webpack_require__(72065);
;// ./node_modules/@rc-component/tour/es/TourStep/DefaultPanel.js





function DefaultPanel(props) {
  var _closable$closeIcon;
  var prefixCls = props.prefixCls,
    current = props.current,
    total = props.total,
    title = props.title,
    description = props.description,
    onClose = props.onClose,
    onPrev = props.onPrev,
    onNext = props.onNext,
    onFinish = props.onFinish,
    className = props.className,
    closable = props.closable;
  var ariaProps = (0,pickAttrs/* default */.A)(closable || {}, true);
  var closeIcon = (_closable$closeIcon = closable === null || closable === void 0 ? void 0 : closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : /*#__PURE__*/react.createElement("span", {
    className: "".concat(prefixCls, "-close-x")
  }, "\xD7");
  var mergedClosable = !!closable;
  return /*#__PURE__*/react.createElement("div", {
    className: classnames_default()("".concat(prefixCls, "-content"), className)
  }, /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-inner")
  }, mergedClosable && /*#__PURE__*/react.createElement("button", (0,esm_extends/* default */.A)({
    type: "button",
    onClick: onClose,
    "aria-label": "Close"
  }, ariaProps, {
    className: "".concat(prefixCls, "-close")
  }), closeIcon), /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-header")
  }, /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-title")
  }, title)), /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-description")
  }, description), /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-footer")
  }, /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-sliders")
  }, total > 1 ? (0,toConsumableArray/* default */.A)(Array.from({
    length: total
  }).keys()).map(function (item, index) {
    return /*#__PURE__*/react.createElement("span", {
      key: item,
      className: index === current ? 'active' : ''
    });
  }) : null), /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-buttons")
  }, current !== 0 ? /*#__PURE__*/react.createElement("button", {
    className: "".concat(prefixCls, "-prev-btn"),
    onClick: onPrev
  }, "Prev") : null, current === total - 1 ? /*#__PURE__*/react.createElement("button", {
    className: "".concat(prefixCls, "-finish-btn"),
    onClick: onFinish
  }, "Finish") : /*#__PURE__*/react.createElement("button", {
    className: "".concat(prefixCls, "-next-btn"),
    onClick: onNext
  }, "Next")))));
}
;// ./node_modules/@rc-component/tour/es/TourStep/index.js


var TourStep = function TourStep(props) {
  var current = props.current,
    renderPanel = props.renderPanel;
  return /*#__PURE__*/react.createElement(react.Fragment, null, typeof renderPanel === 'function' ? renderPanel(props, current) : /*#__PURE__*/react.createElement(DefaultPanel, props));
};
/* harmony default export */ const es_TourStep = (TourStep);
;// ./node_modules/@rc-component/tour/es/Tour.js





var _excluded = ["prefixCls", "steps", "defaultCurrent", "current", "onChange", "onClose", "onFinish", "open", "mask", "arrow", "rootClassName", "placement", "renderPanel", "gap", "animated", "scrollIntoViewOptions", "zIndex", "closeIcon", "closable", "builtinPlacements", "disabledInteraction"];













var CENTER_PLACEHOLDER = {
  left: '50%',
  top: '50%',
  width: 1,
  height: 1
};
var defaultScrollIntoViewOptions = {
  block: 'center',
  inline: 'center'
};
var Tour = function Tour(props) {
  var _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-tour' : _props$prefixCls,
    _props$steps = props.steps,
    steps = _props$steps === void 0 ? [] : _props$steps,
    defaultCurrent = props.defaultCurrent,
    current = props.current,
    onChange = props.onChange,
    onClose = props.onClose,
    _onFinish = props.onFinish,
    open = props.open,
    _props$mask = props.mask,
    mask = _props$mask === void 0 ? true : _props$mask,
    _props$arrow = props.arrow,
    arrow = _props$arrow === void 0 ? true : _props$arrow,
    rootClassName = props.rootClassName,
    placement = props.placement,
    renderPanel = props.renderPanel,
    gap = props.gap,
    animated = props.animated,
    _props$scrollIntoView = props.scrollIntoViewOptions,
    scrollIntoViewOptions = _props$scrollIntoView === void 0 ? defaultScrollIntoViewOptions : _props$scrollIntoView,
    _props$zIndex = props.zIndex,
    zIndex = _props$zIndex === void 0 ? 1001 : _props$zIndex,
    closeIcon = props.closeIcon,
    closable = props.closable,
    builtinPlacements = props.builtinPlacements,
    disabledInteraction = props.disabledInteraction,
    restProps = (0,objectWithoutProperties/* default */.A)(props, _excluded);
  var triggerRef = react.useRef();
  var _useMergedState = (0,useMergedState/* default */.A)(0, {
      value: current,
      defaultValue: defaultCurrent
    }),
    _useMergedState2 = (0,slicedToArray/* default */.A)(_useMergedState, 2),
    mergedCurrent = _useMergedState2[0],
    setMergedCurrent = _useMergedState2[1];
  var _useMergedState3 = (0,useMergedState/* default */.A)(undefined, {
      value: open,
      postState: function postState(origin) {
        return mergedCurrent < 0 || mergedCurrent >= steps.length ? false : origin !== null && origin !== void 0 ? origin : true;
      }
    }),
    _useMergedState4 = (0,slicedToArray/* default */.A)(_useMergedState3, 2),
    mergedOpen = _useMergedState4[0],
    setMergedOpen = _useMergedState4[1];

  // Record if already rended in the DOM to avoid `findDOMNode` issue
  var _React$useState = react.useState(mergedOpen),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    hasOpened = _React$useState2[0],
    setHasOpened = _React$useState2[1];
  var openRef = react.useRef(mergedOpen);
  (0,useLayoutEffect/* default */.A)(function () {
    if (mergedOpen) {
      if (!openRef.current) {
        setMergedCurrent(0);
      }
      setHasOpened(true);
    }
    openRef.current = mergedOpen;
  }, [mergedOpen]);
  var _ref = steps[mergedCurrent] || {},
    target = _ref.target,
    stepPlacement = _ref.placement,
    stepStyle = _ref.style,
    stepArrow = _ref.arrow,
    stepClassName = _ref.className,
    stepMask = _ref.mask,
    _ref$scrollIntoViewOp = _ref.scrollIntoViewOptions,
    stepScrollIntoViewOptions = _ref$scrollIntoViewOp === void 0 ? defaultScrollIntoViewOptions : _ref$scrollIntoViewOp,
    stepCloseIcon = _ref.closeIcon,
    stepClosable = _ref.closable;
  var mergedClosable = useClosable(stepClosable, stepCloseIcon, closable, closeIcon);
  var mergedMask = mergedOpen && (stepMask !== null && stepMask !== void 0 ? stepMask : mask);
  var mergedScrollIntoViewOptions = stepScrollIntoViewOptions !== null && stepScrollIntoViewOptions !== void 0 ? stepScrollIntoViewOptions : scrollIntoViewOptions;
  var _useTarget = useTarget(target, open, gap, mergedScrollIntoViewOptions),
    _useTarget2 = (0,slicedToArray/* default */.A)(_useTarget, 2),
    posInfo = _useTarget2[0],
    targetElement = _useTarget2[1];
  var mergedPlacement = getPlacement(targetElement, placement, stepPlacement);

  // ========================= arrow =========================
  var mergedArrow = targetElement ? typeof stepArrow === 'undefined' ? arrow : stepArrow : false;
  var arrowPointAtCenter = (0,esm_typeof/* default */.A)(mergedArrow) === 'object' ? mergedArrow.pointAtCenter : false;
  (0,useLayoutEffect/* default */.A)(function () {
    var _triggerRef$current;
    (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.forceAlign();
  }, [arrowPointAtCenter, mergedCurrent]);

  // ========================= Change =========================
  var onInternalChange = function onInternalChange(nextCurrent) {
    setMergedCurrent(nextCurrent);
    onChange === null || onChange === void 0 ? void 0 : onChange(nextCurrent);
  };
  var mergedBuiltinPlacements = (0,react.useMemo)(function () {
    if (builtinPlacements) {
      return typeof builtinPlacements === 'function' ? builtinPlacements({
        arrowPointAtCenter: arrowPointAtCenter
      }) : builtinPlacements;
    }
    return getPlacements(arrowPointAtCenter);
  }, [builtinPlacements, arrowPointAtCenter]);

  // ========================= Render =========================
  // Skip if not init yet
  if (targetElement === undefined || !hasOpened) {
    return null;
  }
  var handleClose = function handleClose() {
    setMergedOpen(false);
    onClose === null || onClose === void 0 ? void 0 : onClose(mergedCurrent);
  };
  var getPopupElement = function getPopupElement() {
    return /*#__PURE__*/react.createElement(es_TourStep, (0,esm_extends/* default */.A)({
      arrow: mergedArrow,
      key: "content",
      prefixCls: prefixCls,
      total: steps.length,
      renderPanel: renderPanel,
      onPrev: function onPrev() {
        onInternalChange(mergedCurrent - 1);
      },
      onNext: function onNext() {
        onInternalChange(mergedCurrent + 1);
      },
      onClose: handleClose,
      current: mergedCurrent,
      onFinish: function onFinish() {
        handleClose();
        _onFinish === null || _onFinish === void 0 ? void 0 : _onFinish();
      }
    }, steps[mergedCurrent], {
      closable: mergedClosable
    }));
  };
  var mergedShowMask = typeof mergedMask === 'boolean' ? mergedMask : !!mergedMask;
  var mergedMaskStyle = typeof mergedMask === 'boolean' ? undefined : mergedMask;

  // when targetElement is not exist, use body as triggerDOMNode
  var getTriggerDOMNode = function getTriggerDOMNode(node) {
    return node || targetElement || document.body;
  };
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es_Mask, {
    zIndex: zIndex,
    prefixCls: prefixCls,
    pos: posInfo,
    showMask: mergedShowMask,
    style: mergedMaskStyle === null || mergedMaskStyle === void 0 ? void 0 : mergedMaskStyle.style,
    fill: mergedMaskStyle === null || mergedMaskStyle === void 0 ? void 0 : mergedMaskStyle.color,
    open: mergedOpen,
    animated: animated,
    rootClassName: rootClassName,
    disabledInteraction: disabledInteraction
  }), /*#__PURE__*/react.createElement(trigger_es/* default */.A, (0,esm_extends/* default */.A)({}, restProps, {
    builtinPlacements: mergedBuiltinPlacements,
    ref: triggerRef,
    popupStyle: stepStyle,
    popupPlacement: mergedPlacement,
    popupVisible: mergedOpen,
    popupClassName: classnames_default()(rootClassName, stepClassName),
    prefixCls: prefixCls,
    popup: getPopupElement,
    forceRender: false,
    destroyPopupOnHide: true,
    zIndex: zIndex,
    getTriggerDOMNode: getTriggerDOMNode,
    arrow: !!mergedArrow
  }), /*#__PURE__*/react.createElement(es/* default */.A, {
    open: mergedOpen,
    autoLock: true
  }, /*#__PURE__*/react.createElement("div", {
    className: classnames_default()(rootClassName, "".concat(prefixCls, "-target-placeholder")),
    style: (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, posInfo || CENTER_PLACEHOLDER), {}, {
      position: 'fixed',
      pointerEvents: 'none'
    })
  }))));
};
/* harmony default export */ const es_Tour = (Tour);
;// ./node_modules/@rc-component/tour/es/index.js

/* harmony default export */ const tour_es = (es_Tour);

/***/ }),

/***/ 45062:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ es)
});

// UNUSED EXPORTS: inlineMock

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-dom/index.js
var react_dom = __webpack_require__(40961);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/canUseDom.js
var canUseDom = __webpack_require__(20998);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(68210);
// EXTERNAL MODULE: ./node_modules/rc-util/es/ref.js
var es_ref = __webpack_require__(8719);
;// ./node_modules/@rc-component/portal/es/Context.js

var OrderContext = /*#__PURE__*/react.createContext(null);
/* harmony default export */ const Context = (OrderContext);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useLayoutEffect.js
var useLayoutEffect = __webpack_require__(30981);
;// ./node_modules/@rc-component/portal/es/useDom.js






var EMPTY_LIST = [];

/**
 * Will add `div` to document. Nest call will keep order
 * @param render Render DOM in document
 */
function useDom(render, debug) {
  var _React$useState = react.useState(function () {
      if (!(0,canUseDom/* default */.A)()) {
        return null;
      }
      var defaultEle = document.createElement('div');
      if (false) {}
      return defaultEle;
    }),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 1),
    ele = _React$useState2[0];

  // ========================== Order ==========================
  var appendedRef = react.useRef(false);
  var queueCreate = react.useContext(Context);
  var _React$useState3 = react.useState(EMPTY_LIST),
    _React$useState4 = (0,slicedToArray/* default */.A)(_React$useState3, 2),
    queue = _React$useState4[0],
    setQueue = _React$useState4[1];
  var mergedQueueCreate = queueCreate || (appendedRef.current ? undefined : function (appendFn) {
    setQueue(function (origin) {
      var newQueue = [appendFn].concat((0,toConsumableArray/* default */.A)(origin));
      return newQueue;
    });
  });

  // =========================== DOM ===========================
  function append() {
    if (!ele.parentElement) {
      document.body.appendChild(ele);
    }
    appendedRef.current = true;
  }
  function cleanup() {
    var _ele$parentElement;
    (_ele$parentElement = ele.parentElement) === null || _ele$parentElement === void 0 ? void 0 : _ele$parentElement.removeChild(ele);
    appendedRef.current = false;
  }
  (0,useLayoutEffect/* default */.A)(function () {
    if (render) {
      if (queueCreate) {
        queueCreate(append);
      } else {
        append();
      }
    } else {
      cleanup();
    }
    return cleanup;
  }, [render]);
  (0,useLayoutEffect/* default */.A)(function () {
    if (queue.length) {
      queue.forEach(function (appendFn) {
        return appendFn();
      });
      setQueue(EMPTY_LIST);
    }
  }, [queue]);
  return [ele, mergedQueueCreate];
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/dynamicCSS.js
var dynamicCSS = __webpack_require__(85089);
// EXTERNAL MODULE: ./node_modules/rc-util/es/getScrollBarSize.js
var getScrollBarSize = __webpack_require__(82987);
;// ./node_modules/@rc-component/portal/es/util.js
/**
 * Test usage export. Do not use in your production
 */
function isBodyOverflowing() {
  return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth;
}
;// ./node_modules/@rc-component/portal/es/useScrollLocker.js






var UNIQUE_ID = "rc-util-locker-".concat(Date.now());
var uuid = 0;
function useScrollLocker(lock) {
  var mergedLock = !!lock;
  var _React$useState = react.useState(function () {
      uuid += 1;
      return "".concat(UNIQUE_ID, "_").concat(uuid);
    }),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 1),
    id = _React$useState2[0];
  (0,useLayoutEffect/* default */.A)(function () {
    if (mergedLock) {
      var scrollbarSize = (0,getScrollBarSize/* getTargetScrollBarSize */.V)(document.body).width;
      var isOverflow = isBodyOverflowing();
      (0,dynamicCSS/* updateCSS */.BD)("\nhtml body {\n  overflow-y: hidden;\n  ".concat(isOverflow ? "width: calc(100% - ".concat(scrollbarSize, "px);") : '', "\n}"), id);
    } else {
      (0,dynamicCSS/* removeCSS */.m6)(id);
    }
    return function () {
      (0,dynamicCSS/* removeCSS */.m6)(id);
    };
  }, [mergedLock, id]);
}
;// ./node_modules/@rc-component/portal/es/mock.js
var inline = false;
function inlineMock(nextInline) {
  if (typeof nextInline === 'boolean') {
    inline = nextInline;
  }
  return inline;
}
;// ./node_modules/@rc-component/portal/es/Portal.js










var getPortalContainer = function getPortalContainer(getContainer) {
  if (getContainer === false) {
    return false;
  }
  if (!(0,canUseDom/* default */.A)() || !getContainer) {
    return null;
  }
  if (typeof getContainer === 'string') {
    return document.querySelector(getContainer);
  }
  if (typeof getContainer === 'function') {
    return getContainer();
  }
  return getContainer;
};
var Portal = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var open = props.open,
    autoLock = props.autoLock,
    getContainer = props.getContainer,
    debug = props.debug,
    _props$autoDestroy = props.autoDestroy,
    autoDestroy = _props$autoDestroy === void 0 ? true : _props$autoDestroy,
    children = props.children;
  var _React$useState = react.useState(open),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    shouldRender = _React$useState2[0],
    setShouldRender = _React$useState2[1];
  var mergedRender = shouldRender || open;

  // ========================= Warning =========================
  if (false) {}

  // ====================== Should Render ======================
  react.useEffect(function () {
    if (autoDestroy || open) {
      setShouldRender(open);
    }
  }, [open, autoDestroy]);

  // ======================== Container ========================
  var _React$useState3 = react.useState(function () {
      return getPortalContainer(getContainer);
    }),
    _React$useState4 = (0,slicedToArray/* default */.A)(_React$useState3, 2),
    innerContainer = _React$useState4[0],
    setInnerContainer = _React$useState4[1];
  react.useEffect(function () {
    var customizeContainer = getPortalContainer(getContainer);

    // Tell component that we check this in effect which is safe to be `null`
    setInnerContainer(customizeContainer !== null && customizeContainer !== void 0 ? customizeContainer : null);
  });
  var _useDom = useDom(mergedRender && !innerContainer, debug),
    _useDom2 = (0,slicedToArray/* default */.A)(_useDom, 2),
    defaultContainer = _useDom2[0],
    queueCreate = _useDom2[1];
  var mergedContainer = innerContainer !== null && innerContainer !== void 0 ? innerContainer : defaultContainer;

  // ========================= Locker ==========================
  useScrollLocker(autoLock && open && (0,canUseDom/* default */.A)() && (mergedContainer === defaultContainer || mergedContainer === document.body));

  // =========================== Ref ===========================
  var childRef = null;
  if (children && (0,es_ref/* supportRef */.f3)(children) && ref) {
    var _ref = children;
    childRef = _ref.ref;
  }
  var mergedRef = (0,es_ref/* useComposeRef */.xK)(childRef, ref);

  // ========================= Render ==========================
  // Do not render when nothing need render
  // When innerContainer is `undefined`, it may not ready since user use ref in the same render
  if (!mergedRender || !(0,canUseDom/* default */.A)() || innerContainer === undefined) {
    return null;
  }

  // Render inline
  var renderInline = mergedContainer === false || inlineMock();
  var reffedChildren = children;
  if (ref) {
    reffedChildren = /*#__PURE__*/react.cloneElement(children, {
      ref: mergedRef
    });
  }
  return /*#__PURE__*/react.createElement(Context.Provider, {
    value: queueCreate
  }, renderInline ? reffedChildren : /*#__PURE__*/(0,react_dom.createPortal)(reffedChildren, mergedContainer));
});
if (false) {}
/* harmony default export */ const es_Portal = (Portal);
;// ./node_modules/@rc-component/portal/es/index.js



/* harmony default export */ const es = (es_Portal);

/***/ }),

/***/ 62427:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ trigger_es)
});

// UNUSED EXPORTS: generateTrigger

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(89379);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@rc-component/portal/es/index.js + 6 modules
var es = __webpack_require__(45062);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-resize-observer/es/index.js + 4 modules
var rc_resize_observer_es = __webpack_require__(26076);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/findDOMNode.js
var findDOMNode = __webpack_require__(66588);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/shadow.js
var shadow = __webpack_require__(72633);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useEvent.js
var useEvent = __webpack_require__(26956);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useId.js
var useId = __webpack_require__(56855);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useLayoutEffect.js
var useLayoutEffect = __webpack_require__(30981);
// EXTERNAL MODULE: ./node_modules/rc-util/es/isMobile.js
var isMobile = __webpack_require__(68430);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/rc-motion/es/index.js + 12 modules
var rc_motion_es = __webpack_require__(57557);
// EXTERNAL MODULE: ./node_modules/rc-util/es/ref.js
var es_ref = __webpack_require__(8719);
;// ./node_modules/@rc-component/trigger/es/Popup/Arrow.js


function Arrow(props) {
  var prefixCls = props.prefixCls,
    align = props.align,
    arrow = props.arrow,
    arrowPos = props.arrowPos;
  var _ref = arrow || {},
    className = _ref.className,
    content = _ref.content;
  var _arrowPos$x = arrowPos.x,
    x = _arrowPos$x === void 0 ? 0 : _arrowPos$x,
    _arrowPos$y = arrowPos.y,
    y = _arrowPos$y === void 0 ? 0 : _arrowPos$y;
  var arrowRef = react.useRef();

  // Skip if no align
  if (!align || !align.points) {
    return null;
  }
  var alignStyle = {
    position: 'absolute'
  };

  // Skip if no need to align
  if (align.autoArrow !== false) {
    var popupPoints = align.points[0];
    var targetPoints = align.points[1];
    var popupTB = popupPoints[0];
    var popupLR = popupPoints[1];
    var targetTB = targetPoints[0];
    var targetLR = targetPoints[1];

    // Top & Bottom
    if (popupTB === targetTB || !['t', 'b'].includes(popupTB)) {
      alignStyle.top = y;
    } else if (popupTB === 't') {
      alignStyle.top = 0;
    } else {
      alignStyle.bottom = 0;
    }

    // Left & Right
    if (popupLR === targetLR || !['l', 'r'].includes(popupLR)) {
      alignStyle.left = x;
    } else if (popupLR === 'l') {
      alignStyle.left = 0;
    } else {
      alignStyle.right = 0;
    }
  }
  return /*#__PURE__*/react.createElement("div", {
    ref: arrowRef,
    className: classnames_default()("".concat(prefixCls, "-arrow"), className),
    style: alignStyle
  }, content);
}
;// ./node_modules/@rc-component/trigger/es/Popup/Mask.js




function Mask(props) {
  var prefixCls = props.prefixCls,
    open = props.open,
    zIndex = props.zIndex,
    mask = props.mask,
    motion = props.motion;
  if (!mask) {
    return null;
  }
  return /*#__PURE__*/react.createElement(rc_motion_es/* default */.Ay, (0,esm_extends/* default */.A)({}, motion, {
    motionAppear: true,
    visible: open,
    removeOnLeave: true
  }), function (_ref) {
    var className = _ref.className;
    return /*#__PURE__*/react.createElement("div", {
      style: {
        zIndex: zIndex
      },
      className: classnames_default()("".concat(prefixCls, "-mask"), className)
    });
  });
}
;// ./node_modules/@rc-component/trigger/es/Popup/PopupContent.js

var PopupContent = /*#__PURE__*/react.memo(function (_ref) {
  var children = _ref.children;
  return children;
}, function (_, next) {
  return next.cache;
});
if (false) {}
/* harmony default export */ const Popup_PopupContent = (PopupContent);
;// ./node_modules/@rc-component/trigger/es/Popup/index.js












var Popup = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var popup = props.popup,
    className = props.className,
    prefixCls = props.prefixCls,
    style = props.style,
    target = props.target,
    _onVisibleChanged = props.onVisibleChanged,
    open = props.open,
    keepDom = props.keepDom,
    fresh = props.fresh,
    onClick = props.onClick,
    mask = props.mask,
    arrow = props.arrow,
    arrowPos = props.arrowPos,
    align = props.align,
    motion = props.motion,
    maskMotion = props.maskMotion,
    forceRender = props.forceRender,
    getPopupContainer = props.getPopupContainer,
    autoDestroy = props.autoDestroy,
    Portal = props.portal,
    zIndex = props.zIndex,
    onMouseEnter = props.onMouseEnter,
    onMouseLeave = props.onMouseLeave,
    onPointerEnter = props.onPointerEnter,
    onPointerDownCapture = props.onPointerDownCapture,
    ready = props.ready,
    offsetX = props.offsetX,
    offsetY = props.offsetY,
    offsetR = props.offsetR,
    offsetB = props.offsetB,
    onAlign = props.onAlign,
    onPrepare = props.onPrepare,
    stretch = props.stretch,
    targetWidth = props.targetWidth,
    targetHeight = props.targetHeight;
  var childNode = typeof popup === 'function' ? popup() : popup;

  // We can not remove holder only when motion finished.
  var isNodeVisible = open || keepDom;

  // ======================= Container ========================
  var getPopupContainerNeedParams = (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.length) > 0;
  var _React$useState = react.useState(!getPopupContainer || !getPopupContainerNeedParams),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    show = _React$useState2[0],
    setShow = _React$useState2[1];

  // Delay to show since `getPopupContainer` need target element
  (0,useLayoutEffect/* default */.A)(function () {
    if (!show && getPopupContainerNeedParams && target) {
      setShow(true);
    }
  }, [show, getPopupContainerNeedParams, target]);

  // ========================= Render =========================
  if (!show) {
    return null;
  }

  // >>>>> Offset
  var AUTO = 'auto';
  var offsetStyle = {
    left: '-1000vw',
    top: '-1000vh',
    right: AUTO,
    bottom: AUTO
  };

  // Set align style
  if (ready || !open) {
    var _experimental;
    var points = align.points;
    var dynamicInset = align.dynamicInset || ((_experimental = align._experimental) === null || _experimental === void 0 ? void 0 : _experimental.dynamicInset);
    var alignRight = dynamicInset && points[0][1] === 'r';
    var alignBottom = dynamicInset && points[0][0] === 'b';
    if (alignRight) {
      offsetStyle.right = offsetR;
      offsetStyle.left = AUTO;
    } else {
      offsetStyle.left = offsetX;
      offsetStyle.right = AUTO;
    }
    if (alignBottom) {
      offsetStyle.bottom = offsetB;
      offsetStyle.top = AUTO;
    } else {
      offsetStyle.top = offsetY;
      offsetStyle.bottom = AUTO;
    }
  }

  // >>>>> Misc
  var miscStyle = {};
  if (stretch) {
    if (stretch.includes('height') && targetHeight) {
      miscStyle.height = targetHeight;
    } else if (stretch.includes('minHeight') && targetHeight) {
      miscStyle.minHeight = targetHeight;
    }
    if (stretch.includes('width') && targetWidth) {
      miscStyle.width = targetWidth;
    } else if (stretch.includes('minWidth') && targetWidth) {
      miscStyle.minWidth = targetWidth;
    }
  }
  if (!open) {
    miscStyle.pointerEvents = 'none';
  }
  return /*#__PURE__*/react.createElement(Portal, {
    open: forceRender || isNodeVisible,
    getContainer: getPopupContainer && function () {
      return getPopupContainer(target);
    },
    autoDestroy: autoDestroy
  }, /*#__PURE__*/react.createElement(Mask, {
    prefixCls: prefixCls,
    open: open,
    zIndex: zIndex,
    mask: mask,
    motion: maskMotion
  }), /*#__PURE__*/react.createElement(rc_resize_observer_es/* default */.A, {
    onResize: onAlign,
    disabled: !open
  }, function (resizeObserverRef) {
    return /*#__PURE__*/react.createElement(rc_motion_es/* default */.Ay, (0,esm_extends/* default */.A)({
      motionAppear: true,
      motionEnter: true,
      motionLeave: true,
      removeOnLeave: false,
      forceRender: forceRender,
      leavedClassName: "".concat(prefixCls, "-hidden")
    }, motion, {
      onAppearPrepare: onPrepare,
      onEnterPrepare: onPrepare,
      visible: open,
      onVisibleChanged: function onVisibleChanged(nextVisible) {
        var _motion$onVisibleChan;
        motion === null || motion === void 0 || (_motion$onVisibleChan = motion.onVisibleChanged) === null || _motion$onVisibleChan === void 0 || _motion$onVisibleChan.call(motion, nextVisible);
        _onVisibleChanged(nextVisible);
      }
    }), function (_ref, motionRef) {
      var motionClassName = _ref.className,
        motionStyle = _ref.style;
      var cls = classnames_default()(prefixCls, motionClassName, className);
      return /*#__PURE__*/react.createElement("div", {
        ref: (0,es_ref/* composeRef */.K4)(resizeObserverRef, ref, motionRef),
        className: cls,
        style: (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({
          '--arrow-x': "".concat(arrowPos.x || 0, "px"),
          '--arrow-y': "".concat(arrowPos.y || 0, "px")
        }, offsetStyle), miscStyle), motionStyle), {}, {
          boxSizing: 'border-box',
          zIndex: zIndex
        }, style),
        onMouseEnter: onMouseEnter,
        onMouseLeave: onMouseLeave,
        onPointerEnter: onPointerEnter,
        onClick: onClick,
        onPointerDownCapture: onPointerDownCapture
      }, arrow && /*#__PURE__*/react.createElement(Arrow, {
        prefixCls: prefixCls,
        arrow: arrow,
        arrowPos: arrowPos,
        align: align
      }), /*#__PURE__*/react.createElement(Popup_PopupContent, {
        cache: !open && !fresh
      }, childNode));
    });
  }));
});
if (false) {}
/* harmony default export */ const es_Popup = (Popup);
;// ./node_modules/@rc-component/trigger/es/TriggerWrapper.js


var TriggerWrapper = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var children = props.children,
    getTriggerDOMNode = props.getTriggerDOMNode;
  var canUseRef = (0,es_ref/* supportRef */.f3)(children);

  // When use `getTriggerDOMNode`, we should do additional work to get the real dom
  var setRef = react.useCallback(function (node) {
    (0,es_ref/* fillRef */.Xf)(ref, getTriggerDOMNode ? getTriggerDOMNode(node) : node);
  }, [getTriggerDOMNode]);
  var mergedRef = (0,es_ref/* useComposeRef */.xK)(setRef, (0,es_ref/* getNodeRef */.A9)(children));
  return canUseRef ? /*#__PURE__*/react.cloneElement(children, {
    ref: mergedRef
  }) : children;
});
if (false) {}
/* harmony default export */ const es_TriggerWrapper = (TriggerWrapper);
;// ./node_modules/@rc-component/trigger/es/context.js

var TriggerContext = /*#__PURE__*/react.createContext(null);
/* harmony default export */ const es_context = (TriggerContext);
;// ./node_modules/@rc-component/trigger/es/hooks/useAction.js

function toArray(val) {
  return val ? Array.isArray(val) ? val : [val] : [];
}
function useAction(mobile, action, showAction, hideAction) {
  return react.useMemo(function () {
    var mergedShowAction = toArray(showAction !== null && showAction !== void 0 ? showAction : action);
    var mergedHideAction = toArray(hideAction !== null && hideAction !== void 0 ? hideAction : action);
    var showActionSet = new Set(mergedShowAction);
    var hideActionSet = new Set(mergedHideAction);
    if (mobile) {
      if (showActionSet.has('hover')) {
        showActionSet.delete('hover');
        showActionSet.add('click');
      }
      if (hideActionSet.has('hover')) {
        hideActionSet.delete('hover');
        hideActionSet.add('click');
      }
    }
    return [showActionSet, hideActionSet];
  }, [mobile, action, showAction, hideAction]);
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/isVisible.js
var isVisible = __webpack_require__(42467);
;// ./node_modules/@rc-component/trigger/es/util.js

function isPointsEq() {
  var a1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var a2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  var isAlignPoint = arguments.length > 2 ? arguments[2] : undefined;
  if (isAlignPoint) {
    return a1[0] === a2[0];
  }
  return a1[0] === a2[0] && a1[1] === a2[1];
}
function getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {
  var points = align.points;
  var placements = Object.keys(builtinPlacements);
  for (var i = 0; i < placements.length; i += 1) {
    var _builtinPlacements$pl;
    var placement = placements[i];
    if (isPointsEq((_builtinPlacements$pl = builtinPlacements[placement]) === null || _builtinPlacements$pl === void 0 ? void 0 : _builtinPlacements$pl.points, points, isAlignPoint)) {
      return "".concat(prefixCls, "-placement-").concat(placement);
    }
  }
  return '';
}

/** @deprecated We should not use this if we can refactor all deps */
function getMotion(prefixCls, motion, animation, transitionName) {
  if (motion) {
    return motion;
  }
  if (animation) {
    return {
      motionName: "".concat(prefixCls, "-").concat(animation)
    };
  }
  if (transitionName) {
    return {
      motionName: transitionName
    };
  }
  return null;
}
function getWin(ele) {
  return ele.ownerDocument.defaultView;
}

/**
 * Get all the scrollable parent elements of the element
 * @param ele       The element to be detected
 * @param areaOnly  Only return the parent which will cut visible area
 */
function collectScroller(ele) {
  var scrollerList = [];
  var current = ele === null || ele === void 0 ? void 0 : ele.parentElement;
  var scrollStyle = ['hidden', 'scroll', 'clip', 'auto'];
  while (current) {
    var _getWin$getComputedSt = getWin(current).getComputedStyle(current),
      overflowX = _getWin$getComputedSt.overflowX,
      overflowY = _getWin$getComputedSt.overflowY,
      overflow = _getWin$getComputedSt.overflow;
    if ([overflowX, overflowY, overflow].some(function (o) {
      return scrollStyle.includes(o);
    })) {
      scrollerList.push(current);
    }
    current = current.parentElement;
  }
  return scrollerList;
}
function toNum(num) {
  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
  return Number.isNaN(num) ? defaultValue : num;
}
function getPxValue(val) {
  return toNum(parseFloat(val), 0);
}
/**
 *
 *
 *  **************************************
 *  *              Border                *
 *  *     **************************     *
 *  *     *                  *     *     *
 *  *  B  *                  *  S  *  B  *
 *  *  o  *                  *  c  *  o  *
 *  *  r  *      Content     *  r  *  r  *
 *  *  d  *                  *  o  *  d  *
 *  *  e  *                  *  l  *  e  *
 *  *  r  ********************  l  *  r  *
 *  *     *        Scroll          *     *
 *  *     **************************     *
 *  *              Border                *
 *  **************************************
 *
 */
/**
 * Get visible area of element
 */
function getVisibleArea(initArea, scrollerList) {
  var visibleArea = (0,objectSpread2/* default */.A)({}, initArea);
  (scrollerList || []).forEach(function (ele) {
    if (ele instanceof HTMLBodyElement || ele instanceof HTMLHtmlElement) {
      return;
    }

    // Skip if static position which will not affect visible area
    var _getWin$getComputedSt2 = getWin(ele).getComputedStyle(ele),
      overflow = _getWin$getComputedSt2.overflow,
      overflowClipMargin = _getWin$getComputedSt2.overflowClipMargin,
      borderTopWidth = _getWin$getComputedSt2.borderTopWidth,
      borderBottomWidth = _getWin$getComputedSt2.borderBottomWidth,
      borderLeftWidth = _getWin$getComputedSt2.borderLeftWidth,
      borderRightWidth = _getWin$getComputedSt2.borderRightWidth;
    var eleRect = ele.getBoundingClientRect();
    var eleOutHeight = ele.offsetHeight,
      eleInnerHeight = ele.clientHeight,
      eleOutWidth = ele.offsetWidth,
      eleInnerWidth = ele.clientWidth;
    var borderTopNum = getPxValue(borderTopWidth);
    var borderBottomNum = getPxValue(borderBottomWidth);
    var borderLeftNum = getPxValue(borderLeftWidth);
    var borderRightNum = getPxValue(borderRightWidth);
    var scaleX = toNum(Math.round(eleRect.width / eleOutWidth * 1000) / 1000);
    var scaleY = toNum(Math.round(eleRect.height / eleOutHeight * 1000) / 1000);

    // Original visible area
    var eleScrollWidth = (eleOutWidth - eleInnerWidth - borderLeftNum - borderRightNum) * scaleX;
    var eleScrollHeight = (eleOutHeight - eleInnerHeight - borderTopNum - borderBottomNum) * scaleY;

    // Cut border size
    var scaledBorderTopWidth = borderTopNum * scaleY;
    var scaledBorderBottomWidth = borderBottomNum * scaleY;
    var scaledBorderLeftWidth = borderLeftNum * scaleX;
    var scaledBorderRightWidth = borderRightNum * scaleX;

    // Clip margin
    var clipMarginWidth = 0;
    var clipMarginHeight = 0;
    if (overflow === 'clip') {
      var clipNum = getPxValue(overflowClipMargin);
      clipMarginWidth = clipNum * scaleX;
      clipMarginHeight = clipNum * scaleY;
    }

    // Region
    var eleLeft = eleRect.x + scaledBorderLeftWidth - clipMarginWidth;
    var eleTop = eleRect.y + scaledBorderTopWidth - clipMarginHeight;
    var eleRight = eleLeft + eleRect.width + 2 * clipMarginWidth - scaledBorderLeftWidth - scaledBorderRightWidth - eleScrollWidth;
    var eleBottom = eleTop + eleRect.height + 2 * clipMarginHeight - scaledBorderTopWidth - scaledBorderBottomWidth - eleScrollHeight;
    visibleArea.left = Math.max(visibleArea.left, eleLeft);
    visibleArea.top = Math.max(visibleArea.top, eleTop);
    visibleArea.right = Math.min(visibleArea.right, eleRight);
    visibleArea.bottom = Math.min(visibleArea.bottom, eleBottom);
  });
  return visibleArea;
}
;// ./node_modules/@rc-component/trigger/es/hooks/useAlign.js








function getUnitOffset(size) {
  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
  var offsetStr = "".concat(offset);
  var cells = offsetStr.match(/^(.*)\%$/);
  if (cells) {
    return size * (parseFloat(cells[1]) / 100);
  }
  return parseFloat(offsetStr);
}
function getNumberOffset(rect, offset) {
  var _ref = offset || [],
    _ref2 = (0,slicedToArray/* default */.A)(_ref, 2),
    offsetX = _ref2[0],
    offsetY = _ref2[1];
  return [getUnitOffset(rect.width, offsetX), getUnitOffset(rect.height, offsetY)];
}
function splitPoints() {
  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  return [points[0], points[1]];
}
function getAlignPoint(rect, points) {
  var topBottom = points[0];
  var leftRight = points[1];
  var x;
  var y;

  // Top & Bottom
  if (topBottom === 't') {
    y = rect.y;
  } else if (topBottom === 'b') {
    y = rect.y + rect.height;
  } else {
    y = rect.y + rect.height / 2;
  }

  // Left & Right
  if (leftRight === 'l') {
    x = rect.x;
  } else if (leftRight === 'r') {
    x = rect.x + rect.width;
  } else {
    x = rect.x + rect.width / 2;
  }
  return {
    x: x,
    y: y
  };
}
function reversePoints(points, index) {
  var reverseMap = {
    t: 'b',
    b: 't',
    l: 'r',
    r: 'l'
  };
  return points.map(function (point, i) {
    if (i === index) {
      return reverseMap[point] || 'c';
    }
    return point;
  }).join('');
}
function useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {
  var _React$useState = react.useState({
      ready: false,
      offsetX: 0,
      offsetY: 0,
      offsetR: 0,
      offsetB: 0,
      arrowX: 0,
      arrowY: 0,
      scaleX: 1,
      scaleY: 1,
      align: builtinPlacements[placement] || {}
    }),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    offsetInfo = _React$useState2[0],
    setOffsetInfo = _React$useState2[1];
  var alignCountRef = react.useRef(0);
  var scrollerList = react.useMemo(function () {
    if (!popupEle) {
      return [];
    }
    return collectScroller(popupEle);
  }, [popupEle]);

  // ========================= Flip ==========================
  // We will memo flip info.
  // If size change to make flip, it will memo the flip info and use it in next align.
  var prevFlipRef = react.useRef({});
  var resetFlipCache = function resetFlipCache() {
    prevFlipRef.current = {};
  };
  if (!open) {
    resetFlipCache();
  }

  // ========================= Align =========================
  var onAlign = (0,useEvent/* default */.A)(function () {
    if (popupEle && target && open) {
      var _popupElement$parentE, _popupRect$x, _popupRect$y, _popupElement$parentE2;
      var popupElement = popupEle;
      var doc = popupElement.ownerDocument;
      var win = getWin(popupElement);
      var _win$getComputedStyle = win.getComputedStyle(popupElement),
        width = _win$getComputedStyle.width,
        height = _win$getComputedStyle.height,
        popupPosition = _win$getComputedStyle.position;
      var originLeft = popupElement.style.left;
      var originTop = popupElement.style.top;
      var originRight = popupElement.style.right;
      var originBottom = popupElement.style.bottom;
      var originOverflow = popupElement.style.overflow;

      // Placement
      var placementInfo = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, builtinPlacements[placement]), popupAlign);

      // placeholder element
      var placeholderElement = doc.createElement('div');
      (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 || _popupElement$parentE.appendChild(placeholderElement);
      placeholderElement.style.left = "".concat(popupElement.offsetLeft, "px");
      placeholderElement.style.top = "".concat(popupElement.offsetTop, "px");
      placeholderElement.style.position = popupPosition;
      placeholderElement.style.height = "".concat(popupElement.offsetHeight, "px");
      placeholderElement.style.width = "".concat(popupElement.offsetWidth, "px");

      // Reset first
      popupElement.style.left = '0';
      popupElement.style.top = '0';
      popupElement.style.right = 'auto';
      popupElement.style.bottom = 'auto';
      popupElement.style.overflow = 'hidden';

      // Calculate align style, we should consider `transform` case
      var targetRect;
      if (Array.isArray(target)) {
        targetRect = {
          x: target[0],
          y: target[1],
          width: 0,
          height: 0
        };
      } else {
        var _rect$x, _rect$y;
        var rect = target.getBoundingClientRect();
        rect.x = (_rect$x = rect.x) !== null && _rect$x !== void 0 ? _rect$x : rect.left;
        rect.y = (_rect$y = rect.y) !== null && _rect$y !== void 0 ? _rect$y : rect.top;
        targetRect = {
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height
        };
      }
      var popupRect = popupElement.getBoundingClientRect();
      popupRect.x = (_popupRect$x = popupRect.x) !== null && _popupRect$x !== void 0 ? _popupRect$x : popupRect.left;
      popupRect.y = (_popupRect$y = popupRect.y) !== null && _popupRect$y !== void 0 ? _popupRect$y : popupRect.top;
      var _doc$documentElement = doc.documentElement,
        clientWidth = _doc$documentElement.clientWidth,
        clientHeight = _doc$documentElement.clientHeight,
        scrollWidth = _doc$documentElement.scrollWidth,
        scrollHeight = _doc$documentElement.scrollHeight,
        scrollTop = _doc$documentElement.scrollTop,
        scrollLeft = _doc$documentElement.scrollLeft;
      var popupHeight = popupRect.height;
      var popupWidth = popupRect.width;
      var targetHeight = targetRect.height;
      var targetWidth = targetRect.width;

      // Get bounding of visible area
      var visibleRegion = {
        left: 0,
        top: 0,
        right: clientWidth,
        bottom: clientHeight
      };
      var scrollRegion = {
        left: -scrollLeft,
        top: -scrollTop,
        right: scrollWidth - scrollLeft,
        bottom: scrollHeight - scrollTop
      };
      var htmlRegion = placementInfo.htmlRegion;
      var VISIBLE = 'visible';
      var VISIBLE_FIRST = 'visibleFirst';
      if (htmlRegion !== 'scroll' && htmlRegion !== VISIBLE_FIRST) {
        htmlRegion = VISIBLE;
      }
      var isVisibleFirst = htmlRegion === VISIBLE_FIRST;
      var scrollRegionArea = getVisibleArea(scrollRegion, scrollerList);
      var visibleRegionArea = getVisibleArea(visibleRegion, scrollerList);
      var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;

      // When set to `visibleFirst`,
      // the check `adjust` logic will use `visibleRegion` for check first.
      var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;

      // Record right & bottom align data
      popupElement.style.left = 'auto';
      popupElement.style.top = 'auto';
      popupElement.style.right = '0';
      popupElement.style.bottom = '0';
      var popupMirrorRect = popupElement.getBoundingClientRect();

      // Reset back
      popupElement.style.left = originLeft;
      popupElement.style.top = originTop;
      popupElement.style.right = originRight;
      popupElement.style.bottom = originBottom;
      popupElement.style.overflow = originOverflow;
      (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 || _popupElement$parentE2.removeChild(placeholderElement);

      // Calculate scale
      var _scaleX = toNum(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);
      var _scaleY = toNum(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);

      // No need to align since it's not visible in view
      if (_scaleX === 0 || _scaleY === 0 || (0,findDOMNode/* isDOM */.fk)(target) && !(0,isVisible/* default */.A)(target)) {
        return;
      }

      // Offset
      var offset = placementInfo.offset,
        targetOffset = placementInfo.targetOffset;
      var _getNumberOffset = getNumberOffset(popupRect, offset),
        _getNumberOffset2 = (0,slicedToArray/* default */.A)(_getNumberOffset, 2),
        popupOffsetX = _getNumberOffset2[0],
        popupOffsetY = _getNumberOffset2[1];
      var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset),
        _getNumberOffset4 = (0,slicedToArray/* default */.A)(_getNumberOffset3, 2),
        targetOffsetX = _getNumberOffset4[0],
        targetOffsetY = _getNumberOffset4[1];
      targetRect.x -= targetOffsetX;
      targetRect.y -= targetOffsetY;

      // Points
      var _ref3 = placementInfo.points || [],
        _ref4 = (0,slicedToArray/* default */.A)(_ref3, 2),
        popupPoint = _ref4[0],
        targetPoint = _ref4[1];
      var targetPoints = splitPoints(targetPoint);
      var popupPoints = splitPoints(popupPoint);
      var targetAlignPoint = getAlignPoint(targetRect, targetPoints);
      var popupAlignPoint = getAlignPoint(popupRect, popupPoints);

      // Real align info may not same as origin one
      var nextAlignInfo = (0,objectSpread2/* default */.A)({}, placementInfo);

      // Next Offset
      var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;
      var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;

      // ============== Intersection ===============
      // Get area by position. Used for check if flip area is better
      function getIntersectionVisibleArea(offsetX, offsetY) {
        var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;
        var l = popupRect.x + offsetX;
        var t = popupRect.y + offsetY;
        var r = l + popupWidth;
        var b = t + popupHeight;
        var visibleL = Math.max(l, area.left);
        var visibleT = Math.max(t, area.top);
        var visibleR = Math.min(r, area.right);
        var visibleB = Math.min(b, area.bottom);
        return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));
      }
      var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);

      // As `visibleFirst`, we prepare this for check
      var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);

      // ========================== Overflow ===========================
      var targetAlignPointTL = getAlignPoint(targetRect, ['t', 'l']);
      var popupAlignPointTL = getAlignPoint(popupRect, ['t', 'l']);
      var targetAlignPointBR = getAlignPoint(targetRect, ['b', 'r']);
      var popupAlignPointBR = getAlignPoint(popupRect, ['b', 'r']);
      var overflow = placementInfo.overflow || {};
      var adjustX = overflow.adjustX,
        adjustY = overflow.adjustY,
        shiftX = overflow.shiftX,
        shiftY = overflow.shiftY;
      var supportAdjust = function supportAdjust(val) {
        if (typeof val === 'boolean') {
          return val;
        }
        return val >= 0;
      };

      // Prepare position
      var nextPopupY;
      var nextPopupBottom;
      var nextPopupX;
      var nextPopupRight;
      function syncNextPopupPosition() {
        nextPopupY = popupRect.y + nextOffsetY;
        nextPopupBottom = nextPopupY + popupHeight;
        nextPopupX = popupRect.x + nextOffsetX;
        nextPopupRight = nextPopupX + popupWidth;
      }
      syncNextPopupPosition();

      // >>>>>>>>>> Top & Bottom
      var needAdjustY = supportAdjust(adjustY);
      var sameTB = popupPoints[0] === targetPoints[0];

      // Bottom to Top
      if (needAdjustY && popupPoints[0] === 't' && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {
        var tmpNextOffsetY = nextOffsetY;
        if (sameTB) {
          tmpNextOffsetY -= popupHeight - targetHeight;
        } else {
          tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;
        }
        var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);
        var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);
        if (
        // Of course use larger one
        newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||
        // Choose recommend one
        newVisibleRecommendArea >= originIntersectionRecommendArea)) {
          prevFlipRef.current.bt = true;
          nextOffsetY = tmpNextOffsetY;
          popupOffsetY = -popupOffsetY;
          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];
        } else {
          prevFlipRef.current.bt = false;
        }
      }

      // Top to Bottom
      if (needAdjustY && popupPoints[0] === 'b' && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {
        var _tmpNextOffsetY = nextOffsetY;
        if (sameTB) {
          _tmpNextOffsetY += popupHeight - targetHeight;
        } else {
          _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;
        }
        var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);
        var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);
        if (
        // Of course use larger one
        _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||
        // Choose recommend one
        _newVisibleRecommendArea >= originIntersectionRecommendArea)) {
          prevFlipRef.current.tb = true;
          nextOffsetY = _tmpNextOffsetY;
          popupOffsetY = -popupOffsetY;
          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];
        } else {
          prevFlipRef.current.tb = false;
        }
      }

      // >>>>>>>>>> Left & Right
      var needAdjustX = supportAdjust(adjustX);

      // >>>>> Flip
      var sameLR = popupPoints[1] === targetPoints[1];

      // Right to Left
      if (needAdjustX && popupPoints[1] === 'l' && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {
        var tmpNextOffsetX = nextOffsetX;
        if (sameLR) {
          tmpNextOffsetX -= popupWidth - targetWidth;
        } else {
          tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;
        }
        var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);
        var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);
        if (
        // Of course use larger one
        _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst ||
        // Choose recommend one
        _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {
          prevFlipRef.current.rl = true;
          nextOffsetX = tmpNextOffsetX;
          popupOffsetX = -popupOffsetX;
          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];
        } else {
          prevFlipRef.current.rl = false;
        }
      }

      // Left to Right
      if (needAdjustX && popupPoints[1] === 'r' && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {
        var _tmpNextOffsetX = nextOffsetX;
        if (sameLR) {
          _tmpNextOffsetX += popupWidth - targetWidth;
        } else {
          _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;
        }
        var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);
        var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);
        if (
        // Of course use larger one
        _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst ||
        // Choose recommend one
        _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {
          prevFlipRef.current.lr = true;
          nextOffsetX = _tmpNextOffsetX;
          popupOffsetX = -popupOffsetX;
          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];
        } else {
          prevFlipRef.current.lr = false;
        }
      }

      // ============================ Shift ============================
      syncNextPopupPosition();
      var numShiftX = shiftX === true ? 0 : shiftX;
      if (typeof numShiftX === 'number') {
        // Left
        if (nextPopupX < visibleRegionArea.left) {
          nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;
          if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {
            nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;
          }
        }

        // Right
        if (nextPopupRight > visibleRegionArea.right) {
          nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;
          if (targetRect.x > visibleRegionArea.right - numShiftX) {
            nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;
          }
        }
      }
      var numShiftY = shiftY === true ? 0 : shiftY;
      if (typeof numShiftY === 'number') {
        // Top
        if (nextPopupY < visibleRegionArea.top) {
          nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;

          // When target if far away from visible area
          // Stop shift
          if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {
            nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;
          }
        }

        // Bottom
        if (nextPopupBottom > visibleRegionArea.bottom) {
          nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;
          if (targetRect.y > visibleRegionArea.bottom - numShiftY) {
            nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;
          }
        }
      }

      // ============================ Arrow ============================
      // Arrow center align
      var popupLeft = popupRect.x + nextOffsetX;
      var popupRight = popupLeft + popupWidth;
      var popupTop = popupRect.y + nextOffsetY;
      var popupBottom = popupTop + popupHeight;
      var targetLeft = targetRect.x;
      var targetRight = targetLeft + targetWidth;
      var targetTop = targetRect.y;
      var targetBottom = targetTop + targetHeight;
      var maxLeft = Math.max(popupLeft, targetLeft);
      var minRight = Math.min(popupRight, targetRight);
      var xCenter = (maxLeft + minRight) / 2;
      var nextArrowX = xCenter - popupLeft;
      var maxTop = Math.max(popupTop, targetTop);
      var minBottom = Math.min(popupBottom, targetBottom);
      var yCenter = (maxTop + minBottom) / 2;
      var nextArrowY = yCenter - popupTop;
      onPopupAlign === null || onPopupAlign === void 0 || onPopupAlign(popupEle, nextAlignInfo);

      // Additional calculate right & bottom position
      var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);
      var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);
      if (_scaleX === 1) {
        nextOffsetX = Math.round(nextOffsetX);
        offsetX4Right = Math.round(offsetX4Right);
      }
      if (_scaleY === 1) {
        nextOffsetY = Math.round(nextOffsetY);
        offsetY4Bottom = Math.round(offsetY4Bottom);
      }
      var nextOffsetInfo = {
        ready: true,
        offsetX: nextOffsetX / _scaleX,
        offsetY: nextOffsetY / _scaleY,
        offsetR: offsetX4Right / _scaleX,
        offsetB: offsetY4Bottom / _scaleY,
        arrowX: nextArrowX / _scaleX,
        arrowY: nextArrowY / _scaleY,
        scaleX: _scaleX,
        scaleY: _scaleY,
        align: nextAlignInfo
      };
      setOffsetInfo(nextOffsetInfo);
    }
  });
  var triggerAlign = function triggerAlign() {
    alignCountRef.current += 1;
    var id = alignCountRef.current;

    // Merge all align requirement into one frame
    Promise.resolve().then(function () {
      if (alignCountRef.current === id) {
        onAlign();
      }
    });
  };

  // Reset ready status when placement & open changed
  var resetReady = function resetReady() {
    setOffsetInfo(function (ori) {
      return (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, ori), {}, {
        ready: false
      });
    });
  };
  (0,useLayoutEffect/* default */.A)(resetReady, [placement]);
  (0,useLayoutEffect/* default */.A)(function () {
    if (!open) {
      resetReady();
    }
  }, [open]);
  return [offsetInfo.ready, offsetInfo.offsetX, offsetInfo.offsetY, offsetInfo.offsetR, offsetInfo.offsetB, offsetInfo.arrowX, offsetInfo.arrowY, offsetInfo.scaleX, offsetInfo.scaleY, offsetInfo.align, triggerAlign];
}
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
;// ./node_modules/@rc-component/trigger/es/hooks/useWatch.js



function useWatch(open, target, popup, onAlign, onScroll) {
  (0,useLayoutEffect/* default */.A)(function () {
    if (open && target && popup) {
      var targetElement = target;
      var popupElement = popup;
      var targetScrollList = collectScroller(targetElement);
      var popupScrollList = collectScroller(popupElement);
      var win = getWin(popupElement);
      var mergedList = new Set([win].concat((0,toConsumableArray/* default */.A)(targetScrollList), (0,toConsumableArray/* default */.A)(popupScrollList)));
      function notifyScroll() {
        onAlign();
        onScroll();
      }
      mergedList.forEach(function (scroller) {
        scroller.addEventListener('scroll', notifyScroll, {
          passive: true
        });
      });
      win.addEventListener('resize', notifyScroll, {
        passive: true
      });

      // First time always do align
      onAlign();
      return function () {
        mergedList.forEach(function (scroller) {
          scroller.removeEventListener('scroll', notifyScroll);
          win.removeEventListener('resize', notifyScroll);
        });
      };
    }
  }, [open, target, popup]);
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(68210);
;// ./node_modules/@rc-component/trigger/es/hooks/useWinClick.js




function useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {
  var openRef = react.useRef(open);
  openRef.current = open;
  var popupPointerDownRef = react.useRef(false);

  // Click to hide is special action since click popup element should not hide
  react.useEffect(function () {
    if (clickToHide && popupEle && (!mask || maskClosable)) {
      var onPointerDown = function onPointerDown() {
        popupPointerDownRef.current = false;
      };
      var onTriggerClose = function onTriggerClose(e) {
        var _e$composedPath;
        if (openRef.current && !inPopupOrChild(((_e$composedPath = e.composedPath) === null || _e$composedPath === void 0 || (_e$composedPath = _e$composedPath.call(e)) === null || _e$composedPath === void 0 ? void 0 : _e$composedPath[0]) || e.target) && !popupPointerDownRef.current) {
          triggerOpen(false);
        }
      };
      var win = getWin(popupEle);
      win.addEventListener('pointerdown', onPointerDown, true);
      win.addEventListener('mousedown', onTriggerClose, true);
      win.addEventListener('contextmenu', onTriggerClose, true);

      // shadow root
      var targetShadowRoot = (0,shadow/* getShadowRoot */.j)(targetEle);
      if (targetShadowRoot) {
        targetShadowRoot.addEventListener('mousedown', onTriggerClose, true);
        targetShadowRoot.addEventListener('contextmenu', onTriggerClose, true);
      }

      // Warning if target and popup not in same root
      if (false) { var popupRoot, targetRoot, _targetEle$getRootNod, _popupEle$getRootNode; }
      return function () {
        win.removeEventListener('pointerdown', onPointerDown, true);
        win.removeEventListener('mousedown', onTriggerClose, true);
        win.removeEventListener('contextmenu', onTriggerClose, true);
        if (targetShadowRoot) {
          targetShadowRoot.removeEventListener('mousedown', onTriggerClose, true);
          targetShadowRoot.removeEventListener('contextmenu', onTriggerClose, true);
        }
      };
    }
  }, [clickToHide, targetEle, popupEle, mask, maskClosable]);
  function onPopupPointerDown() {
    popupPointerDownRef.current = true;
  }
  return onPopupPointerDown;
}
;// ./node_modules/@rc-component/trigger/es/index.js



var _excluded = ["prefixCls", "children", "action", "showAction", "hideAction", "popupVisible", "defaultPopupVisible", "onPopupVisibleChange", "afterPopupVisibleChange", "mouseEnterDelay", "mouseLeaveDelay", "focusDelay", "blurDelay", "mask", "maskClosable", "getPopupContainer", "forceRender", "autoDestroy", "destroyPopupOnHide", "popup", "popupClassName", "popupStyle", "popupPlacement", "builtinPlacements", "popupAlign", "zIndex", "stretch", "getPopupClassNameFromAlign", "fresh", "alignPoint", "onPopupClick", "onPopupAlign", "arrow", "popupMotion", "maskMotion", "popupTransitionName", "popupAnimation", "maskTransitionName", "maskAnimation", "className", "getTriggerDOMNode"];



















// Removed Props List
// Seems this can be auto
// getDocument?: (element?: HTMLElement) => Document;

// New version will not wrap popup with `rc-trigger-popup-content` when multiple children

function generateTrigger() {
  var PortalComponent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : es/* default */.A;
  var Trigger = /*#__PURE__*/react.forwardRef(function (props, ref) {
    var _props$prefixCls = props.prefixCls,
      prefixCls = _props$prefixCls === void 0 ? 'rc-trigger-popup' : _props$prefixCls,
      children = props.children,
      _props$action = props.action,
      action = _props$action === void 0 ? 'hover' : _props$action,
      showAction = props.showAction,
      hideAction = props.hideAction,
      popupVisible = props.popupVisible,
      defaultPopupVisible = props.defaultPopupVisible,
      onPopupVisibleChange = props.onPopupVisibleChange,
      afterPopupVisibleChange = props.afterPopupVisibleChange,
      mouseEnterDelay = props.mouseEnterDelay,
      _props$mouseLeaveDela = props.mouseLeaveDelay,
      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,
      focusDelay = props.focusDelay,
      blurDelay = props.blurDelay,
      mask = props.mask,
      _props$maskClosable = props.maskClosable,
      maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,
      getPopupContainer = props.getPopupContainer,
      forceRender = props.forceRender,
      autoDestroy = props.autoDestroy,
      destroyPopupOnHide = props.destroyPopupOnHide,
      popup = props.popup,
      popupClassName = props.popupClassName,
      popupStyle = props.popupStyle,
      popupPlacement = props.popupPlacement,
      _props$builtinPlaceme = props.builtinPlacements,
      builtinPlacements = _props$builtinPlaceme === void 0 ? {} : _props$builtinPlaceme,
      popupAlign = props.popupAlign,
      zIndex = props.zIndex,
      stretch = props.stretch,
      getPopupClassNameFromAlign = props.getPopupClassNameFromAlign,
      fresh = props.fresh,
      alignPoint = props.alignPoint,
      onPopupClick = props.onPopupClick,
      onPopupAlign = props.onPopupAlign,
      arrow = props.arrow,
      popupMotion = props.popupMotion,
      maskMotion = props.maskMotion,
      popupTransitionName = props.popupTransitionName,
      popupAnimation = props.popupAnimation,
      maskTransitionName = props.maskTransitionName,
      maskAnimation = props.maskAnimation,
      className = props.className,
      getTriggerDOMNode = props.getTriggerDOMNode,
      restProps = (0,objectWithoutProperties/* default */.A)(props, _excluded);
    var mergedAutoDestroy = autoDestroy || destroyPopupOnHide || false;

    // =========================== Mobile ===========================
    var _React$useState = react.useState(false),
      _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
      mobile = _React$useState2[0],
      setMobile = _React$useState2[1];
    (0,useLayoutEffect/* default */.A)(function () {
      setMobile((0,isMobile/* default */.A)());
    }, []);

    // ========================== Context ===========================
    var subPopupElements = react.useRef({});
    var parentContext = react.useContext(es_context);
    var context = react.useMemo(function () {
      return {
        registerSubPopup: function registerSubPopup(id, subPopupEle) {
          subPopupElements.current[id] = subPopupEle;
          parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, subPopupEle);
        }
      };
    }, [parentContext]);

    // =========================== Popup ============================
    var id = (0,useId/* default */.A)();
    var _React$useState3 = react.useState(null),
      _React$useState4 = (0,slicedToArray/* default */.A)(_React$useState3, 2),
      popupEle = _React$useState4[0],
      setPopupEle = _React$useState4[1];

    // Used for forwardRef popup. Not use internal
    var externalPopupRef = react.useRef(null);
    var setPopupRef = (0,useEvent/* default */.A)(function (node) {
      externalPopupRef.current = node;
      if ((0,findDOMNode/* isDOM */.fk)(node) && popupEle !== node) {
        setPopupEle(node);
      }
      parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, node);
    });

    // =========================== Target ===========================
    // Use state to control here since `useRef` update not trigger render
    var _React$useState5 = react.useState(null),
      _React$useState6 = (0,slicedToArray/* default */.A)(_React$useState5, 2),
      targetEle = _React$useState6[0],
      setTargetEle = _React$useState6[1];

    // Used for forwardRef target. Not use internal
    var externalForwardRef = react.useRef(null);
    var setTargetRef = (0,useEvent/* default */.A)(function (node) {
      if ((0,findDOMNode/* isDOM */.fk)(node) && targetEle !== node) {
        setTargetEle(node);
        externalForwardRef.current = node;
      }
    });

    // ========================== Children ==========================
    var child = react.Children.only(children);
    var originChildProps = (child === null || child === void 0 ? void 0 : child.props) || {};
    var cloneProps = {};
    var inPopupOrChild = (0,useEvent/* default */.A)(function (ele) {
      var _getShadowRoot, _getShadowRoot2;
      var childDOM = targetEle;
      return (childDOM === null || childDOM === void 0 ? void 0 : childDOM.contains(ele)) || ((_getShadowRoot = (0,shadow/* getShadowRoot */.j)(childDOM)) === null || _getShadowRoot === void 0 ? void 0 : _getShadowRoot.host) === ele || ele === childDOM || (popupEle === null || popupEle === void 0 ? void 0 : popupEle.contains(ele)) || ((_getShadowRoot2 = (0,shadow/* getShadowRoot */.j)(popupEle)) === null || _getShadowRoot2 === void 0 ? void 0 : _getShadowRoot2.host) === ele || ele === popupEle || Object.values(subPopupElements.current).some(function (subPopupEle) {
        return (subPopupEle === null || subPopupEle === void 0 ? void 0 : subPopupEle.contains(ele)) || ele === subPopupEle;
      });
    });

    // =========================== Motion ===========================
    var mergePopupMotion = getMotion(prefixCls, popupMotion, popupAnimation, popupTransitionName);
    var mergeMaskMotion = getMotion(prefixCls, maskMotion, maskAnimation, maskTransitionName);

    // ============================ Open ============================
    var _React$useState7 = react.useState(defaultPopupVisible || false),
      _React$useState8 = (0,slicedToArray/* default */.A)(_React$useState7, 2),
      internalOpen = _React$useState8[0],
      setInternalOpen = _React$useState8[1];

    // Render still use props as first priority
    var mergedOpen = popupVisible !== null && popupVisible !== void 0 ? popupVisible : internalOpen;

    // We use effect sync here in case `popupVisible` back to `undefined`
    var setMergedOpen = (0,useEvent/* default */.A)(function (nextOpen) {
      if (popupVisible === undefined) {
        setInternalOpen(nextOpen);
      }
    });
    (0,useLayoutEffect/* default */.A)(function () {
      setInternalOpen(popupVisible || false);
    }, [popupVisible]);
    var openRef = react.useRef(mergedOpen);
    openRef.current = mergedOpen;
    var lastTriggerRef = react.useRef([]);
    lastTriggerRef.current = [];
    var internalTriggerOpen = (0,useEvent/* default */.A)(function (nextOpen) {
      var _lastTriggerRef$curre;
      setMergedOpen(nextOpen);

      // Enter or Pointer will both trigger open state change
      // We only need take one to avoid duplicated change event trigger
      // Use `lastTriggerRef` to record last open type
      if (((_lastTriggerRef$curre = lastTriggerRef.current[lastTriggerRef.current.length - 1]) !== null && _lastTriggerRef$curre !== void 0 ? _lastTriggerRef$curre : mergedOpen) !== nextOpen) {
        lastTriggerRef.current.push(nextOpen);
        onPopupVisibleChange === null || onPopupVisibleChange === void 0 || onPopupVisibleChange(nextOpen);
      }
    });

    // Trigger for delay
    var delayRef = react.useRef();
    var clearDelay = function clearDelay() {
      clearTimeout(delayRef.current);
    };
    var triggerOpen = function triggerOpen(nextOpen) {
      var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
      clearDelay();
      if (delay === 0) {
        internalTriggerOpen(nextOpen);
      } else {
        delayRef.current = setTimeout(function () {
          internalTriggerOpen(nextOpen);
        }, delay * 1000);
      }
    };
    react.useEffect(function () {
      return clearDelay;
    }, []);

    // ========================== Motion ============================
    var _React$useState9 = react.useState(false),
      _React$useState10 = (0,slicedToArray/* default */.A)(_React$useState9, 2),
      inMotion = _React$useState10[0],
      setInMotion = _React$useState10[1];
    (0,useLayoutEffect/* default */.A)(function (firstMount) {
      if (!firstMount || mergedOpen) {
        setInMotion(true);
      }
    }, [mergedOpen]);
    var _React$useState11 = react.useState(null),
      _React$useState12 = (0,slicedToArray/* default */.A)(_React$useState11, 2),
      motionPrepareResolve = _React$useState12[0],
      setMotionPrepareResolve = _React$useState12[1];

    // =========================== Align ============================
    var _React$useState13 = react.useState(null),
      _React$useState14 = (0,slicedToArray/* default */.A)(_React$useState13, 2),
      mousePos = _React$useState14[0],
      setMousePos = _React$useState14[1];
    var setMousePosByEvent = function setMousePosByEvent(event) {
      setMousePos([event.clientX, event.clientY]);
    };
    var _useAlign = useAlign(mergedOpen, popupEle, alignPoint && mousePos !== null ? mousePos : targetEle, popupPlacement, builtinPlacements, popupAlign, onPopupAlign),
      _useAlign2 = (0,slicedToArray/* default */.A)(_useAlign, 11),
      ready = _useAlign2[0],
      offsetX = _useAlign2[1],
      offsetY = _useAlign2[2],
      offsetR = _useAlign2[3],
      offsetB = _useAlign2[4],
      arrowX = _useAlign2[5],
      arrowY = _useAlign2[6],
      scaleX = _useAlign2[7],
      scaleY = _useAlign2[8],
      alignInfo = _useAlign2[9],
      onAlign = _useAlign2[10];
    var _useAction = useAction(mobile, action, showAction, hideAction),
      _useAction2 = (0,slicedToArray/* default */.A)(_useAction, 2),
      showActions = _useAction2[0],
      hideActions = _useAction2[1];
    var clickToShow = showActions.has('click');
    var clickToHide = hideActions.has('click') || hideActions.has('contextMenu');
    var triggerAlign = (0,useEvent/* default */.A)(function () {
      if (!inMotion) {
        onAlign();
      }
    });
    var onScroll = function onScroll() {
      if (openRef.current && alignPoint && clickToHide) {
        triggerOpen(false);
      }
    };
    useWatch(mergedOpen, targetEle, popupEle, triggerAlign, onScroll);
    (0,useLayoutEffect/* default */.A)(function () {
      triggerAlign();
    }, [mousePos, popupPlacement]);

    // When no builtinPlacements and popupAlign changed
    (0,useLayoutEffect/* default */.A)(function () {
      if (mergedOpen && !(builtinPlacements !== null && builtinPlacements !== void 0 && builtinPlacements[popupPlacement])) {
        triggerAlign();
      }
    }, [JSON.stringify(popupAlign)]);
    var alignedClassName = react.useMemo(function () {
      var baseClassName = getAlignPopupClassName(builtinPlacements, prefixCls, alignInfo, alignPoint);
      return classnames_default()(baseClassName, getPopupClassNameFromAlign === null || getPopupClassNameFromAlign === void 0 ? void 0 : getPopupClassNameFromAlign(alignInfo));
    }, [alignInfo, getPopupClassNameFromAlign, builtinPlacements, prefixCls, alignPoint]);

    // ============================ Refs ============================
    react.useImperativeHandle(ref, function () {
      return {
        nativeElement: externalForwardRef.current,
        popupElement: externalPopupRef.current,
        forceAlign: triggerAlign
      };
    });

    // ========================== Stretch ===========================
    var _React$useState15 = react.useState(0),
      _React$useState16 = (0,slicedToArray/* default */.A)(_React$useState15, 2),
      targetWidth = _React$useState16[0],
      setTargetWidth = _React$useState16[1];
    var _React$useState17 = react.useState(0),
      _React$useState18 = (0,slicedToArray/* default */.A)(_React$useState17, 2),
      targetHeight = _React$useState18[0],
      setTargetHeight = _React$useState18[1];
    var syncTargetSize = function syncTargetSize() {
      if (stretch && targetEle) {
        var rect = targetEle.getBoundingClientRect();
        setTargetWidth(rect.width);
        setTargetHeight(rect.height);
      }
    };
    var onTargetResize = function onTargetResize() {
      syncTargetSize();
      triggerAlign();
    };

    // ========================== Motion ============================
    var onVisibleChanged = function onVisibleChanged(visible) {
      setInMotion(false);
      onAlign();
      afterPopupVisibleChange === null || afterPopupVisibleChange === void 0 || afterPopupVisibleChange(visible);
    };

    // We will trigger align when motion is in prepare
    var onPrepare = function onPrepare() {
      return new Promise(function (resolve) {
        syncTargetSize();
        setMotionPrepareResolve(function () {
          return resolve;
        });
      });
    };
    (0,useLayoutEffect/* default */.A)(function () {
      if (motionPrepareResolve) {
        onAlign();
        motionPrepareResolve();
        setMotionPrepareResolve(null);
      }
    }, [motionPrepareResolve]);

    // =========================== Action ===========================
    /**
     * Util wrapper for trigger action
     */
    function wrapperAction(eventName, nextOpen, delay, preEvent) {
      cloneProps[eventName] = function (event) {
        var _originChildProps$eve;
        preEvent === null || preEvent === void 0 || preEvent(event);
        triggerOpen(nextOpen, delay);

        // Pass to origin
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        (_originChildProps$eve = originChildProps[eventName]) === null || _originChildProps$eve === void 0 || _originChildProps$eve.call.apply(_originChildProps$eve, [originChildProps, event].concat(args));
      };
    }

    // ======================= Action: Click ========================
    if (clickToShow || clickToHide) {
      cloneProps.onClick = function (event) {
        var _originChildProps$onC;
        if (openRef.current && clickToHide) {
          triggerOpen(false);
        } else if (!openRef.current && clickToShow) {
          setMousePosByEvent(event);
          triggerOpen(true);
        }

        // Pass to origin
        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
          args[_key2 - 1] = arguments[_key2];
        }
        (_originChildProps$onC = originChildProps.onClick) === null || _originChildProps$onC === void 0 || _originChildProps$onC.call.apply(_originChildProps$onC, [originChildProps, event].concat(args));
      };
    }

    // Click to hide is special action since click popup element should not hide
    var onPopupPointerDown = useWinClick(mergedOpen, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen);

    // ======================= Action: Hover ========================
    var hoverToShow = showActions.has('hover');
    var hoverToHide = hideActions.has('hover');
    var onPopupMouseEnter;
    var onPopupMouseLeave;
    if (hoverToShow) {
      // Compatible with old browser which not support pointer event
      wrapperAction('onMouseEnter', true, mouseEnterDelay, function (event) {
        setMousePosByEvent(event);
      });
      wrapperAction('onPointerEnter', true, mouseEnterDelay, function (event) {
        setMousePosByEvent(event);
      });
      onPopupMouseEnter = function onPopupMouseEnter(event) {
        // Only trigger re-open when popup is visible
        if ((mergedOpen || inMotion) && popupEle !== null && popupEle !== void 0 && popupEle.contains(event.target)) {
          triggerOpen(true, mouseEnterDelay);
        }
      };

      // Align Point
      if (alignPoint) {
        cloneProps.onMouseMove = function (event) {
          var _originChildProps$onM;
          // setMousePosByEvent(event);
          (_originChildProps$onM = originChildProps.onMouseMove) === null || _originChildProps$onM === void 0 || _originChildProps$onM.call(originChildProps, event);
        };
      }
    }
    if (hoverToHide) {
      wrapperAction('onMouseLeave', false, mouseLeaveDelay);
      wrapperAction('onPointerLeave', false, mouseLeaveDelay);
      onPopupMouseLeave = function onPopupMouseLeave() {
        triggerOpen(false, mouseLeaveDelay);
      };
    }

    // ======================= Action: Focus ========================
    if (showActions.has('focus')) {
      wrapperAction('onFocus', true, focusDelay);
    }
    if (hideActions.has('focus')) {
      wrapperAction('onBlur', false, blurDelay);
    }

    // ==================== Action: ContextMenu =====================
    if (showActions.has('contextMenu')) {
      cloneProps.onContextMenu = function (event) {
        var _originChildProps$onC2;
        if (openRef.current && hideActions.has('contextMenu')) {
          triggerOpen(false);
        } else {
          setMousePosByEvent(event);
          triggerOpen(true);
        }
        event.preventDefault();

        // Pass to origin
        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {
          args[_key3 - 1] = arguments[_key3];
        }
        (_originChildProps$onC2 = originChildProps.onContextMenu) === null || _originChildProps$onC2 === void 0 || _originChildProps$onC2.call.apply(_originChildProps$onC2, [originChildProps, event].concat(args));
      };
    }

    // ========================= ClassName ==========================
    if (className) {
      cloneProps.className = classnames_default()(originChildProps.className, className);
    }

    // =========================== Render ===========================
    var mergedChildrenProps = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, originChildProps), cloneProps);

    // Pass props into cloneProps for nest usage
    var passedProps = {};
    var passedEventList = ['onContextMenu', 'onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur'];
    passedEventList.forEach(function (eventName) {
      if (restProps[eventName]) {
        passedProps[eventName] = function () {
          var _mergedChildrenProps$;
          for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
            args[_key4] = arguments[_key4];
          }
          (_mergedChildrenProps$ = mergedChildrenProps[eventName]) === null || _mergedChildrenProps$ === void 0 || _mergedChildrenProps$.call.apply(_mergedChildrenProps$, [mergedChildrenProps].concat(args));
          restProps[eventName].apply(restProps, args);
        };
      }
    });

    // Child Node
    var triggerNode = /*#__PURE__*/react.cloneElement(child, (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, mergedChildrenProps), passedProps));
    var arrowPos = {
      x: arrowX,
      y: arrowY
    };
    var innerArrow = arrow ? (0,objectSpread2/* default */.A)({}, arrow !== true ? arrow : {}) : null;

    // Render
    return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(rc_resize_observer_es/* default */.A, {
      disabled: !mergedOpen,
      ref: setTargetRef,
      onResize: onTargetResize
    }, /*#__PURE__*/react.createElement(es_TriggerWrapper, {
      getTriggerDOMNode: getTriggerDOMNode
    }, triggerNode)), /*#__PURE__*/react.createElement(es_context.Provider, {
      value: context
    }, /*#__PURE__*/react.createElement(es_Popup, {
      portal: PortalComponent,
      ref: setPopupRef,
      prefixCls: prefixCls,
      popup: popup,
      className: classnames_default()(popupClassName, alignedClassName),
      style: popupStyle,
      target: targetEle,
      onMouseEnter: onPopupMouseEnter,
      onMouseLeave: onPopupMouseLeave
      // https://github.com/ant-design/ant-design/issues/43924
      ,
      onPointerEnter: onPopupMouseEnter,
      zIndex: zIndex
      // Open
      ,
      open: mergedOpen,
      keepDom: inMotion,
      fresh: fresh
      // Click
      ,
      onClick: onPopupClick,
      onPointerDownCapture: onPopupPointerDown
      // Mask
      ,
      mask: mask
      // Motion
      ,
      motion: mergePopupMotion,
      maskMotion: mergeMaskMotion,
      onVisibleChanged: onVisibleChanged,
      onPrepare: onPrepare
      // Portal
      ,
      forceRender: forceRender,
      autoDestroy: mergedAutoDestroy,
      getPopupContainer: getPopupContainer
      // Arrow
      ,
      align: alignInfo,
      arrow: innerArrow,
      arrowPos: arrowPos
      // Align
      ,
      ready: ready,
      offsetX: offsetX,
      offsetY: offsetY,
      offsetR: offsetR,
      offsetB: offsetB,
      onAlign: triggerAlign
      // Stretch
      ,
      stretch: stretch,
      targetWidth: targetWidth / scaleX,
      targetHeight: targetHeight / scaleY
    })));
  });
  if (false) {}
  return Trigger;
}
/* harmony default export */ const trigger_es = (generateTrigger(es/* default */.A));

/***/ }),

/***/ 85364:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {


// UNUSED EXPORTS: DEFAULT_BACKGROUND_COLOR, DEFAULT_FRONT_COLOR, DEFAULT_IMG_SCALE, DEFAULT_LEVEL, DEFAULT_MARGIN_SIZE, DEFAULT_MINVERSION, DEFAULT_NEED_MARGIN, DEFAULT_SIZE, ERROR_LEVEL_MAP, QRCodeCanvas, QRCodeSVG, SPEC_MARGIN_SIZE, excavateModules, generatePath, getImageSettings, getMarginSize, isSupportPath2d

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(24765);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
;// ./node_modules/@rc-component/qrcode/es/libs/qrcodegen.js




var _class, _class2;
// Copyright (c) Project Nayuki. (MIT License)
// https://www.nayuki.io/page/qr-code-generator-library

// Modification with code reorder and prettier

// --------------------------------------------

// Appends the given number of low-order bits of the given value
// to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.
function appendBits(val, len, bb) {
  if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError('Value out of range');
  for (var i = len - 1; i >= 0; i-- // Append bit by bit
  ) bb.push(val >>> i & 1);
}

// Returns true iff the i'th bit of x is set to 1.
function getBit(x, i) {
  return (x >>> i & 1) != 0;
}

// Throws an exception if the given condition is false.
function assert(cond) {
  if (!cond) throw new Error('Assertion error');
}

/*---- Public helper enumeration ----*/
/*
 * Describes how a segment's data bits are numbererpreted. Immutable.
 */
var Mode = /*#__PURE__*/function () {
  function Mode(modeBits, numBitsCharCount) {
    (0,classCallCheck/* default */.A)(this, Mode);
    /*-- Constructor and fields --*/
    // The mode indicator bits, which is a unumber4 value (range 0 to 15).
    (0,defineProperty/* default */.A)(this, "modeBits", void 0);
    // Number of character count bits for three different version ranges.
    (0,defineProperty/* default */.A)(this, "numBitsCharCount", void 0);
    this.modeBits = modeBits;
    this.numBitsCharCount = numBitsCharCount;
  }

  /*-- Method --*/

  // (Package-private) Returns the bit width of the character count field for a segment in
  // this mode in a QR Code at the given version number. The result is in the range [0, 16].
  (0,createClass/* default */.A)(Mode, [{
    key: "numCharCountBits",
    value: function numCharCountBits(ver) {
      return this.numBitsCharCount[Math.floor((ver + 7) / 17)];
    }
  }]);
  return Mode;
}();

/*---- Public helper enumeration ----*/

/*
 * The error correction level in a QR Code symbol. Immutable.
 */
_class = Mode;
/*-- Constants --*/
(0,defineProperty/* default */.A)(Mode, "NUMERIC", new _class(0x1, [10, 12, 14]));
(0,defineProperty/* default */.A)(Mode, "ALPHANUMERIC", new _class(0x2, [9, 11, 13]));
(0,defineProperty/* default */.A)(Mode, "BYTE", new _class(0x4, [8, 16, 16]));
(0,defineProperty/* default */.A)(Mode, "KANJI", new _class(0x8, [8, 10, 12]));
(0,defineProperty/* default */.A)(Mode, "ECI", new _class(0x7, [0, 0, 0]));
var Ecc = /*#__PURE__*/(0,createClass/* default */.A)(function Ecc(ordinal, formatBits) {
  (0,classCallCheck/* default */.A)(this, Ecc);
  // The QR Code can tolerate about 30% erroneous codewords
  /*-- Constructor and fields --*/
  // In the range 0 to 3 (unsigned 2-bit numbereger).
  (0,defineProperty/* default */.A)(this, "ordinal", void 0);
  // (Package-private) In the range 0 to 3 (unsigned 2-bit numbereger).
  (0,defineProperty/* default */.A)(this, "formatBits", void 0);
  this.ordinal = ordinal;
  this.formatBits = formatBits;
});

/*
 * A segment of character/binary/control data in a QR Code symbol.
 * Instances of this class are immutable.
 * The mid-level way to create a segment is to take the payload data
 * and call a static factory function such as QrSegment.makeNumeric().
 * The low-level way to create a segment is to custom-make the bit buffer
 * and call the QrSegment() constructor with appropriate values.
 * This segment class imposes no length restrictions, but QR Codes have restrictions.
 * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.
 * Any segment longer than this is meaningless for the purpose of generating QR Codes.
 */
_class2 = Ecc;
/*-- Constants --*/
(0,defineProperty/* default */.A)(Ecc, "LOW", new _class2(0, 1));
// The QR Code can tolerate about  7% erroneous codewords
(0,defineProperty/* default */.A)(Ecc, "MEDIUM", new _class2(1, 0));
// The QR Code can tolerate about 15% erroneous codewords
(0,defineProperty/* default */.A)(Ecc, "QUARTILE", new _class2(2, 3));
// The QR Code can tolerate about 25% erroneous codewords
(0,defineProperty/* default */.A)(Ecc, "HIGH", new _class2(3, 2));
var QrSegment = /*#__PURE__*/function () {
  // Creates a new QR Code segment with the given attributes and data.
  // The character count (numChars) must agree with the mode and the bit buffer length,
  // but the constranumber isn't checked. The given bit buffer is cloned and stored.
  function QrSegment(mode, numChars, bitData) {
    (0,classCallCheck/* default */.A)(this, QrSegment);
    /*-- Constructor (low level) and fields --*/
    // The mode indicator of this segment.
    (0,defineProperty/* default */.A)(this, "mode", void 0);
    // The length of this segment's unencoded data. Measured in characters for
    // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.
    // Always zero or positive. Not the same as the data's bit length.
    (0,defineProperty/* default */.A)(this, "numChars", void 0);
    // The data bits of this segment. Accessed through getData().
    (0,defineProperty/* default */.A)(this, "bitData", void 0);
    this.mode = mode;
    this.numChars = numChars;
    this.bitData = bitData;
    if (numChars < 0) throw new RangeError('Invalid argument');
    this.bitData = bitData.slice(); // Make defensive copy
  }

  /*-- Methods --*/

  // Returns a new copy of the data bits of this segment.
  (0,createClass/* default */.A)(QrSegment, [{
    key: "getData",
    value: function getData() {
      return this.bitData.slice(); // Make defensive copy
    }

    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at
    // the given version. The result is infinity if a segment has too many characters to fit its length field.
  }], [{
    key: "makeBytes",
    value: /*-- Static factory functions (mid level) --*/

    // Returns a segment representing the given binary data encoded in
    // byte mode. All input byte arrays are acceptable. Any text string
    // can be converted to UTF-8 bytes and encoded as a byte mode segment.
    function makeBytes(data) {
      var bb = [];
      var _iterator = (0,createForOfIteratorHelper/* default */.A)(data),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var b = _step.value;
          appendBits(b, 8, bb);
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      return new QrSegment(Mode.BYTE, data.length, bb);
    }

    // Returns a segment representing the given string of decimal digits encoded in numeric mode.
  }, {
    key: "makeNumeric",
    value: function makeNumeric(digits) {
      if (!QrSegment.isNumeric(digits)) throw new RangeError('String contains non-numeric characters');
      var bb = [];
      for (var i = 0; i < digits.length;) {
        // Consume up to 3 digits per iteration
        var n = Math.min(digits.length - i, 3);
        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);
        i += n;
      }
      return new QrSegment(Mode.NUMERIC, digits.length, bb);
    }

    // Returns a segment representing the given text string encoded in alphanumeric mode.
    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,
    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.
  }, {
    key: "makeAlphanumeric",
    value: function makeAlphanumeric(text) {
      if (!QrSegment.isAlphanumeric(text)) throw new RangeError('String contains unencodable characters in alphanumeric mode');
      var bb = [];
      var i;
      for (i = 0; i + 2 <= text.length; i += 2) {
        // Process groups of 2
        var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;
        temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));
        appendBits(temp, 11, bb);
      }
      if (i < text.length)
        // 1 character remaining
        appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);
      return new QrSegment(Mode.ALPHANUMERIC, text.length, bb);
    }

    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.
    // The result may use various segment modes and switch modes to optimize the length of the bit stream.
  }, {
    key: "makeSegments",
    value: function makeSegments(text) {
      // Select the most efficient segment encoding automatically
      if (text == '') return [];else if (QrSegment.isNumeric(text)) return [QrSegment.makeNumeric(text)];else if (QrSegment.isAlphanumeric(text)) return [QrSegment.makeAlphanumeric(text)];else return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];
    }

    // Returns a segment representing an Extended Channel Interpretation
    // (ECI) designator with the given assignment value.
  }, {
    key: "makeEci",
    value: function makeEci(assignVal) {
      var bb = [];
      if (assignVal < 0) throw new RangeError('ECI assignment value out of range');else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);else if (assignVal < 1 << 14) {
        appendBits(2, 2, bb);
        appendBits(assignVal, 14, bb);
      } else if (assignVal < 1000000) {
        appendBits(6, 3, bb);
        appendBits(assignVal, 21, bb);
      } else throw new RangeError('ECI assignment value out of range');
      return new QrSegment(Mode.ECI, 0, bb);
    }

    // Tests whether the given string can be encoded as a segment in numeric mode.
    // A string is encodable iff each character is in the range 0 to 9.
  }, {
    key: "isNumeric",
    value: function isNumeric(text) {
      return QrSegment.NUMERIC_REGEX.test(text);
    }

    // Tests whether the given string can be encoded as a segment in alphanumeric mode.
    // A string is encodable iff each character is in the following set: 0 to 9, A to Z
    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.
  }, {
    key: "isAlphanumeric",
    value: function isAlphanumeric(text) {
      return QrSegment.ALPHANUMERIC_REGEX.test(text);
    }
  }, {
    key: "getTotalBits",
    value: function getTotalBits(segs, version) {
      var result = 0;
      var _iterator2 = (0,createForOfIteratorHelper/* default */.A)(segs),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var seg = _step2.value;
          var ccbits = seg.mode.numCharCountBits(version);
          if (seg.numChars >= 1 << ccbits) return Infinity; // The segment's length doesn't fit the field's bit width
          result += 4 + ccbits + seg.bitData.length;
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return result;
    }

    // Returns a new array of bytes representing the given string encoded in UTF-8.
  }, {
    key: "toUtf8ByteArray",
    value: function toUtf8ByteArray(input) {
      var str = encodeURI(input);
      var result = [];
      for (var i = 0; i < str.length; i++) {
        if (str.charAt(i) != '%') result.push(str.charCodeAt(i));else {
          result.push(parseInt(str.substring(i + 1, i + 3), 16));
          i += 2;
        }
      }
      return result;
    }

    /*-- Constants --*/

    // Describes precisely all strings that are encodable in numeric mode.
  }]);
  return QrSegment;
}();

/*
 * A QR Code symbol, which is a type of two-dimension barcode.
 * Invented by Denso Wave and described in the ISO/IEC 18004 standard.
 * Instances of this class represent an immutable square grid of dark and light cells.
 * The class provides static factory functions to create a QR Code from text or binary data.
 * The class covers the QR Code Model 2 specification, supporting all versions (sizes)
 * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.
 *
 * Ways to create a QR Code object:
 * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().
 * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().
 * - Low level: Custom-make the array of data codeword bytes (including
 *   segment headers and final padding, excluding error correction codewords),
 *   supply the appropriate version number, and call the QrCode() constructor.
 * (Note that all ways require supplying the desired error correction level.)
 */
(0,defineProperty/* default */.A)(QrSegment, "NUMERIC_REGEX", /^[0-9]*$/);
// Describes precisely all strings that are encodable in alphanumeric mode.
(0,defineProperty/* default */.A)(QrSegment, "ALPHANUMERIC_REGEX", /^[A-Z0-9 $%*+.\/:-]*$/);
// The set of all legal characters in alphanumeric mode,
// where each character value maps to the index in the string.
(0,defineProperty/* default */.A)(QrSegment, "ALPHANUMERIC_CHARSET", '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:');
var QrCode = /*#__PURE__*/function () {
  // Creates a new QR Code with the given version number,
  // error correction level, data codeword bytes, and mask number.
  // This is a low-level API that most users should not use directly.
  // A mid-level API is the encodeSegments() function.
  function QrCode(
  // The version number of this QR Code, which is between 1 and 40 (inclusive).
  // This determines the size of this barcode.
  version,
  // The error correction level used in this QR Code.
  errorCorrectionLevel, dataCodewords, oriMsk) {
    (0,classCallCheck/* default */.A)(this, QrCode);
    /*-- Fields --*/
    // The width and height of this QR Code, measured in modules, between
    // 21 and 177 (inclusive). This is equal to version * 4 + 17.
    (0,defineProperty/* default */.A)(this, "size", void 0);
    // The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).
    // Even if a QR Code is created with automatic masking requested (mask = -1),
    // the resulting object still has a mask value between 0 and 7.
    (0,defineProperty/* default */.A)(this, "mask", void 0);
    // The modules of this QR Code (false = light, true = dark).
    // Immutable after constructor finishes. Accessed through getModule().
    (0,defineProperty/* default */.A)(this, "modules", []);
    // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.
    (0,defineProperty/* default */.A)(this, "isFunction", []);
    /*-- Constructor (low level) and fields --*/
    // The version number of this QR Code, which is between 1 and 40 (inclusive).
    // This determines the size of this barcode.
    (0,defineProperty/* default */.A)(this, "version", void 0);
    // The error correction level used in this QR Code.
    (0,defineProperty/* default */.A)(this, "errorCorrectionLevel", void 0);
    var msk = oriMsk;
    this.version = version;
    this.errorCorrectionLevel = errorCorrectionLevel;
    // Check scalar arguments
    if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION) throw new RangeError('Version value out of range');
    if (msk < -1 || msk > 7) throw new RangeError('Mask value out of range');
    this.size = version * 4 + 17;

    // Initialize both grids to be size*size arrays of Boolean false
    var row = [];
    for (var i = 0; i < this.size; i++) row.push(false);
    for (var _i = 0; _i < this.size; _i++) {
      this.modules.push(row.slice()); // Initially all light
      this.isFunction.push(row.slice());
    }

    // Compute ECC, draw modules
    this.drawFunctionPatterns();
    var allCodewords = this.addEccAndInterleave(dataCodewords);
    this.drawCodewords(allCodewords);

    // Do masking
    if (msk == -1) {
      // Automatically choose best mask
      var minPenalty = 1000000000;
      for (var _i2 = 0; _i2 < 8; _i2++) {
        this.applyMask(_i2);
        this.drawFormatBits(_i2);
        var penalty = this.getPenaltyScore();
        if (penalty < minPenalty) {
          msk = _i2;
          minPenalty = penalty;
        }
        this.applyMask(_i2); // Undoes the mask due to XOR
      }
    }
    assert(0 <= msk && msk <= 7);
    this.mask = msk;
    this.applyMask(msk); // Apply the final choice of mask
    this.drawFormatBits(msk); // Overwrite old format bits

    this.isFunction = [];
  }

  /*-- Accessor methods --*/

  // Returns the color of the module (pixel) at the given coordinates, which is false
  // for light or true for dark. The top left corner has the coordinates (x=0, y=0).
  // If the given coordinates are out of bounds, then false (light) is returned.
  (0,createClass/* default */.A)(QrCode, [{
    key: "getModule",
    value: function getModule(x, y) {
      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];
    }

    // Modified to expose modules for easy access
  }, {
    key: "getModules",
    value: function getModules() {
      return this.modules;
    }

    /*-- Private helper methods for constructor: Drawing function modules --*/

    // Reads this object's version field, and draws and marks all function modules.
  }, {
    key: "drawFunctionPatterns",
    value: function drawFunctionPatterns() {
      // Draw horizontal and vertical timing patterns
      for (var i = 0; i < this.size; i++) {
        this.setFunctionModule(6, i, i % 2 == 0);
        this.setFunctionModule(i, 6, i % 2 == 0);
      }

      // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)
      this.drawFinderPattern(3, 3);
      this.drawFinderPattern(this.size - 4, 3);
      this.drawFinderPattern(3, this.size - 4);

      // Draw numerous alignment patterns
      var alignPatPos = this.getAlignmentPatternPositions();
      var numAlign = alignPatPos.length;
      for (var _i3 = 0; _i3 < numAlign; _i3++) {
        for (var j = 0; j < numAlign; j++) {
          // Don't draw on the three finder corners
          if (!(_i3 == 0 && j == 0 || _i3 == 0 && j == numAlign - 1 || _i3 == numAlign - 1 && j == 0)) this.drawAlignmentPattern(alignPatPos[_i3], alignPatPos[j]);
        }
      }

      // Draw configuration data
      this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor
      this.drawVersion();
    }

    // Draws two copies of the format bits (with its own error correction code)
    // based on the given mask and this object's error correction level field.
  }, {
    key: "drawFormatBits",
    value: function drawFormatBits(mask) {
      // Calculate error correction code and pack bits
      var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is unumber2, mask is unumber3
      var rem = data;
      for (var i = 0; i < 10; i++) rem = rem << 1 ^ (rem >>> 9) * 0x537;
      var bits = (data << 10 | rem) ^ 0x5412; // unumber15
      assert(bits >>> 15 == 0);

      // Draw first copy
      for (var _i4 = 0; _i4 <= 5; _i4++) this.setFunctionModule(8, _i4, getBit(bits, _i4));
      this.setFunctionModule(8, 7, getBit(bits, 6));
      this.setFunctionModule(8, 8, getBit(bits, 7));
      this.setFunctionModule(7, 8, getBit(bits, 8));
      for (var _i5 = 9; _i5 < 15; _i5++) this.setFunctionModule(14 - _i5, 8, getBit(bits, _i5));

      // Draw second copy
      for (var _i6 = 0; _i6 < 8; _i6++) this.setFunctionModule(this.size - 1 - _i6, 8, getBit(bits, _i6));
      for (var _i7 = 8; _i7 < 15; _i7++) this.setFunctionModule(8, this.size - 15 + _i7, getBit(bits, _i7));
      this.setFunctionModule(8, this.size - 8, true); // Always dark
    }

    // Draws two copies of the version bits (with its own error correction code),
    // based on this object's version field, iff 7 <= version <= 40.
  }, {
    key: "drawVersion",
    value: function drawVersion() {
      if (this.version < 7) return;

      // Calculate error correction code and pack bits
      var rem = this.version; // version is unumber6, in the range [7, 40]
      for (var i = 0; i < 12; i++) rem = rem << 1 ^ (rem >>> 11) * 0x1f25;
      var bits = this.version << 12 | rem; // unumber18
      assert(bits >>> 18 == 0);

      // Draw two copies
      for (var _i8 = 0; _i8 < 18; _i8++) {
        var color = getBit(bits, _i8);
        var a = this.size - 11 + _i8 % 3;
        var b = Math.floor(_i8 / 3);
        this.setFunctionModule(a, b, color);
        this.setFunctionModule(b, a, color);
      }
    }

    // Draws a 9*9 finder pattern including the border separator,
    // with the center module at (x, y). Modules can be out of bounds.
  }, {
    key: "drawFinderPattern",
    value: function drawFinderPattern(x, y) {
      for (var dy = -4; dy <= 4; dy++) {
        for (var dx = -4; dx <= 4; dx++) {
          var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm
          var xx = x + dx;
          var yy = y + dy;
          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size) this.setFunctionModule(xx, yy, dist != 2 && dist != 4);
        }
      }
    }

    // Draws a 5*5 alignment pattern, with the center module
    // at (x, y). All modules must be in bounds.
  }, {
    key: "drawAlignmentPattern",
    value: function drawAlignmentPattern(x, y) {
      for (var dy = -2; dy <= 2; dy++) {
        for (var dx = -2; dx <= 2; dx++) this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);
      }
    }

    // Sets the color of a module and marks it as a function module.
    // Only used by the constructor. Coordinates must be in bounds.
  }, {
    key: "setFunctionModule",
    value: function setFunctionModule(x, y, isDark) {
      this.modules[y][x] = isDark;
      this.isFunction[y][x] = true;
    }

    /*-- Private helper methods for constructor: Codewords and masking --*/

    // Returns a new byte string representing the given data with the appropriate error correction
    // codewords appended to it, based on this object's version and error correction level.
  }, {
    key: "addEccAndInterleave",
    value: function addEccAndInterleave(data) {
      var ver = this.version;
      var ecl = this.errorCorrectionLevel;
      if (data.length != QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError('Invalid argument');

      // Calculate parameter numbers
      var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];
      var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];
      var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);
      var numShortBlocks = numBlocks - rawCodewords % numBlocks;
      var shortBlockLen = Math.floor(rawCodewords / numBlocks);

      // Split data numbero blocks and append ECC to each block
      var blocks = [];
      var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);
      for (var i = 0, k = 0; i < numBlocks; i++) {
        var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));
        k += dat.length;
        var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);
        if (i < numShortBlocks) dat.push(0);
        blocks.push(dat.concat(ecc));
      }

      // Interleave (not concatenate) the bytes from every block numbero a single sequence
      var result = [];
      var _loop = function _loop(_i9) {
        blocks.forEach(function (block, j) {
          // Skip the padding byte in short blocks
          if (_i9 != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[_i9]);
        });
      };
      for (var _i9 = 0; _i9 < blocks[0].length; _i9++) {
        _loop(_i9);
      }
      assert(result.length == rawCodewords);
      return result;
    }

    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire
    // data area of this QR Code. Function modules need to be marked off before this is called.
  }, {
    key: "drawCodewords",
    value: function drawCodewords(data) {
      if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8)) throw new RangeError('Invalid argument');
      var i = 0; // Bit index numbero the data
      // Do the funny zigzag scan
      for (var right = this.size - 1; right >= 1; right -= 2) {
        // Index of right column in each column pair
        if (right == 6) right = 5;
        for (var vert = 0; vert < this.size; vert++) {
          // Vertical counter
          for (var j = 0; j < 2; j++) {
            var x = right - j; // Actual x coordinate
            var upward = (right + 1 & 2) == 0;
            var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate
            if (!this.isFunction[y][x] && i < data.length * 8) {
              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));
              i++;
            }
            // If this QR Code has any remainder bits (0 to 7), they were assigned as
            // 0/false/light by the constructor and are left unchanged by this method
          }
        }
      }
      assert(i == data.length * 8);
    }

    // XORs the codeword modules in this QR Code with the given mask pattern.
    // The function modules must be marked and the codeword bits must be drawn
    // before masking. Due to the arithmetic of XOR, calling applyMask() with
    // the same mask value a second time will undo the mask. A final well-formed
    // QR Code needs exactly one (not zero, two, etc.) mask applied.
  }, {
    key: "applyMask",
    value: function applyMask(mask) {
      if (mask < 0 || mask > 7) throw new RangeError('Mask value out of range');
      for (var y = 0; y < this.size; y++) {
        for (var x = 0; x < this.size; x++) {
          var invert = void 0;
          switch (mask) {
            case 0:
              invert = (x + y) % 2 == 0;
              break;
            case 1:
              invert = y % 2 == 0;
              break;
            case 2:
              invert = x % 3 == 0;
              break;
            case 3:
              invert = (x + y) % 3 == 0;
              break;
            case 4:
              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;
              break;
            case 5:
              invert = x * y % 2 + x * y % 3 == 0;
              break;
            case 6:
              invert = (x * y % 2 + x * y % 3) % 2 == 0;
              break;
            case 7:
              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;
              break;
            default:
              throw new Error('Unreachable');
          }
          if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];
        }
      }
    }

    // Calculates and returns the penalty score based on state of this QR Code's current modules.
    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.
  }, {
    key: "getPenaltyScore",
    value: function getPenaltyScore() {
      var result = 0;

      // Adjacent modules in row having same color, and finder-like patterns
      for (var y = 0; y < this.size; y++) {
        var runColor = false;
        var runX = 0;
        var runHistory = [0, 0, 0, 0, 0, 0, 0];
        for (var x = 0; x < this.size; x++) {
          if (this.modules[y][x] == runColor) {
            runX++;
            if (runX == 5) result += QrCode.PENALTY_N1;else if (runX > 5) result++;
          } else {
            this.finderPenaltyAddHistory(runX, runHistory);
            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;
            runColor = this.modules[y][x];
            runX = 1;
          }
        }
        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;
      }
      // Adjacent modules in column having same color, and finder-like patterns
      for (var _x = 0; _x < this.size; _x++) {
        var _runColor = false;
        var runY = 0;
        var _runHistory = [0, 0, 0, 0, 0, 0, 0];
        for (var _y = 0; _y < this.size; _y++) {
          if (this.modules[_y][_x] == _runColor) {
            runY++;
            if (runY == 5) result += QrCode.PENALTY_N1;else if (runY > 5) result++;
          } else {
            this.finderPenaltyAddHistory(runY, _runHistory);
            if (!_runColor) result += this.finderPenaltyCountPatterns(_runHistory) * QrCode.PENALTY_N3;
            _runColor = this.modules[_y][_x];
            runY = 1;
          }
        }
        result += this.finderPenaltyTerminateAndCount(_runColor, runY, _runHistory) * QrCode.PENALTY_N3;
      }

      // 2*2 blocks of modules having same color
      for (var _y2 = 0; _y2 < this.size - 1; _y2++) {
        for (var _x2 = 0; _x2 < this.size - 1; _x2++) {
          var color = this.modules[_y2][_x2];
          if (color == this.modules[_y2][_x2 + 1] && color == this.modules[_y2 + 1][_x2] && color == this.modules[_y2 + 1][_x2 + 1]) result += QrCode.PENALTY_N2;
        }
      }

      // Balance of dark and light modules
      var dark = 0;
      var _iterator3 = (0,createForOfIteratorHelper/* default */.A)(this.modules),
        _step3;
      try {
        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
          var row = _step3.value;
          dark = row.reduce(function (sum, color) {
            return sum + (color ? 1 : 0);
          }, dark);
        }
      } catch (err) {
        _iterator3.e(err);
      } finally {
        _iterator3.f();
      }
      var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2
      // Compute the smallest numbereger k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%
      var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;
      assert(0 <= k && k <= 9);
      result += k * QrCode.PENALTY_N4;
      assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4
      return result;
    }

    /*-- Private helper functions --*/

    // Returns an ascending list of positions of alignment patterns for this version number.
    // Each position is in the range [0,177), and are used on both the x and y axes.
    // This could be implemented as lookup table of 40 variable-length lists of numberegers.
  }, {
    key: "getAlignmentPatternPositions",
    value: function getAlignmentPatternPositions() {
      if (this.version == 1) return [];else {
        var numAlign = Math.floor(this.version / 7) + 2;
        var step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;
        var result = [6];
        for (var pos = this.size - 7; result.length < numAlign; pos -= step) result.splice(1, 0, pos);
        return result;
      }
    }

    // Returns the number of data bits that can be stored in a QR Code of the given version number, after
    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.
    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.
  }, {
    key: "finderPenaltyCountPatterns",
    value:
    // Can only be called immediately after a light run is added, and
    // returns either 0, 1, or 2. A helper function for getPenaltyScore().
    function finderPenaltyCountPatterns(runHistory) {
      var n = runHistory[1];
      assert(n <= this.size * 3);
      var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;
      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);
    }

    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().
  }, {
    key: "finderPenaltyTerminateAndCount",
    value: function finderPenaltyTerminateAndCount(currentRunColor, oriCurrentRunLength, runHistory) {
      var currentRunLength = oriCurrentRunLength;
      if (currentRunColor) {
        // Terminate dark run
        this.finderPenaltyAddHistory(currentRunLength, runHistory);
        currentRunLength = 0;
      }
      currentRunLength += this.size; // Add light border to final run
      this.finderPenaltyAddHistory(currentRunLength, runHistory);
      return this.finderPenaltyCountPatterns(runHistory);
    }

    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().
  }, {
    key: "finderPenaltyAddHistory",
    value: function finderPenaltyAddHistory(oriCurrentRunLength, runHistory) {
      var currentRunLength = oriCurrentRunLength;
      if (runHistory[0] == 0) currentRunLength += this.size; // Add light border to initial run
      runHistory.pop();
      runHistory.unshift(currentRunLength);
    }

    /*-- Constants and tables --*/

    // The minimum version number supported in the QR Code Model 2 standard.
  }], [{
    key: "encodeText",
    value: /*-- Static factory functions (high level) --*/

    // Returns a QR Code representing the given Unicode text string at the given error correction level.
    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer
    // Unicode code ponumbers (not UTF-16 code units) if the low error correction level is used. The smallest possible
    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the
    // ecl argument if it can be done without increasing the version.
    function encodeText(text, ecl) {
      var segs = QrSegment.makeSegments(text);
      return QrCode.encodeSegments(segs, ecl);
    }

    // Returns a QR Code representing the given binary data at the given error correction level.
    // This function always encodes using the binary segment mode, not any text mode. The maximum number of
    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.
    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.
  }, {
    key: "encodeBinary",
    value: function encodeBinary(data, ecl) {
      var seg = QrSegment.makeBytes(data);
      return QrCode.encodeSegments([seg], ecl);
    }

    /*-- Static factory functions (mid level) --*/

    // Returns a QR Code representing the given segments with the given encoding parameters.
    // The smallest possible QR Code version within the given range is automatically
    // chosen for the output. Iff boostEcl is true, then the ECC level of the result
    // may be higher than the ecl argument if it can be done without increasing the
    // version. The mask number is either between 0 to 7 (inclusive) to force that
    // mask, or -1 to automatically choose an appropriate mask (which may be slow).
    // This function allows the user to create a custom sequence of segments that switches
    // between modes (such as alphanumeric and byte) to encode text in less space.
    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().
  }, {
    key: "encodeSegments",
    value: function encodeSegments(segs, oriEcl) {
      var minVersion = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;
      var maxVersion = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 40;
      var mask = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;
      var boostEcl = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : true;
      if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION) || mask < -1 || mask > 7) throw new RangeError('Invalid value');

      // Find the minimal version number to use
      var version;
      var dataUsedBits;
      for (version = minVersion;; version++) {
        var _dataCapacityBits = QrCode.getNumDataCodewords(version, oriEcl) * 8; // Number of data bits available
        var usedBits = QrSegment.getTotalBits(segs, version);
        if (usedBits <= _dataCapacityBits) {
          dataUsedBits = usedBits;
          break; // This version number is found to be suitable
        }
        if (version >= maxVersion)
          // All versions in the range could not fit the given data
          throw new RangeError('Data too long');
      }
      var ecl = oriEcl;
      // Increase the error correction level while the data still fits in the current version number
      for (var _i10 = 0, _arr = [Ecc.MEDIUM, Ecc.QUARTILE, Ecc.HIGH]; _i10 < _arr.length; _i10++) {
        var newEcl = _arr[_i10];
        // From low to high
        if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;
      }

      // Concatenate all segments to create the data bit string
      var bb = [];
      var _iterator4 = (0,createForOfIteratorHelper/* default */.A)(segs),
        _step4;
      try {
        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
          var seg = _step4.value;
          appendBits(seg.mode.modeBits, 4, bb);
          appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);
          var _iterator5 = (0,createForOfIteratorHelper/* default */.A)(seg.getData()),
            _step5;
          try {
            for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {
              var b = _step5.value;
              bb.push(b);
            }
          } catch (err) {
            _iterator5.e(err);
          } finally {
            _iterator5.f();
          }
        }
      } catch (err) {
        _iterator4.e(err);
      } finally {
        _iterator4.f();
      }
      assert(bb.length == dataUsedBits);

      // Add terminator and pad up to a byte if applicable
      var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;
      assert(bb.length <= dataCapacityBits);
      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);
      appendBits(0, (8 - bb.length % 8) % 8, bb);
      assert(bb.length % 8 == 0);

      // Pad with alternating bytes until data capacity is reached
      for (var padByte = 0xec; bb.length < dataCapacityBits; padByte ^= 0xec ^ 0x11) appendBits(padByte, 8, bb);

      // Pack bits numbero bytes in big endian
      var dataCodewords = [];
      while (dataCodewords.length * 8 < bb.length) dataCodewords.push(0);
      bb.forEach(function (b, i) {
        return dataCodewords[i >>> 3] |= b << 7 - (i & 7);
      });

      // Create the QR Code object
      return new QrCode(version, ecl, dataCodewords, mask);
    }
  }, {
    key: "getNumRawDataModules",
    value: function getNumRawDataModules(ver) {
      if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION) throw new RangeError('Version number out of range');
      var result = (16 * ver + 128) * ver + 64;
      if (ver >= 2) {
        var numAlign = Math.floor(ver / 7) + 2;
        result -= (25 * numAlign - 10) * numAlign - 55;
        if (ver >= 7) result -= 36;
      }
      assert(208 <= result && result <= 29648);
      return result;
    }

    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any
    // QR Code of the given version number and error correction level, with remainder bits discarded.
    // This stateless pure function could be implemented as a (40*4)-cell lookup table.
  }, {
    key: "getNumDataCodewords",
    value: function getNumDataCodewords(ver, ecl) {
      return Math.floor(QrCode.getNumRawDataModules(ver) / 8) - QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];
    }

    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be
    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.
  }, {
    key: "reedSolomonComputeDivisor",
    value: function reedSolomonComputeDivisor(degree) {
      if (degree < 1 || degree > 255) throw new RangeError('Degree out of range');
      // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.
      // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the unumber8 array [255, 8, 93].
      var result = [];
      for (var i = 0; i < degree - 1; i++) result.push(0);
      result.push(1); // Start off with the monomial x^0

      // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),
      // and drop the highest monomial term which is always 1x^degree.
      // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).
      var root = 1;
      for (var _i11 = 0; _i11 < degree; _i11++) {
        // Multiply the current product by (x - r^i)
        for (var j = 0; j < result.length; j++) {
          result[j] = QrCode.reedSolomonMultiply(result[j], root);
          if (j + 1 < result.length) result[j] ^= result[j + 1];
        }
        root = QrCode.reedSolomonMultiply(root, 0x02);
      }
      return result;
    }

    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.
  }, {
    key: "reedSolomonComputeRemainder",
    value: function reedSolomonComputeRemainder(data, divisor) {
      var result = divisor.map(function () {
        return 0;
      });
      var _iterator6 = (0,createForOfIteratorHelper/* default */.A)(data),
        _step6;
      try {
        var _loop2 = function _loop2() {
          var b = _step6.value;
          // Polynomial division
          var factor = b ^ result.shift();
          result.push(0);
          divisor.forEach(function (coef, i) {
            return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);
          });
        };
        for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {
          _loop2();
        }
      } catch (err) {
        _iterator6.e(err);
      } finally {
        _iterator6.f();
      }
      return result;
    }

    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result
    // are unsigned 8-bit numberegers. This could be implemented as a lookup table of 256*256 entries of unumber8.
  }, {
    key: "reedSolomonMultiply",
    value: function reedSolomonMultiply(x, y) {
      if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError('Byte out of range');
      // Russian peasant multiplication
      var z = 0;
      for (var i = 7; i >= 0; i--) {
        z = z << 1 ^ (z >>> 7) * 0x11d;
        z ^= (y >>> i & 1) * x;
      }
      assert(z >>> 8 == 0);
      return z;
    }
  }]);
  return QrCode;
}();
(0,defineProperty/* default */.A)(QrCode, "MIN_VERSION", 1);
// The maximum version number supported in the QR Code Model 2 standard.
(0,defineProperty/* default */.A)(QrCode, "MAX_VERSION", 40);
// For use in getPenaltyScore(), when evaluating which mask is best.
(0,defineProperty/* default */.A)(QrCode, "PENALTY_N1", 3);
(0,defineProperty/* default */.A)(QrCode, "PENALTY_N2", 3);
(0,defineProperty/* default */.A)(QrCode, "PENALTY_N3", 40);
(0,defineProperty/* default */.A)(QrCode, "PENALTY_N4", 10);
(0,defineProperty/* default */.A)(QrCode, "ECC_CODEWORDS_PER_BLOCK", [
// Version: (note that index 0 is for padding, and is set to an illegal value)
//0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level
[-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],
// Low
[-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],
// Medium
[-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],
// Quartile
[-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30] // High
]);
(0,defineProperty/* default */.A)(QrCode, "NUM_ERROR_CORRECTION_BLOCKS", [
// Version: (note that index 0 is for padding, and is set to an illegal value)
//0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level
[-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],
// Low
[-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],
// Medium
[-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],
// Quartile
[-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81] // High
]);
;// ./node_modules/@rc-component/qrcode/es/utils.js
// Part logic is from `qrcode.react`. (ISC License)
// https://github.com/zpao/qrcode.react

// ==========================================================



// =================== ERROR_LEVEL ==========================
var ERROR_LEVEL_MAP = {
  L: Ecc.LOW,
  M: Ecc.MEDIUM,
  Q: Ecc.QUARTILE,
  H: Ecc.HIGH
};

// =================== DEFAULT_VALUE ==========================
var DEFAULT_SIZE = 128;
var DEFAULT_LEVEL = 'L';
var DEFAULT_BACKGROUND_COLOR = '#FFFFFF';
var DEFAULT_FRONT_COLOR = '#000000';
var DEFAULT_NEED_MARGIN = false;
var DEFAULT_MINVERSION = 1;
var SPEC_MARGIN_SIZE = 4;
var DEFAULT_MARGIN_SIZE = 0;
var DEFAULT_IMG_SCALE = 0.1;

// =================== UTILS ==========================
/**
 * Generate a path string from modules
 * @param modules
 * @param margin 
 * @returns 
 */
function generatePath(modules) {
  var margin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
  var ops = [];
  modules.forEach(function (row, y) {
    var start = null;
    row.forEach(function (cell, x) {
      if (!cell && start !== null) {
        ops.push("M".concat(start + margin, " ").concat(y + margin, "h").concat(x - start, "v1H").concat(start + margin, "z"));
        start = null;
        return;
      }
      if (x === row.length - 1) {
        if (!cell) {
          return;
        }
        if (start === null) {
          ops.push("M".concat(x + margin, ",").concat(y + margin, " h1v1H").concat(x + margin, "z"));
        } else {
          ops.push("M".concat(start + margin, ",").concat(y + margin, " h").concat(x + 1 - start, "v1H").concat(start + margin, "z"));
        }
        return;
      }
      if (cell && start === null) {
        start = x;
      }
    });
  });
  return ops.join('');
}
/**
 * Excavate modules
 * @param modules 
 * @param excavation 
 * @returns 
 */
function excavateModules(modules, excavation) {
  return modules.slice().map(function (row, y) {
    if (y < excavation.y || y >= excavation.y + excavation.h) {
      return row;
    }
    return row.map(function (cell, x) {
      if (x < excavation.x || x >= excavation.x + excavation.w) {
        return cell;
      }
      return false;
    });
  });
}

/**
 * Get image settings
 * @param cells The modules of the QR code
 * @param size The size of the QR code
 * @param margin 
 * @param imageSettings 
 * @returns 
 */
function getImageSettings(cells, size, margin, imageSettings) {
  if (imageSettings == null) {
    return null;
  }
  var numCells = cells.length + margin * 2;
  var defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);
  var scale = numCells / size;
  var w = (imageSettings.width || defaultSize) * scale;
  var h = (imageSettings.height || defaultSize) * scale;
  var x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;
  var y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;
  var opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;
  var excavation = null;
  if (imageSettings.excavate) {
    var floorX = Math.floor(x);
    var floorY = Math.floor(y);
    var ceilW = Math.ceil(w + x - floorX);
    var ceilH = Math.ceil(h + y - floorY);
    excavation = {
      x: floorX,
      y: floorY,
      w: ceilW,
      h: ceilH
    };
  }
  var crossOrigin = imageSettings.crossOrigin;
  return {
    x: x,
    y: y,
    h: h,
    w: w,
    excavation: excavation,
    opacity: opacity,
    crossOrigin: crossOrigin
  };
}

/**
 * Get margin size
 * @param needMargin Whether need margin
 * @param marginSize Custom margin size
 * @returns 
 */
function getMarginSize(needMargin, marginSize) {
  if (marginSize != null) {
    return Math.floor(marginSize);
  }
  return needMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;
}
/**
 * Check if Path2D is supported
 */
var isSupportPath2d = function () {
  try {
    new Path2D().addPath(new Path2D());
  } catch (e) {
    return false;
  }
  return true;
}();
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(89379);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
;// ./node_modules/@rc-component/qrcode/es/hooks/useQRCode.js



function useQRCode(_ref) {
  var value = _ref.value,
    level = _ref.level,
    minVersion = _ref.minVersion,
    includeMargin = _ref.includeMargin,
    marginSize = _ref.marginSize,
    imageSettings = _ref.imageSettings,
    size = _ref.size;
  var qrcode = (0,react.useMemo)(function () {
    var segments = QrSegment.makeSegments(value);
    return QrCode.encodeSegments(segments, ERROR_LEVEL_MAP[level], minVersion);
  }, [value, level, minVersion]);
  var _useMemo = (0,react.useMemo)(function () {
      var cs = qrcode.getModules();
      var mg = getMarginSize(includeMargin, marginSize);
      var ncs = cs.length + mg * 2;
      var cis = getImageSettings(cs, size, mg, imageSettings);
      return {
        cells: cs,
        margin: mg,
        numCells: ncs,
        calculatedImageSettings: cis
      };
    }, [qrcode, size, imageSettings, includeMargin, marginSize]),
    cells = _useMemo.cells,
    margin = _useMemo.margin,
    numCells = _useMemo.numCells,
    calculatedImageSettings = _useMemo.calculatedImageSettings;
  return {
    qrcode: qrcode,
    margin: margin,
    cells: cells,
    numCells: numCells,
    calculatedImageSettings: calculatedImageSettings
  };
}
;// ./node_modules/@rc-component/qrcode/es/QRCodeCanvas.js




var _excluded = ["value", "size", "level", "bgColor", "fgColor", "includeMargin", "minVersion", "marginSize", "style", "imageSettings"];



var QRCodeCanvas = /*#__PURE__*/react.forwardRef(function QRCodeCanvas(props, forwardedRef) {
  var value = props.value,
    _props$size = props.size,
    size = _props$size === void 0 ? DEFAULT_SIZE : _props$size,
    _props$level = props.level,
    level = _props$level === void 0 ? DEFAULT_LEVEL : _props$level,
    _props$bgColor = props.bgColor,
    bgColor = _props$bgColor === void 0 ? DEFAULT_BACKGROUND_COLOR : _props$bgColor,
    _props$fgColor = props.fgColor,
    fgColor = _props$fgColor === void 0 ? DEFAULT_FRONT_COLOR : _props$fgColor,
    _props$includeMargin = props.includeMargin,
    includeMargin = _props$includeMargin === void 0 ? DEFAULT_NEED_MARGIN : _props$includeMargin,
    _props$minVersion = props.minVersion,
    minVersion = _props$minVersion === void 0 ? DEFAULT_MINVERSION : _props$minVersion,
    marginSize = props.marginSize,
    style = props.style,
    imageSettings = props.imageSettings,
    otherProps = (0,objectWithoutProperties/* default */.A)(props, _excluded);
  var imgSrc = imageSettings === null || imageSettings === void 0 ? void 0 : imageSettings.src;
  var _canvas = (0,react.useRef)(null);
  var _image = (0,react.useRef)(null);
  var setCanvasRef = (0,react.useCallback)(function (node) {
    _canvas.current = node;
    if (typeof forwardedRef === 'function') {
      forwardedRef(node);
    } else if (forwardedRef) {
      forwardedRef.current = node;
    }
  }, [forwardedRef]);
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    setIsImageLoaded = _useState2[1];
  var _useQRCode = useQRCode({
      value: value,
      level: level,
      minVersion: minVersion,
      includeMargin: includeMargin,
      marginSize: marginSize,
      imageSettings: imageSettings,
      size: size
    }),
    margin = _useQRCode.margin,
    cells = _useQRCode.cells,
    numCells = _useQRCode.numCells,
    calculatedImageSettings = _useQRCode.calculatedImageSettings;
  (0,react.useEffect)(function () {
    if (_canvas.current != null) {
      var canvas = _canvas.current;
      var ctx = canvas.getContext('2d');
      if (!ctx) {
        return;
      }
      var cellsToDraw = cells;
      var image = _image.current;
      var haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;
      if (haveImageToRender) {
        if (calculatedImageSettings.excavation != null) {
          cellsToDraw = excavateModules(cells, calculatedImageSettings.excavation);
        }
      }
      var pixelRatio = window.devicePixelRatio || 1;
      canvas.height = canvas.width = size * pixelRatio;
      var scale = size / numCells * pixelRatio;
      ctx.scale(scale, scale);
      ctx.fillStyle = bgColor;
      ctx.fillRect(0, 0, numCells, numCells);
      ctx.fillStyle = fgColor;
      if (isSupportPath2d) {
        ctx.fill(new Path2D(generatePath(cellsToDraw, margin)));
      } else {
        cells.forEach(function (row, rdx) {
          row.forEach(function (cell, cdx) {
            if (cell) {
              ctx.fillRect(cdx + margin, rdx + margin, 1, 1);
            }
          });
        });
      }
      if (calculatedImageSettings) {
        ctx.globalAlpha = calculatedImageSettings.opacity;
      }
      if (haveImageToRender) {
        ctx.drawImage(image, calculatedImageSettings.x + margin, calculatedImageSettings.y + margin, calculatedImageSettings.w, calculatedImageSettings.h);
      }
    }
  });
  (0,react.useEffect)(function () {
    setIsImageLoaded(false);
  }, [imgSrc]);
  var canvasStyle = (0,objectSpread2/* default */.A)({
    height: size,
    width: size
  }, style);
  var img = null;
  if (imgSrc != null) {
    img = /*#__PURE__*/react.createElement("img", {
      src: imgSrc,
      key: imgSrc,
      style: {
        display: 'none'
      },
      onLoad: function onLoad() {
        setIsImageLoaded(true);
      },
      ref: _image
      // when crossOrigin is not set, the image will be tainted
      // and the canvas cannot be exported to an image
      ,
      crossOrigin: calculatedImageSettings === null || calculatedImageSettings === void 0 ? void 0 : calculatedImageSettings.crossOrigin
    });
  }
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("canvas", (0,esm_extends/* default */.A)({
    style: canvasStyle,
    height: size,
    width: size,
    ref: setCanvasRef,
    role: "img"
  }, otherProps)), img);
});
QRCodeCanvas.displayName = 'QRCodeCanvas';

;// ./node_modules/@rc-component/qrcode/es/QRCodeSVG.js


var QRCodeSVG_excluded = ["value", "size", "level", "bgColor", "fgColor", "includeMargin", "minVersion", "title", "marginSize", "imageSettings"];



var QRCodeSVG = /*#__PURE__*/react.forwardRef(function QRCodeSVG(props, forwardedRef) {
  var value = props.value,
    _props$size = props.size,
    size = _props$size === void 0 ? DEFAULT_SIZE : _props$size,
    _props$level = props.level,
    level = _props$level === void 0 ? DEFAULT_LEVEL : _props$level,
    _props$bgColor = props.bgColor,
    bgColor = _props$bgColor === void 0 ? DEFAULT_BACKGROUND_COLOR : _props$bgColor,
    _props$fgColor = props.fgColor,
    fgColor = _props$fgColor === void 0 ? DEFAULT_FRONT_COLOR : _props$fgColor,
    _props$includeMargin = props.includeMargin,
    includeMargin = _props$includeMargin === void 0 ? DEFAULT_NEED_MARGIN : _props$includeMargin,
    _props$minVersion = props.minVersion,
    minVersion = _props$minVersion === void 0 ? DEFAULT_MINVERSION : _props$minVersion,
    title = props.title,
    marginSize = props.marginSize,
    imageSettings = props.imageSettings,
    otherProps = (0,objectWithoutProperties/* default */.A)(props, QRCodeSVG_excluded);
  var _useQRCode = useQRCode({
      value: value,
      level: level,
      minVersion: minVersion,
      includeMargin: includeMargin,
      marginSize: marginSize,
      imageSettings: imageSettings,
      size: size
    }),
    margin = _useQRCode.margin,
    cells = _useQRCode.cells,
    numCells = _useQRCode.numCells,
    calculatedImageSettings = _useQRCode.calculatedImageSettings;
  var cellsToDraw = cells;
  var image = null;
  if (imageSettings != null && calculatedImageSettings != null) {
    if (calculatedImageSettings.excavation != null) {
      cellsToDraw = excavateModules(cells, calculatedImageSettings.excavation);
    }
    image = /*#__PURE__*/react.createElement("image", {
      href: imageSettings.src,
      height: calculatedImageSettings.h,
      width: calculatedImageSettings.w,
      x: calculatedImageSettings.x + margin,
      y: calculatedImageSettings.y + margin,
      preserveAspectRatio: "none",
      opacity: calculatedImageSettings.opacity
      // when crossOrigin is not set, the image will be tainted
      // and the canvas cannot be exported to an image
      ,
      crossOrigin: calculatedImageSettings.crossOrigin
    });
  }
  var fgPath = generatePath(cellsToDraw, margin);
  return /*#__PURE__*/react.createElement("svg", (0,esm_extends/* default */.A)({
    height: size,
    width: size,
    viewBox: "0 0 ".concat(numCells, " ").concat(numCells),
    ref: forwardedRef,
    role: "img"
  }, otherProps), !!title && /*#__PURE__*/react.createElement("title", null, title), /*#__PURE__*/react.createElement("path", {
    fill: bgColor,
    d: "M0,0 h".concat(numCells, "v").concat(numCells, "H0z"),
    shapeRendering: "crispEdges"
  }), /*#__PURE__*/react.createElement("path", {
    fill: fgColor,
    d: fgPath,
    shapeRendering: "crispEdges"
  }), image);
});
QRCodeSVG.displayName = 'QRCodeSVG';

;// ./node_modules/@rc-component/qrcode/es/index.js




/***/ })

}]);