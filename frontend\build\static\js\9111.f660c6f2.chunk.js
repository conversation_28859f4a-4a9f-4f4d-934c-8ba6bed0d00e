"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9111],{

/***/ 94110:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_AppBuilderPage)
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./src/pages/AppBuilderEnhanced.js + 8 modules
var AppBuilderEnhanced = __webpack_require__(43771);
;// ./src/pages/AppBuilderPage.css
// extracted by mini-css-extract-plugin

;// ./src/pages/AppBuilderPage.js




/**
 * AppBuilderPage component
 * Main interface for the app builder functionality
 * Using the enhanced version of the App Builder for production
 */
var AppBuilderPage = function AppBuilderPage() {
  return /*#__PURE__*/react.createElement(AppBuilderEnhanced/* default */.A, null);
};
/* harmony default export */ const pages_AppBuilderPage = (AppBuilderPage);

/***/ })

}]);