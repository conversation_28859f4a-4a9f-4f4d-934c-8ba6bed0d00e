"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4841],{

/***/ 21167:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96540);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(11080);
/* harmony import */ var _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(16918);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(35346);


// Optimized Ant Design imports for better tree-shaking


var Title = _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Title,
  Paragraph = _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Paragraph;

/**
 * HomePageMVP component
 * MVP-focused landing page for the application
 */
var HomePageMVP = function HomePageMVP() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      padding: '40px 20px',
      maxWidth: '1200px',
      margin: '0 auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center',
      marginBottom: '60px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 1,
    style: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      marginBottom: '20px'
    }
  }, "App Builder 201 - MVP"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, {
    style: {
      fontSize: '20px',
      color: '#666',
      maxWidth: '600px',
      margin: '0 auto'
    }
  }, "Build your application with minimal setup. Start with our MVP version and create amazing apps in minutes."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    size: "large",
    style: {
      marginTop: '30px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__/* .Link */ .N_, {
    to: "/mvp"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary",
    size: "large",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .RocketOutlined */ .PKb, null),
    style: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      border: 'none',
      height: '50px',
      padding: '0 30px',
      fontSize: '16px'
    }
  }, "Start Building Now")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__/* .Link */ .N_, {
    to: "/app-builder"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    size: "large",
    style: {
      height: '50px',
      padding: '0 30px'
    }
  }, "Full Version")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Alert */ .Fc, {
    message: "\uD83D\uDE80 MVP Version Available",
    description: "Try our streamlined MVP version for quick app prototyping. Perfect for getting started with core features.",
    type: "info",
    showIcon: true,
    style: {
      marginBottom: '40px'
    },
    action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__/* .Link */ .N_, {
      to: "/mvp"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
      size: "small",
      type: "primary"
    }, "Try MVP"))
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Row */ .fI, {
    gutter: [32, 32],
    justify: "center"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    hoverable: true,
    style: {
      textAlign: 'center',
      height: '100%'
    },
    cover: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
      style: {
        padding: '40px',
        backgroundColor: '#f0f2f5'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .ThunderboltOutlined */ .CwG, {
      style: {
        fontSize: '48px',
        color: '#1890ff'
      }
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 3
  }, "MVP Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Quick and simple app builder with essential features. Perfect for rapid prototyping and MVP development."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__/* .Link */ .N_, {
    to: "/mvp"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary",
    size: "large"
  }, "Start MVP")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    hoverable: true,
    style: {
      textAlign: 'center',
      height: '100%'
    },
    cover: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
      style: {
        padding: '40px',
        backgroundColor: '#f0f2f5'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .AppstoreOutlined */ .rS9, {
      style: {
        fontSize: '48px',
        color: '#52c41a'
      }
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 3
  }, "Full App Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Complete app builder with advanced features, themes, collaboration, and comprehensive tooling."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__/* .Link */ .N_, {
    to: "/app-builder"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary",
    size: "large"
  }, "Full Builder")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    hoverable: true,
    style: {
      textAlign: 'center',
      height: '100%'
    },
    cover: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
      style: {
        padding: '40px',
        backgroundColor: '#f0f2f5'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .ToolOutlined */ .xuD, {
      style: {
        fontSize: '48px',
        color: '#fa8c16'
      }
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 3
  }, "WebSocket Testing"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Test real-time communication features and WebSocket connections for your applications."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__/* .Link */ .N_, {
    to: "/websocket"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary",
    size: "large"
  }, "Test WebSocket"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center',
      marginTop: '60px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 2
  }, "MVP Features"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, {
    style: {
      fontSize: '16px',
      color: '#666',
      marginBottom: '40px'
    }
  }, "Everything you need to build your first app"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Row */ .fI, {
    gutter: [24, 24],
    style: {
      marginTop: '40px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .StarOutlined */ .L0Y, {
    style: {
      fontSize: '32px',
      color: '#1890ff',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 4
  }, "Component Creation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Add buttons, text, inputs, images, cards, and lists with simple configuration."))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .StarOutlined */ .L0Y, {
    style: {
      fontSize: '32px',
      color: '#52c41a',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 4
  }, "Layout Design"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Organize components using Grid, Flex, and Stack layouts with responsive options."))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .StarOutlined */ .L0Y, {
    style: {
      fontSize: '32px',
      color: '#fa8c16',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 4
  }, "Real-time Updates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "See changes instantly with WebSocket-powered live updates and collaboration."))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .StarOutlined */ .L0Y, {
    style: {
      fontSize: '32px',
      color: '#722ed1',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 4
  }, "Export & Save"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Save your app configuration and export as JSON for further development."))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center',
      marginTop: '60px',
      padding: '40px',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      borderRadius: '12px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 2
  }, "Ready to Build Your App?"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, {
    style: {
      fontSize: '18px',
      marginBottom: '30px'
    }
  }, "Start with our MVP version and create your first app in minutes."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__/* .Link */ .N_, {
    to: "/mvp"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary",
    size: "large",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .RocketOutlined */ .PKb, null),
    style: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      border: 'none',
      height: '50px',
      padding: '0 40px',
      fontSize: '16px'
    }
  }, "Launch MVP Builder"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePageMVP);

/***/ })

}]);