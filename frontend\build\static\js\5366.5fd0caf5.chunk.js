"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5366],{

/***/ 23774:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   timeSync: () => (/* binding */ timeSync)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);




var TimeSyncService = /*#__PURE__*/function () {
  function TimeSyncService() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(this, TimeSyncService);
    this.offset = 0;
    this.syncAttempts = 5; // Number of sync attempts to average
    this.syncing = false;
  }
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(TimeSyncService, [{
    key: "synchronize",
    value: function () {
      var _synchronize = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
        var offsets, i, t0, response, t3, _yield$response$json, serverTime, t1, t2, offset, sortedOffsets, validOffsets;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              if (!this.syncing) {
                _context.next = 2;
                break;
              }
              return _context.abrupt("return");
            case 2:
              this.syncing = true;
              offsets = [];
              i = 0;
            case 5:
              if (!(i < this.syncAttempts)) {
                _context.next = 30;
                break;
              }
              t0 = Date.now();
              _context.prev = 7;
              _context.next = 10;
              return fetch('/api/time');
            case 10:
              response = _context.sent;
              t3 = Date.now();
              _context.next = 14;
              return response.json();
            case 14:
              _yield$response$json = _context.sent;
              serverTime = _yield$response$json.serverTime;
              t1 = new Date(serverTime).getTime();
              t2 = t1; // Server processing time is negligible
              // Calculate offset using Network Time Protocol formula
              offset = (t1 - t0 + (t2 - t3)) / 2;
              offsets.push(offset);
              _context.next = 22;
              return new Promise(function (resolve) {
                return setTimeout(resolve, 100);
              });
            case 22:
              _context.next = 27;
              break;
            case 24:
              _context.prev = 24;
              _context.t0 = _context["catch"](7);
              console.error('Time sync failed:', _context.t0);
            case 27:
              i++;
              _context.next = 5;
              break;
            case 30:
              // Calculate average offset, excluding outliers
              sortedOffsets = [].concat(offsets).sort(function (a, b) {
                return a - b;
              });
              validOffsets = sortedOffsets.slice(1, -1); // Remove highest and lowest
              this.offset = validOffsets.reduce(function (sum, val) {
                return sum + val;
              }, 0) / validOffsets.length;
              this.syncing = false;
              console.log('Time synchronized, offset:', this.offset, 'ms');
            case 35:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[7, 24]]);
      }));
      function synchronize() {
        return _synchronize.apply(this, arguments);
      }
      return synchronize;
    }()
  }, {
    key: "now",
    value: function now() {
      return new Date(Date.now() + this.offset);
    }
  }, {
    key: "getServerTime",
    value: function getServerTime(clientTime) {
      return new Date(clientTime.getTime() + this.offset);
    }
  }, {
    key: "getClientTime",
    value: function getClientTime(serverTime) {
      return new Date(serverTime.getTime() - this.offset);
    }
  }]);
}();
var timeSync = new TimeSyncService();

/***/ }),

/***/ 34816:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addComponent: () => (/* binding */ addComponent),
/* harmony export */   addLayout: () => (/* binding */ addLayout),
/* harmony export */   addTheme: () => (/* binding */ addTheme),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   removeComponent: () => (/* binding */ removeComponent),
/* harmony export */   removeLayout: () => (/* binding */ removeLayout),
/* harmony export */   removeTheme: () => (/* binding */ removeTheme),
/* harmony export */   setActiveTheme: () => (/* binding */ setActiveTheme),
/* harmony export */   setCurrentView: () => (/* binding */ setCurrentView),
/* harmony export */   togglePreviewMode: () => (/* binding */ togglePreviewMode),
/* harmony export */   toggleSidebar: () => (/* binding */ toggleSidebar),
/* harmony export */   updateComponent: () => (/* binding */ updateComponent),
/* harmony export */   updateLayout: () => (/* binding */ updateLayout),
/* harmony export */   updateTheme: () => (/* binding */ updateTheme),
/* harmony export */   websocketConnected: () => (/* binding */ websocketConnected),
/* harmony export */   websocketDisconnected: () => (/* binding */ websocketDisconnected),
/* harmony export */   websocketMessageReceived: () => (/* binding */ websocketMessageReceived)
/* harmony export */ });
/* harmony import */ var _actions_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4318);
/**
 * Compatibility layer for the minimal-store.js
 * This file re-exports actions from the main Redux store to maintain backward compatibility
 *
 * IMPORTANT: This file is designed to avoid circular dependencies by directly defining
 * action creators rather than importing them from other files.
 */

// Import action types directly to avoid circular dependencies


// Define action creators directly to avoid circular dependencies
// Component actions
var addComponent = function addComponent(component) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_COMPONENT */ .oz || 'ADD_COMPONENT',
    payload: component
  };
};
var updateComponent = function updateComponent(componentId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_COMPONENT */ .ei || 'UPDATE_COMPONENT',
    payload: {
      id: componentId,
      props: props
    }
  };
};
var removeComponent = function removeComponent(componentId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_COMPONENT */ .xS || 'REMOVE_COMPONENT',
    payload: {
      id: componentId
    }
  };
};

// Layout actions
var addLayout = function addLayout(layout) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_LAYOUT */ .vs || 'ADD_LAYOUT',
    payload: layout
  };
};
var updateLayout = function updateLayout(layoutId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_LAYOUT */ .Pe || 'UPDATE_LAYOUT',
    payload: {
      id: layoutId,
      props: props
    }
  };
};
var removeLayout = function removeLayout(layoutId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_LAYOUT */ .gV || 'REMOVE_LAYOUT',
    payload: {
      id: layoutId
    }
  };
};

// Theme actions
var addTheme = function addTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_THEME */ .U_ || 'ADD_THEME',
    payload: theme
  };
};
var updateTheme = function updateTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_THEME */ .gk || 'UPDATE_THEME',
    payload: theme
  };
};
var removeTheme = function removeTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_THEME */ .D || 'REMOVE_THEME',
    payload: {
      id: themeId
    }
  };
};
var setActiveTheme = function setActiveTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .SET_ACTIVE_THEME */ .wH || 'SET_ACTIVE_THEME',
    payload: themeId
  };
};

// WebSocket actions
var websocketConnected = function websocketConnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_CONNECTED */ .Kg || 'WEBSOCKET_CONNECTED'
  };
};
var websocketDisconnected = function websocketDisconnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_DISCONNECTED */ .co || 'WEBSOCKET_DISCONNECTED'
  };
};
var websocketMessageReceived = function websocketMessageReceived(message) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WS_MESSAGE_RECEIVED */ .ZH || 'WEBSOCKET_MESSAGE_RECEIVED',
    payload: message
  };
};

// UI actions
var toggleSidebar = function toggleSidebar() {
  return {
    type: 'TOGGLE_SIDEBAR'
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: 'SET_CURRENT_VIEW',
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: 'TOGGLE_PREVIEW_MODE'
  };
};

// Re-export all actions for backward compatibility


// Export a dummy store for backward compatibility
var dummyStore = {
  getState: function getState() {
    return {};
  },
  dispatch: function dispatch() {},
  subscribe: function subscribe() {
    return function () {};
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dummyStore);

/***/ }),

/***/ 65366:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ enhanced_FixedWebSocketManager)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
// EXTERNAL MODULE: ./src/design-system/theme.js
var theme = __webpack_require__(86020);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./src/config/env.js
var env = __webpack_require__(26390);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./src/serviceWorkerRegistration.js
var serviceWorkerRegistration = __webpack_require__(77362);
;// ./src/utils/webSocketErrorHandler.js


/**
 * WebSocket Error Handler
 *
 * This utility provides functions to detect and fix WebSocket connection issues,
 * particularly those related to service worker interference.
 */



/**
 * Error codes that might indicate service worker interference
 */
var SERVICE_WORKER_RELATED_ERRORS = ['NS_ERROR_CORRUPTED_CONTENT', 'NS_ERROR_NET_INADEQUATE_SECURITY', 'SECURITY_ERR', 'NetworkError', 'network error response', 'fetch event', 'resulted in network error', 'promise was rejected'];

/**
 * Check if an error is likely caused by service worker interference
 * @param {Error|string} error - The error object or message
 * @returns {boolean} - True if the error is likely caused by service worker
 */
function isServiceWorkerRelatedError(error) {
  var errorMessage = error instanceof Error ? error.message : String(error);
  return SERVICE_WORKER_RELATED_ERRORS.some(function (code) {
    return errorMessage.includes(code) || errorMessage.toLowerCase().includes('serviceworker') || errorMessage.toLowerCase().includes('service worker');
  });
}

/**
 * Handle WebSocket connection errors
 * @param {Error|string} error - The error that occurred
 * @returns {Promise<boolean>} - True if the error was handled
 */
function handleWebSocketError(_x) {
  return _handleWebSocketError.apply(this, arguments);
}

/**
 * Add global error handler for WebSocket issues
 */
function _handleWebSocketError() {
  _handleWebSocketError = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(error) {
    var shouldFix;
    return regenerator_default().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          console.error('WebSocket error occurred:', error);
          if (!isServiceWorkerRelatedError(error)) {
            _context.next = 6;
            break;
          }
          console.log('Detected service worker related WebSocket error');

          // Show a user-friendly error message
          shouldFix = window.confirm('A service worker is interfering with the WebSocket connection. ' + 'Would you like to fix this issue? This will refresh the page.');
          if (!shouldFix) {
            _context.next = 6;
            break;
          }
          return _context.abrupt("return", (0,serviceWorkerRegistration/* fixWebSocketIssues */.jY)());
        case 6:
          return _context.abrupt("return", false);
        case 7:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _handleWebSocketError.apply(this, arguments);
}
function setupGlobalWebSocketErrorHandler() {
  // Listen for unhandled errors that might be WebSocket related
  window.addEventListener('error', function (event) {
    if (event.message && (event.message.includes('WebSocket') || isServiceWorkerRelatedError(event.message))) {
      console.log('Caught global WebSocket error:', event.message);
      handleWebSocketError(event.message);
    }
  });

  // Listen for unhandled promise rejections
  window.addEventListener('unhandledrejection', function (event) {
    var error = event.reason;
    if (error && (String(error).includes('WebSocket') || isServiceWorkerRelatedError(error))) {
      console.log('Caught unhandled WebSocket promise rejection:', error);
      handleWebSocketError(error);
    }
  });

  // Patch the WebSocket constructor to catch errors
  var OriginalWebSocket = window.WebSocket;
  window.WebSocket = function (url, protocols) {
    try {
      return new OriginalWebSocket(url, protocols);
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      if (isServiceWorkerRelatedError(error)) {
        handleWebSocketError(error);
      }
      throw error;
    }
  };

  // Copy all properties from the original WebSocket
  for (var prop in OriginalWebSocket) {
    if (OriginalWebSocket.hasOwnProperty(prop)) {
      window.WebSocket[prop] = OriginalWebSocket[prop];
    }
  }
  window.WebSocket.prototype = OriginalWebSocket.prototype;
  console.log('Global WebSocket error handler set up');
}

/**
 * Specifically handle the "fetch event resulted in network error" issue
 * @param {string} url - The URL that caused the error
 * @returns {Promise<boolean>} - True if the error was handled
 */
function handleFetchEventNetworkError(_x2) {
  return _handleFetchEventNetworkError.apply(this, arguments);
}
function _handleFetchEventNetworkError() {
  _handleFetchEventNetworkError = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(url) {
    var isWebSocketUrl, shouldFix;
    return regenerator_default().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          console.error("Fetch event for \"".concat(url, "\" resulted in network error response, the promise was rejected"));

          // Check if this is a WebSocket URL
          isWebSocketUrl = url.startsWith('ws:') || url.startsWith('wss:') || url.includes('/ws/') || url.includes('/socket');
          if (!isWebSocketUrl) {
            _context2.next = 10;
            break;
          }
          console.log('Detected WebSocket URL with fetch event network error');

          // Show a user-friendly error message
          shouldFix = window.confirm('A service worker is preventing WebSocket connections. ' + 'Would you like to fix this issue? This will refresh the page.');
          if (!shouldFix) {
            _context2.next = 10;
            break;
          }
          _context2.next = 8;
          return (0,serviceWorkerRegistration/* forceCleanServiceWorkers */.hl)();
        case 8:
          // If that doesn't work, try to fix WebSocket issues
          setTimeout(function () {
            (0,serviceWorkerRegistration/* fixWebSocketIssues */.jY)();
          }, 1000);
          return _context2.abrupt("return", true);
        case 10:
          return _context2.abrupt("return", false);
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return _handleFetchEventNetworkError.apply(this, arguments);
}
/* harmony default export */ const webSocketErrorHandler = ({
  handleWebSocketError: handleWebSocketError,
  isServiceWorkerRelatedError: isServiceWorkerRelatedError,
  setupGlobalWebSocketErrorHandler: setupGlobalWebSocketErrorHandler,
  handleFetchEventNetworkError: handleFetchEventNetworkError
});
;// ./src/services/WebSocketClient.js





function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * WebSocketClient
 *
 * A robust WebSocket client implementation with automatic reconnection,
 * event handling, and message queuing.
 */



// WebSocket connection states
var ConnectionState = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
  RECONNECTING: 4
};

/**
 * Utility function to capture and format a stack trace
 * @returns {string} Formatted stack trace
 */
function captureStackTrace() {
  var error = new Error();
  Error.captureStackTrace(error, captureStackTrace);
  return error.stack;
}
var WebSocketClient = /*#__PURE__*/function () {
  /**
   * Create a new WebSocketClient
   * @param {Object} options - Configuration options
   * @param {string} options.url - WebSocket URL (optional, can be set later)
   * @param {number} options.reconnectInterval - Initial reconnect interval in ms (default: 1000)
   * @param {number} options.maxReconnectInterval - Maximum reconnect interval in ms (default: 30000)
   * @param {number} options.reconnectDecay - Exponential backoff factor (default: 1.5)
   * @param {number} options.maxReconnectAttempts - Maximum reconnect attempts (default: 10)
   * @param {boolean} options.debug - Enable debug logging (default: false)
   * @param {boolean} options.automaticOpen - Automatically open connection (default: true)
   */
  function WebSocketClient() {
    var _this = this;
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0,classCallCheck/* default */.A)(this, WebSocketClient);
    /**
     * Enhanced logging system
     * @private
     */
    (0,defineProperty/* default */.A)(this, "logger", {
      _formatMessage: function _formatMessage(level) {
        var timestamp = new Date().toISOString();
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        return ["[".concat(timestamp, "][WebSocketClient:").concat(_this.connectionId, "][").concat(level, "]")].concat(args);
      },
      debug: function debug() {
        if (_this.currentLogLevel <= _this.logLevels.DEBUG) {
          var _console, _this$logger;
          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          (_console = console).debug.apply(_console, (0,toConsumableArray/* default */.A)((_this$logger = _this.logger)._formatMessage.apply(_this$logger, ['DEBUG'].concat(args))));
        }
      },
      info: function info() {
        if (_this.currentLogLevel <= _this.logLevels.INFO) {
          var _console2, _this$logger2;
          for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
            args[_key3] = arguments[_key3];
          }
          (_console2 = console).info.apply(_console2, (0,toConsumableArray/* default */.A)((_this$logger2 = _this.logger)._formatMessage.apply(_this$logger2, ['INFO'].concat(args))));
        }
      },
      warn: function warn() {
        if (_this.currentLogLevel <= _this.logLevels.WARN) {
          var _console3, _this$logger3;
          for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
            args[_key4] = arguments[_key4];
          }
          (_console3 = console).warn.apply(_console3, (0,toConsumableArray/* default */.A)((_this$logger3 = _this.logger)._formatMessage.apply(_this$logger3, ['WARN'].concat(args))));
        }
      },
      error: function error() {
        if (_this.currentLogLevel <= _this.logLevels.ERROR) {
          var _console4, _this$logger4;
          for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {
            args[_key5] = arguments[_key5];
          }
          (_console4 = console).error.apply(_console4, (0,toConsumableArray/* default */.A)((_this$logger4 = _this.logger)._formatMessage.apply(_this$logger4, ['ERROR'].concat(args))));
        }
      },
      group: function group(label) {
        if (_this.debug) {
          console.group("[WebSocketClient:".concat(_this.connectionId, "] ").concat(label));
        }
      },
      groupEnd: function groupEnd() {
        if (_this.debug) {
          console.groupEnd();
        }
      }
    });
    /**
     * Set the connection state and dispatch state change event
     * @private
     * @param {number} state - New connection state
     */
    (0,defineProperty/* default */.A)(this, "setConnectionState", function (state) {
      var previousState = _this.connectionState;
      _this.connectionState = state;
      _this.dispatchEvent('state_change', {
        previousState: previousState,
        currentState: state
      });
    });
    /**
     * Open the WebSocket connection
     * @param {string} url - WebSocket URL (optional if already set)
     * @returns {WebSocketClient} this instance for chaining
     */
    (0,defineProperty/* default */.A)(this, "open", function (url) {
      console.log('open method called with url:', url);
      console.log('this in open method before any operations:', _this);

      // Update URL if provided
      if (url) {
        _this.url = url;
      }

      // Validate URL
      if (!_this.url) {
        console.error('WebSocketClient: URL not set in open method');
        throw new Error('WebSocket URL not set');
      }
      console.log('URL after validation:', _this.url);

      // Close existing connection if any
      if (_this.socket && (_this.socket.readyState === WebSocket.OPEN || _this.socket.readyState === WebSocket.CONNECTING)) {
        _this.log('Closing existing connection before opening a new one');
        _this.socket.close();
      }

      // Log the URL we're connecting to for debugging
      _this.log("Opening connection to ".concat(_this.url));
      console.log("WebSocketClient: Connecting to ".concat(_this.url));

      // Log network status
      console.log("WebSocketClient: Network status - Online: ".concat(navigator.onLine));

      // Check if the URL is valid
      try {
        var urlObj = new URL(_this.url);
        console.log("WebSocketClient: URL parsed successfully - Protocol: ".concat(urlObj.protocol, ", Host: ").concat(urlObj.host, ", Path: ").concat(urlObj.pathname));
      } catch (error) {
        console.error("WebSocketClient: Invalid URL - ".concat(_this.url), error);
      }
      _this.setConnectionState(ConnectionState.CONNECTING);
      try {
        console.log('Creating new WebSocket object with URL:', _this.url);
        console.log('this before WebSocket creation:', _this);

        // Create new WebSocket without explicit protocols to improve compatibility
        _this.socket = new WebSocket(_this.url);

        // Set binary type to arraybuffer for more reliable binary data handling
        _this.socket.binaryType = 'arraybuffer';
        console.log('WebSocket object created:', _this.socket);
        console.log('WebSocket readyState:', _this.socket.readyState);

        // Set up connection timeout
        console.log('Setting up connection timeout of', _this.connectionTimeout, 'ms');
        _this.connectionTimeoutId = setTimeout(function () {
          console.log('Connection timeout callback triggered');
          console.log('this in timeout callback:', _this);
          if (_this.socket && _this.socket.readyState !== WebSocket.OPEN) {
            console.warn("WebSocketClient: Connection timeout after ".concat(_this.connectionTimeout, "ms"));
            _this.socket.close(4000, 'Connection timeout');
            _this.dispatchEvent('error', new Error('Connection timeout'));
          }
        }, _this.connectionTimeout);

        // Log event handlers before binding
        console.log('Event handlers before binding:');
        console.log('onOpen:', _this.onOpen);
        console.log('onMessage:', _this.onMessage);
        console.log('onClose:', _this.onClose);
        console.log('onError:', _this.onError);

        // Set up event handlers (already bound in constructor)
        _this.socket.onopen = _this.onOpen;
        _this.socket.onmessage = _this.onMessage;
        _this.socket.onclose = _this.onClose;
        _this.socket.onerror = _this.onError;
        console.log('Event handlers after binding:');
        console.log('socket.onopen:', _this.socket.onopen);
        console.log('socket.onmessage:', _this.socket.onmessage);
        console.log('socket.onclose:', _this.socket.onclose);
        console.log('socket.onerror:', _this.socket.onerror);
        return _this;
      } catch (error) {
        _this.log('Error creating WebSocket:', error);

        // Capture stack trace for debugging
        var stack = captureStackTrace();
        console.error('WebSocketClient: Error creating connection:', error);
        console.error('WebSocketClient: Connection error stack trace:', stack);

        // Add stack trace to error object
        if (error && (0,esm_typeof/* default */.A)(error) === 'object') {
          error.stack = error.stack || stack;
        }

        // Check if this is a service worker related error
        if (isServiceWorkerRelatedError(error)) {
          console.warn('WebSocketClient: Detected service worker related error');
          // Handle the service worker error
          handleWebSocketError(error).then(function (fixed) {
            if (!fixed) {
              // If not fixed by the handler, dispatch error and try to reconnect
              _this.dispatchEvent('error', error);
              _this.reconnect();
            }
          });
        } else {
          // Regular error handling
          _this.dispatchEvent('error', error);
          _this.reconnect();
        }
        return _this;
      }
    });
    /**
     * Close the WebSocket connection
     * @param {number} code - Close code (default: 1000)
     * @param {string} reason - Close reason
     * @returns {WebSocketClient} this instance for chaining
     */
    (0,defineProperty/* default */.A)(this, "close", function () {
      var code = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1000;
      var reason = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Normal closure';
      if (_this.socket) {
        _this.log("Closing connection with code ".concat(code, ": ").concat(reason));

        // Stop heartbeat before closing
        _this.log('Stopping heartbeat');
        _this.stopHeartbeat();
        _this.setConnectionState(ConnectionState.CLOSING);
        _this.socket.close(code, reason);
      }
      return _this;
    });
    /**
     * Clean up resources and event listeners
     * @private
     */
    (0,defineProperty/* default */.A)(this, "cleanup", function () {
      // Remove the beforeunload event listener
      window.removeEventListener('beforeunload', _this.handleBeforeUnload);

      // Clear any pending timeouts
      if (_this.connectionTimeoutId) {
        clearTimeout(_this.connectionTimeoutId);
        _this.connectionTimeoutId = null;
      }
      if (_this.reconnectTimeoutId) {
        clearTimeout(_this.reconnectTimeoutId);
        _this.reconnectTimeoutId = null;
      }

      // Stop heartbeat
      _this.stopHeartbeat();
    });
    /**
     * Start sending heartbeat messages to keep the connection alive
     * @private
     */
    (0,defineProperty/* default */.A)(this, "startHeartbeat", function () {
      // Only start heartbeat if enabled and connection is open
      if (!_this.enableHeartbeat || _this.connectionState !== ConnectionState.OPEN) {
        return;
      }

      // Clear any existing heartbeat timeout
      if (_this.heartbeatTimeoutId) {
        clearTimeout(_this.heartbeatTimeoutId);
      }

      // Schedule the next heartbeat
      _this.heartbeatTimeoutId = setTimeout(function () {
        // Only send if connection is still open
        if (_this.connectionState === ConnectionState.OPEN) {
          try {
            // Update timestamp in heartbeat message
            var heartbeat = (0,esm_typeof/* default */.A)(_this.heartbeatMessage) === 'object' ? _objectSpread(_objectSpread({}, _this.heartbeatMessage), {}, {
              timestamp: new Date().toISOString()
            }) : _this.heartbeatMessage;

            // Send the heartbeat
            _this.send(heartbeat);
            _this.logger.debug('Heartbeat sent', {
              timestamp: new Date().toISOString()
            });

            // Schedule the next heartbeat
            _this.startHeartbeat();
          } catch (error) {
            _this.logger.error('Failed to send heartbeat', {
              error: error
            });
            // Try to reconnect if sending heartbeat fails
            _this.reconnect();
          }
        }
      }, _this.heartbeatInterval);
    });
    /**
     * Stop sending heartbeat messages
     * @private
     */
    (0,defineProperty/* default */.A)(this, "stopHeartbeat", function () {
      if (_this.heartbeatTimeoutId) {
        clearTimeout(_this.heartbeatTimeoutId);
        _this.heartbeatTimeoutId = null;
      }
    });
    /**
     * Send a message through the WebSocket
     * @param {Object|string|ArrayBuffer|Blob} data - Data to send
     * @returns {boolean} true if sent, false if queued or failed
     */
    (0,defineProperty/* default */.A)(this, "send", function (data) {
      _this.logger.group('Sending Message');
      try {
        // Validate the data before sending
        if (data === undefined || data === null) {
          _this.logger.error('Cannot send undefined or null data');
          return false;
        }

        // Prepare the message based on data type
        var message;
        if (data instanceof ArrayBuffer || data instanceof Blob) {
          // Binary data can be sent directly
          message = data;
          _this.logger.debug('Sending binary data', {
            type: data instanceof ArrayBuffer ? 'ArrayBuffer' : 'Blob',
            size: data instanceof ArrayBuffer ? data.byteLength : data.size
          });
        } else if ((0,esm_typeof/* default */.A)(data) === 'object') {
          // Convert objects to JSON strings
          try {
            message = JSON.stringify(data);
          } catch (jsonError) {
            _this.logger.error('Failed to stringify object', {
              error: jsonError
            });
            return false;
          }
        } else {
          // Use data as is for strings and other primitives
          message = data;
        }

        // Check connection state before sending
        if (_this.connectionState === ConnectionState.OPEN) {
          // Check if the socket is actually open
          if (_this.socket && _this.socket.readyState === WebSocket.OPEN) {
            // Send the message
            _this.socket.send(message);

            // Log success with appropriate details
            if (typeof message === 'string') {
              _this.logger.debug('Message sent successfully', {
                dataType: (0,esm_typeof/* default */.A)(data),
                dataLength: message.length,
                preview: message.length > 100 ? message.substring(0, 97) + '...' : message
              });
            } else {
              _this.logger.debug('Binary message sent successfully', {
                dataType: message instanceof ArrayBuffer ? 'ArrayBuffer' : 'Blob',
                dataSize: message instanceof ArrayBuffer ? message.byteLength : message.size
              });
            }
            return true;
          } else {
            // Socket is not actually open despite the connection state
            _this.logger.warn('Socket not open despite connection state', {
              connectionState: _this.connectionState,
              socketReadyState: _this.socket ? _this.socket.readyState : 'no socket',
              messageQueued: true
            });

            // Queue the message
            _this.messageQueue.push(message);
            return false;
          }
        } else {
          // Connection is not open
          _this.logger.warn('Cannot send - connection not open', {
            connectionState: _this.connectionState,
            messageQueued: true
          });

          // Queue the message
          _this.messageQueue.push(message);
          return false;
        }
      } catch (error) {
        _this.logger.error('Failed to send message', {
          error: error,
          errorMessage: error.message,
          errorStack: error.stack,
          data: (0,esm_typeof/* default */.A)(data) === 'object' ? 'object' : data,
          connectionState: _this.connectionState
        });
        return false;
      } finally {
        _this.logger.groupEnd();
      }
    });
    /**
     * Attempt to reconnect
     * @private
     */
    (0,defineProperty/* default */.A)(this, "reconnect", function () {
      // Skip if already reconnecting or closing
      if (_this.connectionState === ConnectionState.RECONNECTING || _this.connectionState === ConnectionState.CLOSING) {
        return;
      }

      // Check if max reconnect attempts reached
      if (_this.reconnectAttempts >= _this.maxReconnectAttempts) {
        _this.log('Max reconnect attempts reached');
        _this.dispatchEvent('reconnect_failed');
        return;
      }
      _this.reconnectAttempts++;
      _this.setConnectionState(ConnectionState.RECONNECTING);

      // Calculate reconnect delay with exponential backoff
      var delay = Math.min(_this.reconnectInterval * Math.pow(_this.reconnectDecay, _this.reconnectAttempts - 1), _this.maxReconnectInterval);
      _this.log("Reconnecting in ".concat(delay, "ms (attempt ").concat(_this.reconnectAttempts, "/").concat(_this.maxReconnectAttempts, ")"));
      _this.dispatchEvent('reconnect_attempt', {
        attempt: _this.reconnectAttempts,
        delay: delay
      });

      // Schedule reconnect
      setTimeout(function () {
        _this.log("Attempting reconnect ".concat(_this.reconnectAttempts, "/").concat(_this.maxReconnectAttempts));
        try {
          _this.open();
        } catch (error) {
          _this.log('Reconnect error:', error);
          _this.dispatchEvent('reconnect_error', error);
          _this.reconnect();
        }
      }, delay);
    });
    /**
     * Handle WebSocket open event
     * @private
     * @param {Event} event - WebSocket event
     */
    (0,defineProperty/* default */.A)(this, "onOpen", function (event) {
      console.log('onOpen event handler called with event:', event);
      console.log('this in onOpen handler:', _this);
      console.log('Current socket:', _this.socket);
      console.log('Current connection state:', _this.connectionState);
      _this.log('Connection opened');

      // Clear connection timeout
      if (_this.connectionTimeoutId) {
        console.log('Clearing connection timeout:', _this.connectionTimeoutId);
        clearTimeout(_this.connectionTimeoutId);
        _this.connectionTimeoutId = null;
      }
      console.log('Setting connection state to OPEN');
      _this.setConnectionState(ConnectionState.OPEN);
      _this.reconnectAttempts = 0;

      // Process queued messages
      if (_this.messageQueue.length > 0) {
        _this.log("Processing ".concat(_this.messageQueue.length, " queued messages"));

        // Create a copy of the queue to avoid issues if new messages are queued during processing
        var queueCopy = (0,toConsumableArray/* default */.A)(_this.messageQueue);
        _this.messageQueue = [];

        // Process each message
        var _iterator = _createForOfIteratorHelper(queueCopy),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var message = _step.value;
            try {
              if (_this.socket && _this.socket.readyState === WebSocket.OPEN) {
                _this.socket.send(message);
                _this.log('Queued message sent successfully');
              } else {
                _this.log('Socket not open while processing queue, re-queuing message');
                _this.messageQueue.push(message);
              }
            } catch (error) {
              _this.log('Error sending queued message:', error);
              // Re-queue the message if it's a temporary error
              if (error.name !== 'SyntaxError' && error.name !== 'TypeError') {
                _this.messageQueue.push(message);
              }
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
        _this.log("Queue processing complete. ".concat(_this.messageQueue.length, " messages re-queued"));
      }

      // Start heartbeat to keep connection alive
      if (_this.enableHeartbeat) {
        _this.log('Starting heartbeat');
        _this.startHeartbeat();
      }

      // Dispatch event
      _this.dispatchEvent('open', event);
    });
    /**
     * Handle WebSocket message event
     * @private
     * @param {MessageEvent} event - WebSocket message event
     */
    (0,defineProperty/* default */.A)(this, "onMessage", function (event) {
      try {
        // Check if the data is a Blob or ArrayBuffer (binary data)
        if (event.data instanceof Blob || event.data instanceof ArrayBuffer) {
          _this.log('Binary message received');
          // Handle binary data - convert to text if needed
          _this.handleBinaryMessage(event);
          return;
        }

        // Log the received message (only for text data)
        _this.log('Message received:', typeof event.data === 'string' ? event.data.substring(0, 100) + (event.data.length > 100 ? '...' : '') : event.data);

        // Validate that the data is not corrupted
        if (typeof event.data !== 'string') {
          _this.log('Non-string data received:', (0,esm_typeof/* default */.A)(event.data));
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: event.data
          });
          return;
        }

        // Check for empty or invalid data
        if (!event.data || event.data.trim() === '') {
          _this.log('Empty message received');
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: ''
          });
          return;
        }
        try {
          // Parse JSON if possible
          var data = JSON.parse(event.data);
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: data
          });
        } catch (error) {
          _this.log('Error parsing JSON message:', error);
          // Dispatch raw data if not JSON
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: event.data,
            parseError: error.message
          });
        }
      } catch (error) {
        _this.log('Error in onMessage handler:', error);
        // Dispatch an error event
        _this.dispatchEvent('error', {
          message: 'Error processing WebSocket message',
          error: error.message,
          originalEvent: event
        });
      }
    });
    /**
     * Handle binary WebSocket messages
     * @private
     * @param {MessageEvent} event - WebSocket message event with binary data
     */
    (0,defineProperty/* default */.A)(this, "handleBinaryMessage", function (event) {
      var reader = new FileReader();
      reader.onload = function () {
        try {
          var text = reader.result;
          _this.log('Binary message converted to text:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
          try {
            // Try to parse as JSON
            var data = JSON.parse(text);
            _this.dispatchEvent('message', {
              originalEvent: event,
              data: data,
              binary: true
            });
          } catch (error) {
            // Dispatch raw text if not JSON
            _this.dispatchEvent('message', {
              originalEvent: event,
              data: text,
              binary: true,
              parseError: error.message
            });
          }
        } catch (error) {
          _this.log('Error processing binary message:', error);
          _this.dispatchEvent('error', {
            message: 'Error processing binary WebSocket message',
            error: error.message,
            originalEvent: event
          });
        }
      };
      reader.onerror = function (error) {
        _this.log('Error reading binary message:', error);
        _this.dispatchEvent('error', {
          message: 'Error reading binary WebSocket message',
          error: error.message,
          originalEvent: event
        });
      };

      // Read the binary data as text
      if (event.data instanceof Blob) {
        reader.readAsText(event.data);
      } else if (event.data instanceof ArrayBuffer) {
        var text = new TextDecoder().decode(event.data);
        try {
          var data = JSON.parse(text);
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: data,
            binary: true
          });
        } catch (error) {
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: text,
            binary: true,
            parseError: error.message
          });
        }
      }
    });
    /**
     * Handle WebSocket close event
     * @private
     * @param {CloseEvent} event - WebSocket close event
     */
    (0,defineProperty/* default */.A)(this, "onClose", function (event) {
      // Create a more detailed close information object
      var closeInfo = {
        code: event.code,
        reason: event.reason || 'No reason provided',
        wasClean: event.wasClean,
        timestamp: new Date().toISOString(),
        url: _this.url,
        networkOnline: navigator.onLine,
        connectionState: _this.connectionState
      };

      // Clear connection timeout
      if (_this.connectionTimeoutId) {
        clearTimeout(_this.connectionTimeoutId);
        _this.connectionTimeoutId = null;
      }
      _this.log("Connection closed: ".concat(closeInfo.code, " ").concat(closeInfo.reason, " (clean: ").concat(closeInfo.wasClean, ")"));
      _this.setConnectionState(ConnectionState.CLOSED);

      // Dispatch event with detailed information
      _this.dispatchEvent('close', closeInfo);

      // Clean up resources
      _this.cleanup();

      // Determine if we should reconnect
      var isNormalClosure = event.code === 1000 || event.code === 1001;
      var isAbnormalClosure = event.code === 1006;
      var isServerError = event.code >= 1011 && event.code <= 1015;

      // Enhanced handling for code 1006 (abnormal closure)
      if (isAbnormalClosure) {
        console.warn('WebSocketClient: Abnormal closure (code 1006) detected');
        console.log('WebSocketClient: Network status:', navigator.onLine ? 'Online' : 'Offline');

        // Check if we're online
        if (!navigator.onLine) {
          console.warn('WebSocketClient: Network appears to be offline, waiting for online status');

          // Add event listener for when we come back online
          var _onlineHandler = function onlineHandler() {
            console.log('WebSocketClient: Network is back online, attempting to reconnect');
            window.removeEventListener('online', _onlineHandler);
            setTimeout(function () {
              return _this.reconnect();
            }, 1000); // Wait a second for network to stabilize
          };
          window.addEventListener('online', _onlineHandler);
          return; // Don't attempt to reconnect now
        }

        // If we're online, try to reconnect with a short delay
        console.log('WebSocketClient: Network is online, attempting immediate reconnect');
        _this.reconnectInterval = 1000; // Use a short interval for first attempt
        setTimeout(function () {
          return _this.reconnect();
        }, 250); // Try almost immediately
        return;
      }

      // Handle server errors with exponential backoff
      if (isServerError) {
        console.warn("WebSocketClient: Server error (code ".concat(event.code, ") detected, using exponential backoff"));
        // Use current reconnect interval (will increase with each attempt)
        _this.reconnect();
        return;
      }

      // Handle other non-normal closures
      if (!isNormalClosure) {
        console.log("WebSocketClient: Non-normal closure (code ".concat(event.code, "), scheduling reconnect"));
        _this.reconnect();
      } else {
        console.log("WebSocketClient: Not reconnecting after clean close (".concat(event.code, ")"));
      }
    });
    /**
     * Handle WebSocket error event
     * @private
     * @param {Event} event - WebSocket error event
     */
    (0,defineProperty/* default */.A)(this, "onError", function (event) {
      // Safely log error details
      try {
        console.log('WebSocket error occurred:', event);

        // Clear connection timeout
        if (_this.connectionTimeoutId) {
          console.log('Clearing connection timeout in onError:', _this.connectionTimeoutId);
          clearTimeout(_this.connectionTimeoutId);
          _this.connectionTimeoutId = null;
        }

        // Check if we're online
        var isOnline = navigator && typeof navigator.onLine === 'boolean' ? navigator.onLine : true;

        // Create a more detailed error object
        var errorDetails = {
          originalEvent: event,
          message: 'WebSocket connection error',
          timestamp: new Date().toISOString(),
          connectionState: _this.connectionState,
          url: _this.url,
          reconnectAttempts: _this.reconnectAttempts,
          maxReconnectAttempts: _this.maxReconnectAttempts,
          browser: {
            online: isOnline
          }
        };

        // Log detailed error information
        console.error('WebSocketClient error details:', errorDetails);

        // Dispatch the error event with detailed information
        _this.dispatchEvent('error', errorDetails);

        // Attempt to reconnect if not already reconnecting
        if (_this.connectionState !== ConnectionState.RECONNECTING) {
          _this.reconnect();
        }
      } catch (handlerError) {
        // Last resort error handling
        console.error('Error in WebSocket error handler:', handlerError);
      }
    });
    /**
     * Handle beforeunload event to close the WebSocket connection cleanly
     * @private
     * @param {Event} _event - BeforeUnload event (unused)
     */
    (0,defineProperty/* default */.A)(this, "handleBeforeUnload", function (_event) {
      if (_this.socket && _this.connectionState === ConnectionState.OPEN) {
        _this.log('Page unloading, closing WebSocket connection');
        _this.close(1000, 'Page unloaded');
      }
    });
    console.log('WebSocketClient constructor called with options:', options);
    console.log('this in constructor:', this);

    // Validate URL if provided
    if (options.url) {
      try {
        // Validate URL format
        var urlObj = new URL(options.url);

        // Validate protocol
        if (urlObj.protocol !== 'ws:' && urlObj.protocol !== 'wss:') {
          console.warn("WebSocketClient: Invalid protocol - ".concat(urlObj.protocol, ". URL should use ws:// or wss://"));
          // Auto-correct the protocol if possible
          if (urlObj.protocol === 'http:') {
            options.url = options.url.replace('http:', 'ws:');
            console.log("WebSocketClient: Corrected URL to ".concat(options.url));
          } else if (urlObj.protocol === 'https:') {
            options.url = options.url.replace('https:', 'wss:');
            console.log("WebSocketClient: Corrected URL to ".concat(options.url));
          }
        }
      } catch (error) {
        console.error("WebSocketClient: Invalid URL - ".concat(options.url), error);
        // Don't throw here, just log the error and continue with null URL
        // The open method will validate again before connecting
        options.url = null;
      }
    }
    this.url = options.url || null;
    this.reconnectInterval = options.reconnectInterval || 1000;
    this.maxReconnectInterval = options.maxReconnectInterval || 30000;
    this.reconnectDecay = options.reconnectDecay || 1.5;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.debug = options.debug || false;
    this.automaticOpen = options.automaticOpen !== false;
    this.connectionTimeout = options.connectionTimeout || 5000; // Add connection timeout

    // Heartbeat configuration
    this.enableHeartbeat = options.enableHeartbeat !== false;
    this.heartbeatInterval = options.heartbeatInterval || 30000; // 30 seconds by default
    this.heartbeatMessage = options.heartbeatMessage || {
      type: 'ping',
      timestamp: new Date().toISOString()
    };
    this.heartbeatTimeoutId = null;

    // Enhanced logging configuration
    this.connectionId = Math.random().toString(36).substring(2, 11);
    this.logLevels = {
      DEBUG: 0,
      INFO: 1,
      WARN: 2,
      ERROR: 3
    };
    this.currentLogLevel = this.debug ? this.logLevels.DEBUG : this.logLevels.INFO;
    this.socket = null;
    this.connectionState = ConnectionState.CLOSED;
    this.reconnectAttempts = 0;
    this.messageQueue = [];
    this.eventListeners = {
      open: [],
      message: [],
      close: [],
      error: [],
      reconnect: [],
      reconnect_attempt: [],
      reconnect_error: [],
      reconnect_failed: [],
      state_change: []
    };

    // No need to bind methods when using arrow functions as class properties

    // Add beforeunload event listener to close the connection when the page is unloaded
    window.addEventListener('beforeunload', this.handleBeforeUnload);

    // Automatically open connection if URL is provided
    if (this.url && this.automaticOpen) {
      this.open();
    }
  }
  return (0,createClass/* default */.A)(WebSocketClient, [{
    key: "addEventListener",
    value:
    /**
     * Add an event listener
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @param {Object} options - Listener options
     * @param {boolean} options.once - Remove listener after first call
     * @returns {Function} Function to remove the listener
     */
    function addEventListener(type, listener) {
      var _this2 = this;
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      if (!this.eventListeners[type]) {
        this.eventListeners[type] = [];
      }
      var listenerObj = {
        callback: listener,
        once: options.once || false
      };
      this.eventListeners[type].push(listenerObj);

      // Return function to remove the listener
      return function () {
        return _this2.removeEventListener(type, listener);
      };
    }

    /**
     * Remove an event listener
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @returns {boolean} true if removed, false if not found
     */
  }, {
    key: "removeEventListener",
    value: function removeEventListener(type, listener) {
      if (!this.eventListeners[type]) {
        return false;
      }
      var initialLength = this.eventListeners[type].length;
      this.eventListeners[type] = this.eventListeners[type].filter(function (l) {
        return l.callback !== listener;
      });
      return initialLength !== this.eventListeners[type].length;
    }

    /**
     * Dispatch an event to all listeners
     * @private
     * @param {string} type - Event type
     * @param {*} data - Event data
     */
  }, {
    key: "dispatchEvent",
    value: function dispatchEvent(type, data) {
      if (!this.eventListeners[type]) {
        return;
      }

      // Create a copy of the listeners array to avoid issues if listeners are removed during iteration
      var listeners = (0,toConsumableArray/* default */.A)(this.eventListeners[type]);

      // Call each listener
      listeners.forEach(function (listener) {
        try {
          listener.callback(data);
        } catch (error) {
          console.error("Error in ".concat(type, " listener:"), error);
        }
      });

      // Remove one-time listeners
      this.eventListeners[type] = this.eventListeners[type].filter(function (listener) {
        return !listener.once;
      });
    }

    /**
     * Get the current connection state
     * @returns {number} Connection state
     */
  }, {
    key: "getState",
    value: function getState() {
      return this.connectionState;
    }

    /**
     * Check if the connection is open
     * @returns {boolean} true if open
     */
  }, {
    key: "isOpen",
    value: function isOpen() {
      return this.connectionState === ConnectionState.OPEN;
    }

    /**
     * Check if the connection is connecting
     * @returns {boolean} true if connecting
     */
  }, {
    key: "isConnecting",
    value: function isConnecting() {
      return this.connectionState === ConnectionState.CONNECTING || this.connectionState === ConnectionState.RECONNECTING;
    }

    /**
     * Check if the connection is closed
     * @returns {boolean} true if closed
     */
  }, {
    key: "isClosed",
    value: function isClosed() {
      return this.connectionState === ConnectionState.CLOSED || this.connectionState === ConnectionState.CLOSING;
    }
  }], [{
    key: "forEndpoint",
    value:
    /**
     * Create a WebSocketClient for a specific endpoint
     * @static
     * @param {string} endpoint - WebSocket endpoint (e.g., 'app_builder', 'test')
     * @param {Object} options - Client options
     * @returns {WebSocketClient} WebSocketClient instance
     */
    function forEndpoint(endpoint) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var url = (0,env/* getWebSocketUrl */.$0)(endpoint);
      console.log("Creating WebSocketClient for endpoint '".concat(endpoint, "' with URL: ").concat(url));
      return new WebSocketClient(_objectSpread(_objectSpread({}, options), {}, {
        url: url,
        debug: true
      }));
    }
  }]);
}();
/* harmony default export */ const services_WebSocketClient = (WebSocketClient);
;// ./src/hooks/useWebSocket.js

/**
 * useWebSocket Hook
 *
 * A React hook for using WebSockets in components.
 */



/**
 * Hook for using WebSockets in React components
 * @param {string|Object} options - WebSocket URL or options object
 * @param {string} options.url - WebSocket URL
 * @param {string} options.endpoint - WebSocket endpoint (alternative to URL)
 * @param {boolean} options.autoConnect - Automatically connect on mount (default: true)
 * @param {boolean} options.reconnect - Automatically reconnect on close (default: true)
 * @param {number} options.reconnectInterval - Initial reconnect interval in ms (default: 1000)
 * @param {number} options.maxReconnectAttempts - Maximum reconnect attempts (default: 10)
 * @param {boolean} options.debug - Enable debug logging (default: false)
 * @param {Function} options.onOpen - Callback when connection opens
 * @param {Function} options.onMessage - Callback when message is received
 * @param {Function} options.onClose - Callback when connection closes
 * @param {Function} options.onError - Callback when error occurs
 * @returns {Object} WebSocket hook API
 */
var useWebSocket = function useWebSocket(options) {
  // Handle string URL as options
  var config = typeof options === 'string' ? {
    url: options
  } : options;

  // State for connection status and last message
  var _useState = (0,react.useState)(ConnectionState.CLOSED),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    connectionState = _useState2[0],
    setConnectionState = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    lastMessage = _useState4[0],
    setLastMessage = _useState4[1];
  var _useState5 = (0,react.useState)(null),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    lastError = _useState6[0],
    setLastError = _useState6[1];

  // Refs for WebSocketClient and callbacks
  var clientRef = (0,react.useRef)(null);
  var callbacksRef = (0,react.useRef)({
    onOpen: config.onOpen,
    onMessage: config.onMessage,
    onClose: config.onClose,
    onError: config.onError
  });

  // Update callbacks ref when props change
  (0,react.useEffect)(function () {
    callbacksRef.current = {
      onOpen: config.onOpen,
      onMessage: config.onMessage,
      onClose: config.onClose,
      onError: config.onError
    };
  }, [config.onOpen, config.onMessage, config.onClose, config.onError]);

  // Initialize WebSocketClient
  (0,react.useEffect)(function () {
    // Create client options
    var clientOptions = {
      url: config.url,
      reconnectInterval: config.reconnectInterval,
      maxReconnectAttempts: config.maxReconnectAttempts,
      debug: config.debug,
      automaticOpen: false // We'll handle connection manually
    };

    // Create client
    var client;
    if (config.endpoint) {
      client = services_WebSocketClient.forEndpoint(config.endpoint, clientOptions);
    } else {
      client = new services_WebSocketClient(clientOptions);
    }
    clientRef.current = client;

    // Set up event listeners
    var stateChangeListener = function stateChangeListener(_ref) {
      var currentState = _ref.currentState;
      setConnectionState(currentState);
    };
    var openListener = function openListener(event) {
      if (callbacksRef.current.onOpen) {
        callbacksRef.current.onOpen(event);
      }
    };
    var messageListener = function messageListener(_ref2) {
      var data = _ref2.data;
      setLastMessage(data);
      if (callbacksRef.current.onMessage) {
        callbacksRef.current.onMessage(data);
      }
    };
    var closeListener = function closeListener(event) {
      if (callbacksRef.current.onClose) {
        callbacksRef.current.onClose(event);
      }
    };
    var errorListener = function errorListener(event) {
      // Create a more detailed error object
      var errorDetails = {
        timestamp: new Date().toISOString(),
        type: event.type || 'unknown',
        message: event.message || 'WebSocket connection error',
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        originalEvent: event
      };

      // Log detailed error information
      if (config.debug) {
        console.error('WebSocket Error:', errorDetails);
      }

      // Update error state
      setLastError(errorDetails);

      // Call error callback with enhanced error information
      if (callbacksRef.current.onError) {
        callbacksRef.current.onError(errorDetails);
      }
    };

    // Add event listeners
    client.addEventListener('state_change', stateChangeListener);
    client.addEventListener('open', openListener);
    client.addEventListener('message', messageListener);
    client.addEventListener('close', closeListener);
    client.addEventListener('error', errorListener);

    // Connect if autoConnect is true
    if (config.autoConnect !== false) {
      client.open();
    }

    // Cleanup function
    return function () {
      // Remove event listeners
      client.removeEventListener('state_change', stateChangeListener);
      client.removeEventListener('open', openListener);
      client.removeEventListener('message', messageListener);
      client.removeEventListener('close', closeListener);
      client.removeEventListener('error', errorListener);

      // Close connection
      client.close();
    };
  }, [config.url, config.endpoint]); // Only recreate client if URL or endpoint changes

  // Connect function with retry logic
  var connect = (0,react.useCallback)(function () {
    if (clientRef.current) {
      try {
        // Reset error state when attempting to connect
        setLastError(null);

        // Attempt to open the connection
        clientRef.current.open();

        // Log connection attempt if debug is enabled
        if (config.debug) {
          console.log("WebSocket: Attempting to connect to ".concat(config.url || config.endpoint));
        }
      } catch (error) {
        // Handle any synchronous errors during connection attempt
        var errorDetails = {
          timestamp: new Date().toISOString(),
          type: 'connection_error',
          message: error.message || 'Failed to initiate WebSocket connection',
          originalError: error
        };
        setLastError(errorDetails);
        if (callbacksRef.current.onError) {
          callbacksRef.current.onError(errorDetails);
        }
      }
    }
  }, [config.debug, config.url, config.endpoint]);

  // Disconnect function
  var disconnect = (0,react.useCallback)(function (code, reason) {
    if (clientRef.current) {
      clientRef.current.close(code, reason);
    }
  }, []);

  // Send function
  var send = (0,react.useCallback)(function (data) {
    if (clientRef.current) {
      return clientRef.current.send(data);
    }
    return false;
  }, []);

  // Add event listener function
  var addEventListener = (0,react.useCallback)(function (type, listener, options) {
    if (clientRef.current) {
      return clientRef.current.addEventListener(type, listener, options);
    }
    return function () {};
  }, []);

  // Remove event listener function
  var removeEventListener = (0,react.useCallback)(function (type, listener) {
    if (clientRef.current) {
      return clientRef.current.removeEventListener(type, listener);
    }
    return false;
  }, []);

  // Return hook API
  return {
    // State
    connectionState: connectionState,
    lastMessage: lastMessage,
    lastError: lastError,
    // Connection status helpers
    isConnecting: connectionState === ConnectionState.CONNECTING || connectionState === ConnectionState.RECONNECTING,
    isOpen: connectionState === ConnectionState.OPEN,
    isClosed: connectionState === ConnectionState.CLOSED || connectionState === ConnectionState.CLOSING,
    hasError: lastError !== null,
    // Error helpers
    errorMessage: lastError ? lastError.message : null,
    errorType: lastError ? lastError.type : null,
    errorTimestamp: lastError ? lastError.timestamp : null,
    // Methods
    connect: connect,
    disconnect: disconnect,
    send: send,
    addEventListener: addEventListener,
    removeEventListener: removeEventListener,
    // Reset error state
    clearError: function clearError() {
      return setLastError(null);
    },
    // Raw client (for advanced usage)
    client: clientRef.current
  };
};
/* harmony default export */ const hooks_useWebSocket = (useWebSocket);
;// ./src/components/enhanced/WebSocketDemo.js




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;






var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;
var Option = es/* Select */.l6.Option;
var TextArea = es/* Input */.pd.TextArea;
var DemoContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 20px;\n"])));
var DemoCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 20px;\n"])));
var FormGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n\n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"])));
var ButtonGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"])));
var InfoBox = (0,styled_components_browser_esm/* default */.Ay)(es/* Alert */.Fc)(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n"])));
var MessageList = (0,styled_components_browser_esm/* default */.Ay)(es/* List */.B8)(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid var(--border-color);\n  border-radius: 4px;\n  margin-bottom: 16px;\n\n  .ant-list-item {\n    padding: 8px 16px;\n    border-bottom: 1px solid var(--border-color);\n  }\n\n  .message-direction {\n    font-weight: bold;\n    margin-right: 8px;\n  }\n\n  .message-time {\n    color: var(--text-secondary);\n    font-size: 12px;\n  }\n\n  .message-content {\n    margin-top: 4px;\n    word-break: break-word;\n  }\n"])));
var ConnectionStatus = styled_components_browser_esm/* default */.Ay.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 8px 16px;\n  border-radius: 4px;\n  background-color: ", ";\n\n  .status-icon {\n    margin-right: 8px;\n    font-size: 16px;\n    color: ", ";\n  }\n"])), function (props) {
  switch (props.status) {
    case 'connected':
      return 'rgba(82, 196, 26, 0.1)';
    case 'connecting':
      return 'rgba(250, 173, 20, 0.1)';
    case 'disconnected':
      return 'rgba(245, 34, 45, 0.1)';
    default:
      return 'rgba(0, 0, 0, 0.05)';
  }
}, function (props) {
  switch (props.status) {
    case 'connected':
      return '#52c41a';
    case 'connecting':
      return '#faad14';
    case 'disconnected':
      return '#f5222d';
    default:
      return '#8c8c8c';
  }
});

/**
 * WebSocketDemo component
 * Demonstrates the use of WebSockets with the useWebSocket hook
 */
var WebSocketDemo = function WebSocketDemo() {
  // WebSocket URL
  var _useState = (0,react.useState)((0,env/* getWebSocketUrl */.$0)('app_builder')),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    url = _useState2[0],
    setUrl = _useState2[1];

  // WebSocket options
  var _useState3 = (0,react.useState)(true),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    autoReconnect = _useState4[0],
    setAutoReconnect = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    debug = _useState6[0],
    setDebug = _useState6[1];

  // Message state
  var _useState7 = (0,react.useState)(''),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    message = _useState8[0],
    setMessage = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    messages = _useState0[0],
    setMessages = _useState0[1];
  var messagesEndRef = (0,react.useRef)(null);

  // Use our WebSocket hook
  var _useWebSocket = hooks_useWebSocket({
      url: url,
      autoConnect: false,
      reconnect: autoReconnect,
      debug: debug,
      onMessage: function onMessage(data) {
        // Add received message to the list
        var newMessage = {
          id: Date.now(),
          direction: 'received',
          content: (0,esm_typeof/* default */.A)(data) === 'object' ? JSON.stringify(data) : data,
          timestamp: new Date().toISOString()
        };
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [newMessage]);
        });
      }
    }),
    connectionState = _useWebSocket.connectionState,
    lastMessage = _useWebSocket.lastMessage,
    lastError = _useWebSocket.lastError,
    isConnecting = _useWebSocket.isConnecting,
    isOpen = _useWebSocket.isOpen,
    isClosed = _useWebSocket.isClosed,
    hasError = _useWebSocket.hasError,
    connect = _useWebSocket.connect,
    disconnect = _useWebSocket.disconnect,
    send = _useWebSocket.send,
    clearError = _useWebSocket.clearError;

  // Scroll to bottom when messages change
  (0,react.useEffect)(function () {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }, [messages]);

  // Handle connecting to WebSocket
  var handleConnect = function handleConnect() {
    connect();
  };

  // Handle disconnecting from WebSocket
  var handleDisconnect = function handleDisconnect() {
    disconnect(1000, 'User initiated disconnect');
  };

  // Handle sending a message
  var handleSend = function handleSend() {
    if (!message.trim()) return;

    // Send the message
    var success = send(message);
    if (success) {
      // Add sent message to the list
      var newMessage = {
        id: Date.now(),
        direction: 'sent',
        content: message,
        timestamp: new Date().toISOString()
      };
      setMessages(function (prev) {
        return [].concat((0,toConsumableArray/* default */.A)(prev), [newMessage]);
      });
      setMessage('');
    }
  };

  // Handle clearing messages
  var handleClearMessages = function handleClearMessages() {
    setMessages([]);
  };

  // Get connection status
  var getConnectionStatus = function getConnectionStatus() {
    if (isOpen) return 'connected';
    if (isConnecting) return 'connecting';
    return 'disconnected';
  };

  // Get connection status icon
  var getStatusIcon = function getStatusIcon() {
    if (isOpen) return /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, {
      className: "status-icon"
    });
    if (isConnecting) return /*#__PURE__*/react.createElement(icons_es/* SyncOutlined */.OmY, {
      spin: true,
      className: "status-icon"
    });
    return /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, {
      className: "status-icon"
    });
  };

  // Get connection status text
  var getStatusText = function getStatusText() {
    if (isOpen) return 'Connected';
    if (isConnecting) return 'Connecting...';
    if (hasError) return "Disconnected (".concat((lastError === null || lastError === void 0 ? void 0 : lastError.message) || 'Error', ")");
    return 'Disconnected';
  };
  return /*#__PURE__*/react.createElement(DemoContainer, null, /*#__PURE__*/react.createElement(Title, {
    level: 3
  }, "WebSocket Demo"), /*#__PURE__*/react.createElement(Paragraph, null, "This demo shows how to use WebSockets with the useWebSocket hook."), /*#__PURE__*/react.createElement(DemoCard, {
    title: "WebSocket Connection"
  }, /*#__PURE__*/react.createElement(InfoBox, {
    type: "info",
    message: "WebSocket Configuration",
    description: "Configure and manage your WebSocket connection.",
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)
  }), /*#__PURE__*/react.createElement(ConnectionStatus, {
    status: getConnectionStatus()
  }, getStatusIcon(), /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, getStatusText())), hasError && /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    type: "error",
    message: "Connection Error",
    description: (lastError === null || lastError === void 0 ? void 0 : lastError.message) || 'An error occurred with the WebSocket connection',
    style: {
      marginBottom: '16px'
    },
    closable: true,
    onClose: clearError
  }), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "WebSocket URL:"), /*#__PURE__*/react.createElement(es/* Input */.pd, {
    value: url,
    onChange: function onChange(e) {
      return setUrl(e.target.value);
    },
    placeholder: "Enter WebSocket URL",
    disabled: isOpen || isConnecting
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Auto Reconnect:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: autoReconnect,
    onChange: setAutoReconnect,
    disabled: isOpen || isConnecting
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Debug Mode:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: debug,
    onChange: setDebug
  })), /*#__PURE__*/react.createElement(ButtonGroup, null, !isOpen ? /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
    onClick: handleConnect,
    loading: isConnecting,
    disabled: isConnecting
  }, "Connect") : /*#__PURE__*/react.createElement(es/* Button */.$n, {
    danger: true,
    icon: /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, null),
    onClick: handleDisconnect
  }, "Disconnect"))), /*#__PURE__*/react.createElement(DemoCard, {
    title: "Messages"
  }, /*#__PURE__*/react.createElement(InfoBox, {
    type: "info",
    message: "Send and Receive Messages",
    description: "Send messages to the WebSocket server and view received messages.",
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)
  }), /*#__PURE__*/react.createElement(MessageList, {
    dataSource: messages,
    locale: {
      emptyText: 'No messages yet'
    },
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, null, /*#__PURE__*/react.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
        color: item.direction === 'sent' ? 'blue' : 'green',
        text: /*#__PURE__*/react.createElement("span", {
          className: "message-direction"
        }, item.direction === 'sent' ? 'Sent' : 'Received')
      }), /*#__PURE__*/react.createElement("span", {
        className: "message-time"
      }, new Date(item.timestamp).toLocaleTimeString())), /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, /*#__PURE__*/react.createElement(Text, {
        code: true
      }, item.content))));
    }
  }), /*#__PURE__*/react.createElement("div", {
    ref: messagesEndRef
  }), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Message:"), /*#__PURE__*/react.createElement(TextArea, {
    value: message,
    onChange: function onChange(e) {
      return setMessage(e.target.value);
    },
    placeholder: "Enter message to send",
    rows: 4,
    disabled: !isOpen
  })), /*#__PURE__*/react.createElement(ButtonGroup, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* SendOutlined */.jnF, null),
    onClick: handleSend,
    disabled: !isOpen || !message.trim()
  }, "Send"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* ClearOutlined */.ohj, null),
    onClick: handleClearMessages,
    disabled: messages.length === 0
  }, "Clear Messages"))));
};
/* harmony default export */ const enhanced_WebSocketDemo = (WebSocketDemo);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./src/redux/minimal-store.js
var minimal_store = __webpack_require__(34816);
;// ./src/hooks/useWebSocketConnection.js





var _excluded = ["url", "autoConnect", "autoReconnect", "updateRedux", "debug"];

function useWebSocketConnection_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function useWebSocketConnection_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? useWebSocketConnection_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : useWebSocketConnection_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




// Safely import optional dependencies
var EnhancedWebSocketClient, useWebSocketConnection_ConnectionState, timeSync;
try {
  var wsModule = __webpack_require__(17177);
  EnhancedWebSocketClient = wsModule["default"] || wsModule.EnhancedWebSocketClient;
  useWebSocketConnection_ConnectionState = wsModule.ConnectionState;
} catch (error) {
  console.warn('EnhancedWebSocketClient not available, using fallback');
}
try {
  var timeSyncModule = __webpack_require__(23774);
  timeSync = timeSyncModule.timeSync || timeSyncModule["default"];
} catch (error) {
  console.warn('TimeSyncService not available, using fallback');
}

/**
 * Custom hook for WebSocket integration with better error handling
 * 
 * @param {Object} options - WebSocket options
 * @param {string} options.url - WebSocket URL
 * @param {boolean} [options.autoConnect=true] - Whether to connect automatically
 * @param {boolean} [options.autoReconnect=true] - Whether to reconnect automatically
 * @param {boolean} [options.updateRedux=true] - Whether to update Redux state
 * @param {boolean} [options.debug=false] - Enable debug logging
 * @returns {Object} WebSocket state and methods
 */
function useWebSocketConnection(options) {
  var url = options.url,
    _options$autoConnect = options.autoConnect,
    autoConnect = _options$autoConnect === void 0 ? true : _options$autoConnect,
    _options$autoReconnec = options.autoReconnect,
    autoReconnect = _options$autoReconnec === void 0 ? true : _options$autoReconnec,
    _options$updateRedux = options.updateRedux,
    updateRedux = _options$updateRedux === void 0 ? true : _options$updateRedux,
    _options$debug = options.debug,
    debug = _options$debug === void 0 ? false : _options$debug,
    wsOptions = (0,objectWithoutProperties/* default */.A)(options, _excluded);
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var wsRef = (0,react.useRef)(null);
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    connected = _useState2[0],
    setConnected = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    connecting = _useState4[0],
    setConnecting = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    messages = _useState6[0],
    setMessages = _useState6[1];
  var _useState7 = (0,react.useState)(null),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    error = _useState8[0],
    setError = _useState8[1];
  var _useState9 = (0,react.useState)(0),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    reconnectAttempt = _useState0[0],
    setReconnectAttempt = _useState0[1];
  var _useState1 = (0,react.useState)(null),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    closeInfo = _useState10[0],
    setCloseInfo = _useState10[1];

  // Initialize WebSocket client
  (0,react.useEffect)(function () {
    if (!url) return;

    // Check if EnhancedWebSocketClient is available
    if (!EnhancedWebSocketClient) {
      console.warn('EnhancedWebSocketClient not available, using native WebSocket');

      // Fallback to native WebSocket
      try {
        var _ws = new WebSocket(url);
        wsRef.current = _ws;

        // Add basic methods for compatibility
        _ws.destroy = function () {
          return _ws.close();
        };
        _ws.open = function () {
          // WebSocket opens automatically on creation
        };
        return function () {
          if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
          }
        };
      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        setError(error);
        return;
      }
    }

    // Create WebSocket client
    var ws = new EnhancedWebSocketClient(useWebSocketConnection_objectSpread({
      url: url,
      autoConnect: autoConnect,
      autoReconnect: autoReconnect,
      debug: debug
    }, wsOptions));

    // Store reference
    wsRef.current = ws;

    // Clean up on unmount
    return function () {
      if (wsRef.current) {
        wsRef.current.destroy();
        wsRef.current = null;
      }
    };
  }, [url, autoConnect, autoReconnect, debug]);

  // Set up event listeners
  (0,react.useEffect)(function () {
    var ws = wsRef.current;
    if (!ws) return;

    // Connection opened
    var handleOpen = /*#__PURE__*/function () {
      var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(event) {
        return regenerator_default().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              if (!(timeSync && typeof timeSync.synchronize === 'function')) {
                _context.next = 9;
                break;
              }
              _context.prev = 1;
              _context.next = 4;
              return timeSync.synchronize();
            case 4:
              _context.next = 9;
              break;
            case 6:
              _context.prev = 6;
              _context.t0 = _context["catch"](1);
              console.warn('Time synchronization failed:', _context.t0);
            case 9:
              console.log('WebSocket connection opened:', event);
              setConnected(true);
              setConnecting(false);
              setError(null);
              setCloseInfo(null);
              if (updateRedux) {
                try {
                  dispatch((0,minimal_store.websocketConnected)());
                } catch (error) {
                  console.warn('Failed to dispatch websocketConnected:', error);
                }
              }
            case 15:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[1, 6]]);
      }));
      return function handleOpen(_x) {
        return _ref.apply(this, arguments);
      };
    }();

    // Connection closed
    var handleClose = function handleClose(event) {
      console.log('WebSocket connection closed:', event);
      setConnected(false);
      setConnecting(false);
      setCloseInfo(event);
      if (updateRedux) {
        dispatch((0,minimal_store.websocketDisconnected)());
      }
    };

    // Connection error
    var handleError = function handleError(event) {
      console.error('WebSocket error:', event);
      setError(event);
      setConnecting(false);
    };

    // Message received
    var handleMessage = (0,react.useCallback)(function (message) {
      try {
        var processedMessage = message;

        // Handle native WebSocket message events
        if (message.data) {
          try {
            processedMessage = JSON.parse(message.data);
          } catch (error) {
            processedMessage = {
              content: message.data
            };
          }
        }
        var serverTimestamp = new Date(processedMessage.timestamp || Date.now());
        var localTimestamp = serverTimestamp;

        // Only use timeSync if available
        if (timeSync && typeof timeSync.getClientTime === 'function') {
          try {
            localTimestamp = timeSync.getClientTime(serverTimestamp);
          } catch (error) {
            console.warn('Time sync failed, using server timestamp:', error);
          }
        }
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [useWebSocketConnection_objectSpread(useWebSocketConnection_objectSpread({}, processedMessage), {}, {
            timestamp: localTimestamp,
            serverTimestamp: serverTimestamp
          })]);
        });

        // Update Redux if enabled
        if (updateRedux) {
          try {
            dispatch((0,minimal_store.websocketMessageReceived)({
              type: 'received',
              content: processedMessage.content || processedMessage,
              timestamp: localTimestamp,
              serverTimestamp: serverTimestamp
            }));
          } catch (error) {
            console.warn('Failed to dispatch websocketMessageReceived:', error);
          }
        }
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    }, [dispatch, updateRedux]);

    // Reconnect attempt
    var handleReconnectAttempt = function handleReconnectAttempt(data) {
      console.log('WebSocket reconnect attempt:', data);
      setConnecting(true);
      setReconnectAttempt(data.attempt);
    };

    // Add event listeners with null checks
    ws.addEventListener('open', handleOpen);
    ws.addEventListener('close', handleClose);
    ws.addEventListener('error', handleError);
    ws.addEventListener('message', handleMessage);
    ws.addEventListener('reconnect_attempt', handleReconnectAttempt);

    // Clean up event listeners
    return function () {
      if (ws) {
        ws.removeEventListener('open', handleOpen);
        ws.removeEventListener('close', handleClose);
        ws.removeEventListener('error', handleError);
        ws.removeEventListener('message', handleMessage);
        ws.removeEventListener('reconnect_attempt', handleReconnectAttempt);
      }
    };
  }, [dispatch, updateRedux]);

  // Connect to WebSocket
  var connect = (0,react.useCallback)(function () {
    if (!wsRef.current) return;
    setConnecting(true);
    wsRef.current.open();
  }, []);

  // Disconnect from WebSocket
  var disconnect = (0,react.useCallback)(function () {
    if (!wsRef.current) return;
    wsRef.current.close();
  }, []);

  // Send message
  var sendMessage = (0,react.useCallback)(function (data) {
    var batch = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
    if (!wsRef.current) {
      return false;
    }

    // Check connection state
    var isConnected = useWebSocketConnection_ConnectionState ? wsRef.current.connectionState === useWebSocketConnection_ConnectionState.OPEN : wsRef.current.readyState === WebSocket.OPEN;
    if (!isConnected) {
      return false;
    }
    try {
      var success = false;

      // Send message using appropriate method
      if (wsRef.current.send && typeof wsRef.current.send === 'function') {
        // Enhanced WebSocket client
        success = wsRef.current.send(data, batch);
      } else {
        // Native WebSocket
        var messageStr = typeof data === 'string' ? data : JSON.stringify(data);
        wsRef.current.send(messageStr);
        success = true;
      }

      // Add to messages if successful
      if (success) {
        var timestamp = new Date().toISOString();

        // Add message to state
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [{
            type: 'sent',
            content: data,
            timestamp: timestamp
          }]);
        });

        // Update Redux if enabled
        if (updateRedux) {
          try {
            dispatch((0,minimal_store.websocketMessageReceived)({
              type: 'sent',
              content: data,
              timestamp: timestamp
            }));
          } catch (error) {
            console.warn('Failed to dispatch sent message:', error);
          }
        }
      }
      return success;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      return false;
    }
  }, [dispatch, updateRedux]);

  // Clear messages
  var clearMessages = (0,react.useCallback)(function () {
    setMessages([]);
  }, []);

  // Reset connection (close and reopen)
  var resetConnection = (0,react.useCallback)(function () {
    if (!wsRef.current) return;
    wsRef.current.close();
    setTimeout(function () {
      setError(null);
      setCloseInfo(null);
      setReconnectAttempt(0);
      wsRef.current.open();
    }, 1000);
  }, []);
  return {
    connected: connected,
    connecting: connecting,
    reconnectAttempt: reconnectAttempt,
    messages: messages,
    error: error,
    closeInfo: closeInfo,
    connect: connect,
    disconnect: disconnect,
    sendMessage: sendMessage,
    clearMessages: clearMessages,
    resetConnection: resetConnection,
    client: wsRef.current
  };
}
/* harmony default export */ const hooks_useWebSocketConnection = (useWebSocketConnection);
;// ./src/components/enhanced/LegacyWebSocketManager.js



var LegacyWebSocketManager_templateObject, LegacyWebSocketManager_templateObject2, LegacyWebSocketManager_templateObject3, LegacyWebSocketManager_templateObject4, LegacyWebSocketManager_templateObject5, LegacyWebSocketManager_templateObject6, LegacyWebSocketManager_templateObject7, _templateObject8, _templateObject9, _templateObject0;







// Import custom hook

var TabPane = es/* Tabs */.tU.TabPane;
var LegacyWebSocketManager_TextArea = es/* Input */.pd.TextArea;
var LegacyWebSocketManager_Title = es/* Typography */.o5.Title,
  LegacyWebSocketManager_Text = es/* Typography */.o5.Text,
  LegacyWebSocketManager_Paragraph = es/* Typography */.o5.Paragraph;
var WebSocketManagerContainer = design_system.styled.div(LegacyWebSocketManager_templateObject || (LegacyWebSocketManager_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);
var LegacyWebSocketManager_ConnectionStatus = design_system.styled.div(LegacyWebSocketManager_templateObject2 || (LegacyWebSocketManager_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", ";\n  border-radius: ", ";\n  background-color: ", ";\n  color: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3], theme/* default.borderRadius */.Ay.borderRadius.md, function (props) {
  return props.connected ? theme/* default.colors */.Ay.colors.success.light : theme/* default.colors */.Ay.colors.error.light;
}, function (props) {
  return props.connected ? theme/* default.colors */.Ay.colors.success.dark : theme/* default.colors */.Ay.colors.error.dark;
});
var LegacyWebSocketManager_MessageList = design_system.styled.div(LegacyWebSocketManager_templateObject3 || (LegacyWebSocketManager_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  max-height: 400px;\n  overflow-y: auto;\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.colors */.Ay.colors.neutral[50]);
var Message = design_system.styled.div(LegacyWebSocketManager_templateObject4 || (LegacyWebSocketManager_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", " ", ";\n  border-radius: ", ";\n  background-color: ", ";\n  border-left: 4px solid ", ";\n  box-shadow: ", ";\n\n  .message-header {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: ", ";\n    font-size: ", ";\n    color: ", ";\n  }\n\n  .timestamp {\n    font-family: monospace;\n    font-size: ", ";\n    color: ", ";\n    background-color: ", ";\n    padding: 2px 4px;\n    border-radius: 3px;\n  }\n\n  .message-content {\n    word-break: break-word;\n  }\n\n  pre {\n    background-color: ", ";\n    padding: ", ";\n    border-radius: ", ";\n    overflow-x: auto;\n    font-family: ", ";\n    font-size: ", ";\n    margin: ", " 0 0 0;\n  }\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3], theme/* default.borderRadius */.Ay.borderRadius.md, function (props) {
  return props.type === 'sent' ? theme/* default.colors */.Ay.colors.primary.light : 'white';
}, function (props) {
  return props.type === 'sent' ? theme/* default.colors */.Ay.colors.primary.main : theme/* default.colors */.Ay.colors.secondary.main;
}, theme/* default.shadows */.Ay.shadows.sm, theme/* default.spacing */.Ay.spacing[1], theme/* default.typography */.Ay.typography.fontSize.sm, theme/* default.colors */.Ay.colors.neutral[500], theme/* default.typography */.Ay.typography.fontSize.xs, theme/* default.colors */.Ay.colors.neutral[600], theme/* default.colors */.Ay.colors.neutral[100], theme/* default.colors */.Ay.colors.neutral[100], theme/* default.spacing */.Ay.spacing[2], theme/* default.borderRadius */.Ay.borderRadius.sm, theme/* default.typography */.Ay.typography.fontFamily.code, theme/* default.typography */.Ay.typography.fontSize.sm, theme/* default.spacing */.Ay.spacing[2]);
var MessageInput = design_system.styled.div(LegacyWebSocketManager_templateObject5 || (LegacyWebSocketManager_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2]);
var SettingsPanel = design_system.styled.div(LegacyWebSocketManager_templateObject6 || (LegacyWebSocketManager_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: white;\n"])), theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[3], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md);
var SettingsGroup = design_system.styled.div(LegacyWebSocketManager_templateObject7 || (LegacyWebSocketManager_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2]);
var MessageTemplates = design_system.styled.div(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: ", ";\n  margin-bottom: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3]);
var DiagnosticsPanel = design_system.styled.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: white;\n"])), theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[3], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md);
var DiagnosticsItem = design_system.styled.div(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: ", ";\n"])), theme/* default.spacing */.Ay.spacing[1], theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.colors */.Ay.colors.neutral[50]);

/**
 * Legacy WebSocket Manager with error handling for code 1006
 */
var LegacyWebSocketManager = function LegacyWebSocketManager() {
  // Safely get websocket state with fallback
  var websocketState = (0,react_redux/* useSelector */.d4)(function (state) {
    try {
      return (state === null || state === void 0 ? void 0 : state.websocket) || {};
    } catch (error) {
      console.warn('Error accessing websocket state:', error);
      return {};
    }
  });
  var storeUrl = websocketState === null || websocketState === void 0 ? void 0 : websocketState.url;
  var _useState = (0,react.useState)('messages'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react.useState)(storeUrl || 'ws://localhost:8000/ws/test/'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    wsUrl = _useState4[0],
    setWsUrl = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    messageText = _useState6[0],
    setMessageText = _useState6[1];
  var _useState7 = (0,react.useState)(true),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    autoReconnect = _useState8[0],
    setAutoReconnect = _useState8[1];
  var _useState9 = (0,react.useState)(true),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    showTimestamp = _useState0[0],
    setShowTimestamp = _useState0[1];
  var _useState1 = (0,react.useState)(true),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    heartbeatEnabled = _useState10[0],
    setHeartbeatEnabled = _useState10[1];
  var messagesEndRef = (0,react.useRef)(null);

  // Use the WebSocket hook - MUST be called before any conditional returns
  var _useWebSocketConnecti = hooks_useWebSocketConnection({
      url: wsUrl,
      autoConnect: false,
      autoReconnect: autoReconnect,
      debug: true,
      updateRedux: true,
      heartbeatInterval: heartbeatEnabled ? 30000 : 0,
      reconnectInterval: 2000,
      maxReconnectAttempts: 5
    }),
    connected = _useWebSocketConnecti.connected,
    connecting = _useWebSocketConnecti.connecting,
    reconnectAttempt = _useWebSocketConnecti.reconnectAttempt,
    messages = _useWebSocketConnecti.messages,
    error = _useWebSocketConnecti.error,
    closeInfo = _useWebSocketConnecti.closeInfo,
    connect = _useWebSocketConnecti.connect,
    disconnect = _useWebSocketConnecti.disconnect,
    sendMessage = _useWebSocketConnecti.sendMessage,
    clearMessages = _useWebSocketConnecti.clearMessages,
    resetConnection = _useWebSocketConnecti.resetConnection;

  // Early return with simple UI if there are any issues
  if (!websocketState || (0,esm_typeof/* default */.A)(websocketState) !== 'object') {
    return /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: "Legacy WebSocket Manager",
      style: {
        margin: '20px'
      }
    }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
      message: "Legacy Component",
      description: "This is the legacy WebSocket manager. We recommend using the new WebSocket Demo component for better performance and features.",
      type: "warning",
      showIcon: true,
      style: {
        marginBottom: '16px'
      }
    }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "primary",
      onClick: function onClick() {
        return window.location.reload();
      }
    }, "Switch to New WebSocket Demo"));
  }

  // Auto-scroll to bottom of messages
  (0,react.useEffect)(function () {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }, [messages]);

  // Handle connect button click
  var handleConnect = function handleConnect() {
    try {
      // Validate WebSocket URL
      if (!wsUrl.trim().startsWith('ws://') && !wsUrl.trim().startsWith('wss://')) {
        es/* message */.iU.error('Invalid WebSocket URL. It should start with ws:// or wss://');
        return;
      }
      connect();
      es/* message */.iU.info('Connecting to WebSocket...');
    } catch (error) {
      es/* message */.iU.error("Connection error: ".concat(error.message));
    }
  };

  // Handle disconnect button click
  var handleDisconnect = function handleDisconnect() {
    try {
      disconnect();
      es/* message */.iU.info('Disconnected from WebSocket');
    } catch (error) {
      es/* message */.iU.error("Disconnect error: ".concat(error.message));
    }
  };

  // Handle reconnect with new URL
  var handleReconnect = function handleReconnect() {
    try {
      resetConnection();
      setTimeout(function () {
        connect();
        es/* message */.iU.info('Reconnecting to WebSocket...');
      }, 500);
    } catch (error) {
      es/* message */.iU.error("Reconnection error: ".concat(error.message));
    }
  };

  // Handle send message button click
  var handleSendMessage = function handleSendMessage() {
    if (!messageText.trim() || !connected) return;
    try {
      // Try to parse as JSON if it looks like JSON
      var messageData;
      if (messageText.trim().startsWith('{') || messageText.trim().startsWith('[')) {
        messageData = JSON.parse(messageText);
      } else {
        messageData = {
          message: messageText
        };
      }

      // Send the message
      sendMessage(messageData);

      // Clear the input
      setMessageText('');
    } catch (error) {
      es/* message */.iU.error("Error sending message: ".concat(error.message));
    }
  };

  // Handle key down in message input
  var handleKeyDown = function handleKeyDown(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  var formatTimestamp = function formatTimestamp(timestamp) {
    if (!showTimestamp) return null;
    try {
      // Parse the timestamp string to a Date object
      var date = new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp:', timestamp);
        return null;
      }

      // Format the date as YYYY-MM-DD HH:MM:SS.mmm
      var year = date.getFullYear();
      var month = String(date.getMonth() + 1).padStart(2, '0');
      var day = String(date.getDate()).padStart(2, '0');
      var hours = String(date.getHours()).padStart(2, '0');
      var minutes = String(date.getMinutes()).padStart(2, '0');
      var seconds = String(date.getSeconds()).padStart(2, '0');
      var milliseconds = String(date.getMilliseconds()).padStart(3, '0');
      return "".concat(year, "-").concat(month, "-").concat(day, " ").concat(hours, ":").concat(minutes, ":").concat(seconds, ".").concat(milliseconds);
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return null;
    }
  };

  // Check if a string is JSON
  var isJsonString = function isJsonString(str) {
    try {
      var json = JSON.parse(str);
      return (0,esm_typeof/* default */.A)(json) === 'object';
    } catch (e) {
      return false;
    }
  };

  // Render message content
  var renderMessageContent = function renderMessageContent(content) {
    if (typeof content === 'string' && isJsonString(content)) {
      return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, "JSON Message"), /*#__PURE__*/react.createElement("pre", null, JSON.stringify(JSON.parse(content), null, 2)));
    } else if ((0,esm_typeof/* default */.A)(content) === 'object') {
      return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, "Object Message"), /*#__PURE__*/react.createElement("pre", null, JSON.stringify(content, null, 2)));
    } else {
      return /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, content);
    }
  };

  // Message templates
  var messageTemplates = [{
    name: 'Ping',
    content: '{"type": "ping"}'
  }, {
    name: 'Request Data',
    content: '{"type": "request_data"}'
  }, {
    name: 'Hello',
    content: '{"type": "message", "content": "Hello, WebSocket!"}'
  }, {
    name: 'Status',
    content: '{"type": "status_request"}'
  }, {
    name: 'Get Components',
    content: '{"type": "get_components"}'
  }, {
    name: 'Get Layouts',
    content: '{"type": "get_layouts"}'
  }, {
    name: 'Get Themes',
    content: '{"type": "get_themes"}'
  }, {
    name: 'Create Component',
    content: '{"type": "create_component", "data": {"name": "Test Button", "type": "button", "props": {"text": "Click Me", "variant": "primary"}}}'
  }, {
    name: 'Update Theme',
    content: '{"type": "update_theme", "theme_id": "default", "data": {"primaryColor": "#FF5733"}}'
  }];

  // Render error alert for code 1006
  var renderErrorAlert = function renderErrorAlert() {
    if (!closeInfo || closeInfo.code !== 1006) return null;
    return /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
      message: "WebSocket Connection Error (Code 1006)",
      description: /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("p", null, "The WebSocket connection was closed abnormally (code 1006). This typically indicates one of the following issues:"), /*#__PURE__*/react.createElement("ul", null, /*#__PURE__*/react.createElement("li", null, "Network connectivity problems"), /*#__PURE__*/react.createElement("li", null, "Server process termination"), /*#__PURE__*/react.createElement("li", null, "Proxy or firewall blocking the connection"), /*#__PURE__*/react.createElement("li", null, "Connection timeout")), /*#__PURE__*/react.createElement("p", null, /*#__PURE__*/react.createElement("strong", null, "Troubleshooting steps:")), /*#__PURE__*/react.createElement("ol", null, /*#__PURE__*/react.createElement("li", null, "Check if the WebSocket server is running"), /*#__PURE__*/react.createElement("li", null, "Verify the WebSocket URL is correct"), /*#__PURE__*/react.createElement("li", null, "Check for network restrictions"), /*#__PURE__*/react.createElement("li", null, "Try using a different WebSocket endpoint")), /*#__PURE__*/react.createElement("div", {
        style: {
          marginTop: '10px'
        }
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        type: "primary",
        icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
        onClick: resetConnection
      }, "Reset Connection"))),
      type: "error",
      showIcon: true,
      icon: /*#__PURE__*/react.createElement(icons_es/* BugOutlined */.NhG, null),
      style: {
        marginBottom: '16px'
      }
    });
  };
  return /*#__PURE__*/react.createElement(WebSocketManagerContainer, null, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Legacy WebSocket Manager"
  }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Legacy Component",
    description: "This is the legacy WebSocket manager. We recommend using the new WebSocket Demo component for better performance and features.",
    type: "warning",
    showIcon: true,
    style: {
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    onClick: function onClick() {
      return window.location.reload();
    },
    style: {
      marginBottom: '16px'
    }
  }, "Switch to New WebSocket Demo")));
};
/* harmony default export */ const enhanced_LegacyWebSocketManager = (LegacyWebSocketManager);
;// ./src/components/enhanced/FixedWebSocketManager.js


var FixedWebSocketManager_templateObject;





// Import WebSocket components


var FixedWebSocketManager_WebSocketManagerContainer = design_system.styled.div(FixedWebSocketManager_templateObject || (FixedWebSocketManager_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);

/**
 * Enhanced WebSocket Manager with improved features and error handling
 */
var FixedWebSocketManager = function FixedWebSocketManager() {
  var _useState = (0,react.useState)('new'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  return /*#__PURE__*/react.createElement(FixedWebSocketManager_WebSocketManagerContainer, null, /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react.createElement(es/* Tabs */.tU.TabPane, {
    tab: "Enhanced WebSocket",
    key: "new"
  }, /*#__PURE__*/react.createElement(enhanced_WebSocketDemo, null)), /*#__PURE__*/react.createElement(es/* Tabs */.tU.TabPane, {
    tab: "Legacy WebSocket",
    key: "legacy"
  }, /*#__PURE__*/react.createElement(enhanced_LegacyWebSocketManager, null))));
};
/* harmony default export */ const enhanced_FixedWebSocketManager = (FixedWebSocketManager);

/***/ })

}]);