"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3205],{

/***/ 33205:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ rc_image_es)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(89379);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/css.js
var css = __webpack_require__(92830);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(12533);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/rc-dialog/es/index.js + 8 modules
var es = __webpack_require__(33766);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/addEventListener.js
var addEventListener = __webpack_require__(69916);
// EXTERNAL MODULE: ./node_modules/rc-util/es/KeyCode.js
var KeyCode = __webpack_require__(16928);
// EXTERNAL MODULE: ./node_modules/@rc-component/portal/es/index.js + 6 modules
var portal_es = __webpack_require__(45062);
// EXTERNAL MODULE: ./node_modules/rc-motion/es/index.js + 12 modules
var rc_motion_es = __webpack_require__(57557);
;// ./node_modules/rc-image/es/context.js

var PreviewGroupContext = /*#__PURE__*/react.createContext(null);
;// ./node_modules/rc-image/es/Operations.js









var Operations = function Operations(props) {
  var visible = props.visible,
    maskTransitionName = props.maskTransitionName,
    getContainer = props.getContainer,
    prefixCls = props.prefixCls,
    rootClassName = props.rootClassName,
    icons = props.icons,
    countRender = props.countRender,
    showSwitch = props.showSwitch,
    showProgress = props.showProgress,
    current = props.current,
    transform = props.transform,
    count = props.count,
    scale = props.scale,
    minScale = props.minScale,
    maxScale = props.maxScale,
    closeIcon = props.closeIcon,
    onActive = props.onActive,
    onClose = props.onClose,
    onZoomIn = props.onZoomIn,
    onZoomOut = props.onZoomOut,
    onRotateRight = props.onRotateRight,
    onRotateLeft = props.onRotateLeft,
    onFlipX = props.onFlipX,
    onFlipY = props.onFlipY,
    onReset = props.onReset,
    toolbarRender = props.toolbarRender,
    zIndex = props.zIndex,
    image = props.image;
  var groupContext = (0,react.useContext)(PreviewGroupContext);
  var rotateLeft = icons.rotateLeft,
    rotateRight = icons.rotateRight,
    zoomIn = icons.zoomIn,
    zoomOut = icons.zoomOut,
    close = icons.close,
    left = icons.left,
    right = icons.right,
    flipX = icons.flipX,
    flipY = icons.flipY;
  var toolClassName = "".concat(prefixCls, "-operations-operation");
  react.useEffect(function () {
    var onKeyDown = function onKeyDown(e) {
      if (e.keyCode === KeyCode/* default */.A.ESC) {
        onClose();
      }
    };
    if (visible) {
      window.addEventListener('keydown', onKeyDown);
    }
    return function () {
      window.removeEventListener('keydown', onKeyDown);
    };
  }, [visible]);
  var handleActive = function handleActive(e, offset) {
    e.preventDefault();
    e.stopPropagation();
    onActive(offset);
  };
  var renderOperation = react.useCallback(function (_ref) {
    var type = _ref.type,
      disabled = _ref.disabled,
      onClick = _ref.onClick,
      icon = _ref.icon;
    return /*#__PURE__*/react.createElement("div", {
      key: type,
      className: classnames_default()(toolClassName, "".concat(prefixCls, "-operations-operation-").concat(type), (0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-operations-operation-disabled"), !!disabled)),
      onClick: onClick
    }, icon);
  }, [toolClassName, prefixCls]);
  var switchPrevNode = showSwitch ? renderOperation({
    icon: left,
    onClick: function onClick(e) {
      return handleActive(e, -1);
    },
    type: 'prev',
    disabled: current === 0
  }) : undefined;
  var switchNextNode = showSwitch ? renderOperation({
    icon: right,
    onClick: function onClick(e) {
      return handleActive(e, 1);
    },
    type: 'next',
    disabled: current === count - 1
  }) : undefined;
  var flipYNode = renderOperation({
    icon: flipY,
    onClick: onFlipY,
    type: 'flipY'
  });
  var flipXNode = renderOperation({
    icon: flipX,
    onClick: onFlipX,
    type: 'flipX'
  });
  var rotateLeftNode = renderOperation({
    icon: rotateLeft,
    onClick: onRotateLeft,
    type: 'rotateLeft'
  });
  var rotateRightNode = renderOperation({
    icon: rotateRight,
    onClick: onRotateRight,
    type: 'rotateRight'
  });
  var zoomOutNode = renderOperation({
    icon: zoomOut,
    onClick: onZoomOut,
    type: 'zoomOut',
    disabled: scale <= minScale
  });
  var zoomInNode = renderOperation({
    icon: zoomIn,
    onClick: onZoomIn,
    type: 'zoomIn',
    disabled: scale === maxScale
  });
  var toolbarNode = /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-operations")
  }, flipYNode, flipXNode, rotateLeftNode, rotateRightNode, zoomOutNode, zoomInNode);
  return /*#__PURE__*/react.createElement(rc_motion_es/* default */.Ay, {
    visible: visible,
    motionName: maskTransitionName
  }, function (_ref2) {
    var className = _ref2.className,
      style = _ref2.style;
    return /*#__PURE__*/react.createElement(portal_es/* default */.A, {
      open: true,
      getContainer: getContainer !== null && getContainer !== void 0 ? getContainer : document.body
    }, /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-operations-wrapper"), className, rootClassName),
      style: (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, style), {}, {
        zIndex: zIndex
      })
    }, closeIcon === null ? null : /*#__PURE__*/react.createElement("button", {
      className: "".concat(prefixCls, "-close"),
      onClick: onClose
    }, closeIcon || close), showSwitch && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-switch-left"), (0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-switch-left-disabled"), current === 0)),
      onClick: function onClick(e) {
        return handleActive(e, -1);
      }
    }, left), /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-switch-right"), (0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-switch-right-disabled"), current === count - 1)),
      onClick: function onClick(e) {
        return handleActive(e, 1);
      }
    }, right)), /*#__PURE__*/react.createElement("div", {
      className: "".concat(prefixCls, "-footer")
    }, showProgress && /*#__PURE__*/react.createElement("div", {
      className: "".concat(prefixCls, "-progress")
    }, countRender ? countRender(current + 1, count) : /*#__PURE__*/react.createElement("bdi", null, "".concat(current + 1, " / ").concat(count))), toolbarRender ? toolbarRender(toolbarNode, (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({
      icons: {
        prevIcon: switchPrevNode,
        nextIcon: switchNextNode,
        flipYIcon: flipYNode,
        flipXIcon: flipXNode,
        rotateLeftIcon: rotateLeftNode,
        rotateRightIcon: rotateRightNode,
        zoomOutIcon: zoomOutNode,
        zoomInIcon: zoomInNode
      },
      actions: {
        onActive: onActive,
        onFlipY: onFlipY,
        onFlipX: onFlipX,
        onRotateLeft: onRotateLeft,
        onRotateRight: onRotateRight,
        onZoomOut: onZoomOut,
        onZoomIn: onZoomIn,
        onReset: onReset,
        onClose: onClose
      },
      transform: transform
    }, groupContext ? {
      current: current,
      total: count
    } : {}), {}, {
      image: image
    })) : toolbarNode)));
  });
};
/* harmony default export */ const es_Operations = (Operations);
// EXTERNAL MODULE: ./node_modules/rc-util/es/isEqual.js
var isEqual = __webpack_require__(43210);
// EXTERNAL MODULE: ./node_modules/rc-util/es/raf.js
var raf = __webpack_require__(25371);
;// ./node_modules/rc-image/es/hooks/useImageTransform.js






var initialTransform = {
  x: 0,
  y: 0,
  rotate: 0,
  scale: 1,
  flipX: false,
  flipY: false
};
function useImageTransform(imgRef, minScale, maxScale, onTransform) {
  var frame = (0,react.useRef)(null);
  var queue = (0,react.useRef)([]);
  var _useState = (0,react.useState)(initialTransform),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    transform = _useState2[0],
    setTransform = _useState2[1];
  var resetTransform = function resetTransform(action) {
    setTransform(initialTransform);
    if (!(0,isEqual/* default */.A)(initialTransform, transform)) {
      onTransform === null || onTransform === void 0 || onTransform({
        transform: initialTransform,
        action: action
      });
    }
  };

  /** Direct update transform */
  var updateTransform = function updateTransform(newTransform, action) {
    if (frame.current === null) {
      queue.current = [];
      frame.current = (0,raf/* default */.A)(function () {
        setTransform(function (preState) {
          var memoState = preState;
          queue.current.forEach(function (queueState) {
            memoState = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, memoState), queueState);
          });
          frame.current = null;
          onTransform === null || onTransform === void 0 || onTransform({
            transform: memoState,
            action: action
          });
          return memoState;
        });
      });
    }
    queue.current.push((0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, transform), newTransform));
  };

  /** Scale according to the position of centerX and centerY */
  var dispatchZoomChange = function dispatchZoomChange(ratio, action, centerX, centerY, isTouch) {
    var _imgRef$current = imgRef.current,
      width = _imgRef$current.width,
      height = _imgRef$current.height,
      offsetWidth = _imgRef$current.offsetWidth,
      offsetHeight = _imgRef$current.offsetHeight,
      offsetLeft = _imgRef$current.offsetLeft,
      offsetTop = _imgRef$current.offsetTop;
    var newRatio = ratio;
    var newScale = transform.scale * ratio;
    if (newScale > maxScale) {
      newScale = maxScale;
      newRatio = maxScale / transform.scale;
    } else if (newScale < minScale) {
      // For mobile interactions, allow scaling down to the minimum scale.
      newScale = isTouch ? newScale : minScale;
      newRatio = newScale / transform.scale;
    }

    /** Default center point scaling */
    var mergedCenterX = centerX !== null && centerX !== void 0 ? centerX : innerWidth / 2;
    var mergedCenterY = centerY !== null && centerY !== void 0 ? centerY : innerHeight / 2;
    var diffRatio = newRatio - 1;
    /** Deviation calculated from image size */
    var diffImgX = diffRatio * width * 0.5;
    var diffImgY = diffRatio * height * 0.5;
    /** The difference between the click position and the edge of the document */
    var diffOffsetLeft = diffRatio * (mergedCenterX - transform.x - offsetLeft);
    var diffOffsetTop = diffRatio * (mergedCenterY - transform.y - offsetTop);
    /** Final positioning */
    var newX = transform.x - (diffOffsetLeft - diffImgX);
    var newY = transform.y - (diffOffsetTop - diffImgY);

    /**
     * When zooming the image
     * When the image size is smaller than the width and height of the window, the position is initialized
     */
    if (ratio < 1 && newScale === 1) {
      var mergedWidth = offsetWidth * newScale;
      var mergedHeight = offsetHeight * newScale;
      var _getClientSize = (0,css/* getClientSize */.XV)(),
        clientWidth = _getClientSize.width,
        clientHeight = _getClientSize.height;
      if (mergedWidth <= clientWidth && mergedHeight <= clientHeight) {
        newX = 0;
        newY = 0;
      }
    }
    updateTransform({
      x: newX,
      y: newY,
      scale: newScale
    }, action);
  };
  return {
    transform: transform,
    resetTransform: resetTransform,
    updateTransform: updateTransform,
    dispatchZoomChange: dispatchZoomChange
  };
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(68210);
;// ./node_modules/rc-image/es/getFixScaleEleTransPosition.js



function fixPoint(key, start, width, clientWidth) {
  var startAddWidth = start + width;
  var offsetStart = (width - clientWidth) / 2;
  if (width > clientWidth) {
    if (start > 0) {
      return (0,defineProperty/* default */.A)({}, key, offsetStart);
    }
    if (start < 0 && startAddWidth < clientWidth) {
      return (0,defineProperty/* default */.A)({}, key, -offsetStart);
    }
  } else if (start < 0 || startAddWidth > clientWidth) {
    return (0,defineProperty/* default */.A)({}, key, start < 0 ? offsetStart : -offsetStart);
  }
  return {};
}

/**
 * Fix positon x,y point when
 *
 * Ele width && height < client
 * - Back origin
 *
 * - Ele width | height > clientWidth | clientHeight
 * - left | top > 0 -> Back 0
 * - left | top + width | height < clientWidth | clientHeight -> Back left | top + width | height === clientWidth | clientHeight
 *
 * Regardless of other
 */
function getFixScaleEleTransPosition(width, height, left, top) {
  var _getClientSize = (0,css/* getClientSize */.XV)(),
    clientWidth = _getClientSize.width,
    clientHeight = _getClientSize.height;
  var fixPos = null;
  if (width <= clientWidth && height <= clientHeight) {
    fixPos = {
      x: 0,
      y: 0
    };
  } else if (width > clientWidth || height > clientHeight) {
    fixPos = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, fixPoint('x', left, width, clientWidth)), fixPoint('y', top, height, clientHeight));
  }
  return fixPos;
}
;// ./node_modules/rc-image/es/previewConfig.js
/** Scale the ratio base */
var BASE_SCALE_RATIO = 1;
/** The maximum zoom ratio when the mouse zooms in, adjustable */
var WHEEL_MAX_SCALE_RATIO = 1;
;// ./node_modules/rc-image/es/hooks/useMouseEvent.js







function useMouseEvent(imgRef, movable, visible, scaleStep, transform, updateTransform, dispatchZoomChange) {
  var rotate = transform.rotate,
    scale = transform.scale,
    x = transform.x,
    y = transform.y;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    isMoving = _useState2[0],
    setMoving = _useState2[1];
  var startPositionInfo = (0,react.useRef)({
    diffX: 0,
    diffY: 0,
    transformX: 0,
    transformY: 0
  });
  var onMouseDown = function onMouseDown(event) {
    // Only allow main button
    if (!movable || event.button !== 0) return;
    event.preventDefault();
    event.stopPropagation();
    startPositionInfo.current = {
      diffX: event.pageX - x,
      diffY: event.pageY - y,
      transformX: x,
      transformY: y
    };
    setMoving(true);
  };
  var onMouseMove = function onMouseMove(event) {
    if (visible && isMoving) {
      updateTransform({
        x: event.pageX - startPositionInfo.current.diffX,
        y: event.pageY - startPositionInfo.current.diffY
      }, 'move');
    }
  };
  var onMouseUp = function onMouseUp() {
    if (visible && isMoving) {
      setMoving(false);

      /** No need to restore the position when the picture is not moved, So as not to interfere with the click */
      var _startPositionInfo$cu = startPositionInfo.current,
        transformX = _startPositionInfo$cu.transformX,
        transformY = _startPositionInfo$cu.transformY;
      var hasChangedPosition = x !== transformX && y !== transformY;
      if (!hasChangedPosition) return;
      var width = imgRef.current.offsetWidth * scale;
      var height = imgRef.current.offsetHeight * scale;
      // eslint-disable-next-line @typescript-eslint/no-shadow
      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),
        left = _imgRef$current$getBo.left,
        top = _imgRef$current$getBo.top;
      var isRotate = rotate % 180 !== 0;
      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);
      if (fixState) {
        updateTransform((0,objectSpread2/* default */.A)({}, fixState), 'dragRebound');
      }
    }
  };
  var onWheel = function onWheel(event) {
    if (!visible || event.deltaY == 0) return;
    // Scale ratio depends on the deltaY size
    var scaleRatio = Math.abs(event.deltaY / 100);
    // Limit the maximum scale ratio
    var mergedScaleRatio = Math.min(scaleRatio, WHEEL_MAX_SCALE_RATIO);
    // Scale the ratio each time
    var ratio = BASE_SCALE_RATIO + mergedScaleRatio * scaleStep;
    if (event.deltaY > 0) {
      ratio = BASE_SCALE_RATIO / ratio;
    }
    dispatchZoomChange(ratio, 'wheel', event.clientX, event.clientY);
  };
  (0,react.useEffect)(function () {
    var onTopMouseUpListener;
    var onTopMouseMoveListener;
    var onMouseUpListener;
    var onMouseMoveListener;
    if (movable) {
      onMouseUpListener = (0,addEventListener/* default */.A)(window, 'mouseup', onMouseUp, false);
      onMouseMoveListener = (0,addEventListener/* default */.A)(window, 'mousemove', onMouseMove, false);
      try {
        // Resolve if in iframe lost event
        /* istanbul ignore next */
        if (window.top !== window.self) {
          onTopMouseUpListener = (0,addEventListener/* default */.A)(window.top, 'mouseup', onMouseUp, false);
          onTopMouseMoveListener = (0,addEventListener/* default */.A)(window.top, 'mousemove', onMouseMove, false);
        }
      } catch (error) {
        /* istanbul ignore next */
        (0,warning/* warning */.$e)(false, "[rc-image] ".concat(error));
      }
    }
    return function () {
      var _onMouseUpListener, _onMouseMoveListener, _onTopMouseUpListener, _onTopMouseMoveListen;
      (_onMouseUpListener = onMouseUpListener) === null || _onMouseUpListener === void 0 || _onMouseUpListener.remove();
      (_onMouseMoveListener = onMouseMoveListener) === null || _onMouseMoveListener === void 0 || _onMouseMoveListener.remove();
      /* istanbul ignore next */
      (_onTopMouseUpListener = onTopMouseUpListener) === null || _onTopMouseUpListener === void 0 || _onTopMouseUpListener.remove();
      /* istanbul ignore next */
      (_onTopMouseMoveListen = onTopMouseMoveListener) === null || _onTopMouseMoveListen === void 0 || _onTopMouseMoveListen.remove();
    };
  }, [visible, isMoving, x, y, rotate, movable]);
  return {
    isMoving: isMoving,
    onMouseDown: onMouseDown,
    onMouseMove: onMouseMove,
    onMouseUp: onMouseUp,
    onWheel: onWheel
  };
}
;// ./node_modules/rc-image/es/util.js
function isImageValid(src) {
  return new Promise(function (resolve) {
    if (!src) {
      resolve(false);
      return;
    }
    var img = document.createElement('img');
    img.onerror = function () {
      return resolve(false);
    };
    img.onload = function () {
      return resolve(true);
    };
    img.src = src;
  });
}
;// ./node_modules/rc-image/es/hooks/useStatus.js



function useStatus(_ref) {
  var src = _ref.src,
    isCustomPlaceholder = _ref.isCustomPlaceholder,
    fallback = _ref.fallback;
  var _useState = (0,react.useState)(isCustomPlaceholder ? 'loading' : 'normal'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    status = _useState2[0],
    setStatus = _useState2[1];
  var isLoaded = (0,react.useRef)(false);
  var isError = status === 'error';

  // https://github.com/react-component/image/pull/187
  (0,react.useEffect)(function () {
    var isCurrentSrc = true;
    isImageValid(src).then(function (isValid) {
      // https://github.com/ant-design/ant-design/issues/44948
      // If src changes, the previous setStatus should not be triggered
      if (!isValid && isCurrentSrc) {
        setStatus('error');
      }
    });
    return function () {
      isCurrentSrc = false;
    };
  }, [src]);
  (0,react.useEffect)(function () {
    if (isCustomPlaceholder && !isLoaded.current) {
      setStatus('loading');
    } else if (isError) {
      setStatus('normal');
    }
  }, [src]);
  var onLoad = function onLoad() {
    setStatus('normal');
  };
  var getImgRef = function getImgRef(img) {
    isLoaded.current = false;
    if (status === 'loading' && img !== null && img !== void 0 && img.complete && (img.naturalWidth || img.naturalHeight)) {
      isLoaded.current = true;
      onLoad();
    }
  };
  var srcAndOnload = isError && fallback ? {
    src: fallback
  } : {
    onLoad: onLoad,
    src: src
  };
  return [getImgRef, srcAndOnload, status];
}
;// ./node_modules/rc-image/es/hooks/useTouchEvent.js





function getDistance(a, b) {
  var x = a.x - b.x;
  var y = a.y - b.y;
  return Math.hypot(x, y);
}
function getCenter(oldPoint1, oldPoint2, newPoint1, newPoint2) {
  // Calculate the distance each point has moved
  var distance1 = getDistance(oldPoint1, newPoint1);
  var distance2 = getDistance(oldPoint2, newPoint2);

  // If both distances are 0, return the original points
  if (distance1 === 0 && distance2 === 0) {
    return [oldPoint1.x, oldPoint1.y];
  }

  // Calculate the ratio of the distances
  var ratio = distance1 / (distance1 + distance2);

  // Calculate the new center point based on the ratio
  var x = oldPoint1.x + ratio * (oldPoint2.x - oldPoint1.x);
  var y = oldPoint1.y + ratio * (oldPoint2.y - oldPoint1.y);
  return [x, y];
}
function useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange) {
  var rotate = transform.rotate,
    scale = transform.scale,
    x = transform.x,
    y = transform.y;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    isTouching = _useState2[0],
    setIsTouching = _useState2[1];
  var touchPointInfo = (0,react.useRef)({
    point1: {
      x: 0,
      y: 0
    },
    point2: {
      x: 0,
      y: 0
    },
    eventType: 'none'
  });
  var updateTouchPointInfo = function updateTouchPointInfo(values) {
    touchPointInfo.current = (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, touchPointInfo.current), values);
  };
  var onTouchStart = function onTouchStart(event) {
    if (!movable) return;
    event.stopPropagation();
    setIsTouching(true);
    var _event$touches = event.touches,
      touches = _event$touches === void 0 ? [] : _event$touches;
    if (touches.length > 1) {
      // touch zoom
      updateTouchPointInfo({
        point1: {
          x: touches[0].clientX,
          y: touches[0].clientY
        },
        point2: {
          x: touches[1].clientX,
          y: touches[1].clientY
        },
        eventType: 'touchZoom'
      });
    } else {
      // touch move
      updateTouchPointInfo({
        point1: {
          x: touches[0].clientX - x,
          y: touches[0].clientY - y
        },
        eventType: 'move'
      });
    }
  };
  var onTouchMove = function onTouchMove(event) {
    var _event$touches2 = event.touches,
      touches = _event$touches2 === void 0 ? [] : _event$touches2;
    var _touchPointInfo$curre = touchPointInfo.current,
      point1 = _touchPointInfo$curre.point1,
      point2 = _touchPointInfo$curre.point2,
      eventType = _touchPointInfo$curre.eventType;
    if (touches.length > 1 && eventType === 'touchZoom') {
      // touch zoom
      var newPoint1 = {
        x: touches[0].clientX,
        y: touches[0].clientY
      };
      var newPoint2 = {
        x: touches[1].clientX,
        y: touches[1].clientY
      };
      var _getCenter = getCenter(point1, point2, newPoint1, newPoint2),
        _getCenter2 = (0,slicedToArray/* default */.A)(_getCenter, 2),
        centerX = _getCenter2[0],
        centerY = _getCenter2[1];
      var ratio = getDistance(newPoint1, newPoint2) / getDistance(point1, point2);
      dispatchZoomChange(ratio, 'touchZoom', centerX, centerY, true);
      updateTouchPointInfo({
        point1: newPoint1,
        point2: newPoint2,
        eventType: 'touchZoom'
      });
    } else if (eventType === 'move') {
      // touch move
      updateTransform({
        x: touches[0].clientX - point1.x,
        y: touches[0].clientY - point1.y
      }, 'move');
      updateTouchPointInfo({
        eventType: 'move'
      });
    }
  };
  var onTouchEnd = function onTouchEnd() {
    if (!visible) return;
    if (isTouching) {
      setIsTouching(false);
    }
    updateTouchPointInfo({
      eventType: 'none'
    });
    if (minScale > scale) {
      /** When the scaling ratio is less than the minimum scaling ratio, reset the scaling ratio */
      return updateTransform({
        x: 0,
        y: 0,
        scale: minScale
      }, 'touchZoom');
    }
    var width = imgRef.current.offsetWidth * scale;
    var height = imgRef.current.offsetHeight * scale;
    // eslint-disable-next-line @typescript-eslint/no-shadow
    var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),
      left = _imgRef$current$getBo.left,
      top = _imgRef$current$getBo.top;
    var isRotate = rotate % 180 !== 0;
    var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);
    if (fixState) {
      updateTransform((0,objectSpread2/* default */.A)({}, fixState), 'dragRebound');
    }
  };
  (0,react.useEffect)(function () {
    var onTouchMoveListener;
    if (visible && movable) {
      onTouchMoveListener = (0,addEventListener/* default */.A)(window, 'touchmove', function (e) {
        return e.preventDefault();
      }, {
        passive: false
      });
    }
    return function () {
      var _onTouchMoveListener;
      (_onTouchMoveListener = onTouchMoveListener) === null || _onTouchMoveListener === void 0 || _onTouchMoveListener.remove();
    };
  }, [visible, movable]);
  return {
    isTouching: isTouching,
    onTouchStart: onTouchStart,
    onTouchMove: onTouchMove,
    onTouchEnd: onTouchEnd
  };
}
;// ./node_modules/rc-image/es/Preview.js





var _excluded = ["fallback", "src", "imgRef"],
  _excluded2 = ["prefixCls", "src", "alt", "imageInfo", "fallback", "movable", "onClose", "visible", "icons", "rootClassName", "closeIcon", "getContainer", "current", "count", "countRender", "scaleStep", "minScale", "maxScale", "transitionName", "maskTransitionName", "imageRender", "imgCommonProps", "toolbarRender", "onTransform", "onChange"];












var PreviewImage = function PreviewImage(_ref) {
  var fallback = _ref.fallback,
    src = _ref.src,
    imgRef = _ref.imgRef,
    props = (0,objectWithoutProperties/* default */.A)(_ref, _excluded);
  var _useStatus = useStatus({
      src: src,
      fallback: fallback
    }),
    _useStatus2 = (0,slicedToArray/* default */.A)(_useStatus, 2),
    getImgRef = _useStatus2[0],
    srcAndOnload = _useStatus2[1];
  return /*#__PURE__*/react.createElement("img", (0,esm_extends/* default */.A)({
    ref: function ref(_ref2) {
      imgRef.current = _ref2;
      getImgRef(_ref2);
    }
  }, props, srcAndOnload));
};
var Preview = function Preview(props) {
  var prefixCls = props.prefixCls,
    src = props.src,
    alt = props.alt,
    imageInfo = props.imageInfo,
    fallback = props.fallback,
    _props$movable = props.movable,
    movable = _props$movable === void 0 ? true : _props$movable,
    onClose = props.onClose,
    visible = props.visible,
    _props$icons = props.icons,
    icons = _props$icons === void 0 ? {} : _props$icons,
    rootClassName = props.rootClassName,
    closeIcon = props.closeIcon,
    getContainer = props.getContainer,
    _props$current = props.current,
    current = _props$current === void 0 ? 0 : _props$current,
    _props$count = props.count,
    count = _props$count === void 0 ? 1 : _props$count,
    countRender = props.countRender,
    _props$scaleStep = props.scaleStep,
    scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,
    _props$minScale = props.minScale,
    minScale = _props$minScale === void 0 ? 1 : _props$minScale,
    _props$maxScale = props.maxScale,
    maxScale = _props$maxScale === void 0 ? 50 : _props$maxScale,
    _props$transitionName = props.transitionName,
    transitionName = _props$transitionName === void 0 ? 'zoom' : _props$transitionName,
    _props$maskTransition = props.maskTransitionName,
    maskTransitionName = _props$maskTransition === void 0 ? 'fade' : _props$maskTransition,
    imageRender = props.imageRender,
    imgCommonProps = props.imgCommonProps,
    toolbarRender = props.toolbarRender,
    onTransform = props.onTransform,
    onChange = props.onChange,
    restProps = (0,objectWithoutProperties/* default */.A)(props, _excluded2);
  var imgRef = (0,react.useRef)();
  var groupContext = (0,react.useContext)(PreviewGroupContext);
  var showLeftOrRightSwitches = groupContext && count > 1;
  var showOperationsProgress = groupContext && count >= 1;
  var _useState = (0,react.useState)(true),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    enableTransition = _useState2[0],
    setEnableTransition = _useState2[1];
  var _useImageTransform = useImageTransform(imgRef, minScale, maxScale, onTransform),
    transform = _useImageTransform.transform,
    resetTransform = _useImageTransform.resetTransform,
    updateTransform = _useImageTransform.updateTransform,
    dispatchZoomChange = _useImageTransform.dispatchZoomChange;
  var _useMouseEvent = useMouseEvent(imgRef, movable, visible, scaleStep, transform, updateTransform, dispatchZoomChange),
    isMoving = _useMouseEvent.isMoving,
    onMouseDown = _useMouseEvent.onMouseDown,
    onWheel = _useMouseEvent.onWheel;
  var _useTouchEvent = useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange),
    isTouching = _useTouchEvent.isTouching,
    onTouchStart = _useTouchEvent.onTouchStart,
    onTouchMove = _useTouchEvent.onTouchMove,
    onTouchEnd = _useTouchEvent.onTouchEnd;
  var rotate = transform.rotate,
    scale = transform.scale;
  var wrapClassName = classnames_default()((0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-moving"), isMoving));
  (0,react.useEffect)(function () {
    if (!enableTransition) {
      setEnableTransition(true);
    }
  }, [enableTransition]);
  var onAfterClose = function onAfterClose() {
    resetTransform('close');
  };
  var onZoomIn = function onZoomIn() {
    dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'zoomIn');
  };
  var onZoomOut = function onZoomOut() {
    dispatchZoomChange(BASE_SCALE_RATIO / (BASE_SCALE_RATIO + scaleStep), 'zoomOut');
  };
  var onRotateRight = function onRotateRight() {
    updateTransform({
      rotate: rotate + 90
    }, 'rotateRight');
  };
  var onRotateLeft = function onRotateLeft() {
    updateTransform({
      rotate: rotate - 90
    }, 'rotateLeft');
  };
  var onFlipX = function onFlipX() {
    updateTransform({
      flipX: !transform.flipX
    }, 'flipX');
  };
  var onFlipY = function onFlipY() {
    updateTransform({
      flipY: !transform.flipY
    }, 'flipY');
  };
  var onReset = function onReset() {
    resetTransform('reset');
  };
  var onActive = function onActive(offset) {
    var position = current + offset;
    if (!Number.isInteger(position) || position < 0 || position > count - 1) {
      return;
    }
    setEnableTransition(false);
    resetTransform(offset < 0 ? 'prev' : 'next');
    onChange === null || onChange === void 0 || onChange(position, current);
  };
  var onKeyDown = function onKeyDown(event) {
    if (!visible || !showLeftOrRightSwitches) return;
    if (event.keyCode === KeyCode/* default */.A.LEFT) {
      onActive(-1);
    } else if (event.keyCode === KeyCode/* default */.A.RIGHT) {
      onActive(1);
    }
  };
  var onDoubleClick = function onDoubleClick(event) {
    if (visible) {
      if (scale !== 1) {
        updateTransform({
          x: 0,
          y: 0,
          scale: 1
        }, 'doubleClick');
      } else {
        dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'doubleClick', event.clientX, event.clientY);
      }
    }
  };
  (0,react.useEffect)(function () {
    var onKeyDownListener = (0,addEventListener/* default */.A)(window, 'keydown', onKeyDown, false);
    return function () {
      onKeyDownListener.remove();
    };
  }, [visible, showLeftOrRightSwitches, current]);
  var imgNode = /*#__PURE__*/react.createElement(PreviewImage, (0,esm_extends/* default */.A)({}, imgCommonProps, {
    width: props.width,
    height: props.height,
    imgRef: imgRef,
    className: "".concat(prefixCls, "-img"),
    alt: alt,
    style: {
      transform: "translate3d(".concat(transform.x, "px, ").concat(transform.y, "px, 0) scale3d(").concat(transform.flipX ? '-' : '').concat(scale, ", ").concat(transform.flipY ? '-' : '').concat(scale, ", 1) rotate(").concat(rotate, "deg)"),
      transitionDuration: (!enableTransition || isTouching) && '0s'
    },
    fallback: fallback,
    src: src,
    onWheel: onWheel,
    onMouseDown: onMouseDown,
    onDoubleClick: onDoubleClick,
    onTouchStart: onTouchStart,
    onTouchMove: onTouchMove,
    onTouchEnd: onTouchEnd,
    onTouchCancel: onTouchEnd
  }));
  var image = (0,objectSpread2/* default */.A)({
    url: src,
    alt: alt
  }, imageInfo);
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* default */.A, (0,esm_extends/* default */.A)({
    transitionName: transitionName,
    maskTransitionName: maskTransitionName,
    closable: false,
    keyboard: true,
    prefixCls: prefixCls,
    onClose: onClose,
    visible: visible,
    classNames: {
      wrapper: wrapClassName
    },
    rootClassName: rootClassName,
    getContainer: getContainer
  }, restProps, {
    afterClose: onAfterClose
  }), /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-img-wrapper")
  }, imageRender ? imageRender(imgNode, (0,objectSpread2/* default */.A)({
    transform: transform,
    image: image
  }, groupContext ? {
    current: current
  } : {})) : imgNode)), /*#__PURE__*/react.createElement(es_Operations, {
    visible: visible,
    transform: transform,
    maskTransitionName: maskTransitionName,
    closeIcon: closeIcon,
    getContainer: getContainer,
    prefixCls: prefixCls,
    rootClassName: rootClassName,
    icons: icons,
    countRender: countRender,
    showSwitch: showLeftOrRightSwitches,
    showProgress: showOperationsProgress,
    current: current,
    count: count,
    scale: scale,
    minScale: minScale,
    maxScale: maxScale,
    toolbarRender: toolbarRender,
    onActive: onActive,
    onZoomIn: onZoomIn,
    onZoomOut: onZoomOut,
    onRotateRight: onRotateRight,
    onRotateLeft: onRotateLeft,
    onFlipX: onFlipX,
    onFlipY: onFlipY,
    onClose: onClose,
    onReset: onReset,
    zIndex: restProps.zIndex !== undefined ? restProps.zIndex + 1 : undefined,
    image: image
  }));
};
/* harmony default export */ const es_Preview = (Preview);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
;// ./node_modules/rc-image/es/common.js
var COMMON_PROPS = ['crossOrigin', 'decoding', 'draggable', 'loading', 'referrerPolicy', 'sizes', 'srcSet', 'useMap', 'alt'];
;// ./node_modules/rc-image/es/hooks/usePreviewItems.js






/**
 * Merge props provided `items` or context collected images
 */
function usePreviewItems(items) {
  // Context collection image data
  var _React$useState = react.useState({}),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    images = _React$useState2[0],
    setImages = _React$useState2[1];
  var registerImage = react.useCallback(function (id, data) {
    setImages(function (imgs) {
      return (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, imgs), {}, (0,defineProperty/* default */.A)({}, id, data));
    });
    return function () {
      setImages(function (imgs) {
        var cloneImgs = (0,objectSpread2/* default */.A)({}, imgs);
        delete cloneImgs[id];
        return cloneImgs;
      });
    };
  }, []);

  // items
  var mergedItems = react.useMemo(function () {
    // use `items` first
    if (items) {
      return items.map(function (item) {
        if (typeof item === 'string') {
          return {
            data: {
              src: item
            }
          };
        }
        var data = {};
        Object.keys(item).forEach(function (key) {
          if (['src'].concat((0,toConsumableArray/* default */.A)(COMMON_PROPS)).includes(key)) {
            data[key] = item[key];
          }
        });
        return {
          data: data
        };
      });
    }

    // use registered images secondly
    return Object.keys(images).reduce(function (total, id) {
      var _images$id = images[id],
        canPreview = _images$id.canPreview,
        data = _images$id.data;
      if (canPreview) {
        total.push({
          data: data,
          id: id
        });
      }
      return total;
    }, []);
  }, [items, images]);
  return [mergedItems, registerImage, !!items];
}
;// ./node_modules/rc-image/es/PreviewGroup.js




var PreviewGroup_excluded = ["visible", "onVisibleChange", "getContainer", "current", "movable", "minScale", "maxScale", "countRender", "closeIcon", "onChange", "onTransform", "toolbarRender", "imageRender"],
  PreviewGroup_excluded2 = ["src"];






var Group = function Group(_ref) {
  var _mergedItems$current;
  var _ref$previewPrefixCls = _ref.previewPrefixCls,
    previewPrefixCls = _ref$previewPrefixCls === void 0 ? 'rc-image-preview' : _ref$previewPrefixCls,
    children = _ref.children,
    _ref$icons = _ref.icons,
    icons = _ref$icons === void 0 ? {} : _ref$icons,
    items = _ref.items,
    preview = _ref.preview,
    fallback = _ref.fallback;
  var _ref2 = (0,esm_typeof/* default */.A)(preview) === 'object' ? preview : {},
    previewVisible = _ref2.visible,
    onVisibleChange = _ref2.onVisibleChange,
    getContainer = _ref2.getContainer,
    currentIndex = _ref2.current,
    movable = _ref2.movable,
    minScale = _ref2.minScale,
    maxScale = _ref2.maxScale,
    countRender = _ref2.countRender,
    closeIcon = _ref2.closeIcon,
    onChange = _ref2.onChange,
    onTransform = _ref2.onTransform,
    toolbarRender = _ref2.toolbarRender,
    imageRender = _ref2.imageRender,
    dialogProps = (0,objectWithoutProperties/* default */.A)(_ref2, PreviewGroup_excluded);

  // ========================== Items ===========================
  var _usePreviewItems = usePreviewItems(items),
    _usePreviewItems2 = (0,slicedToArray/* default */.A)(_usePreviewItems, 3),
    mergedItems = _usePreviewItems2[0],
    register = _usePreviewItems2[1],
    fromItems = _usePreviewItems2[2];

  // ========================= Preview ==========================
  // >>> Index
  var _useMergedState = (0,useMergedState/* default */.A)(0, {
      value: currentIndex
    }),
    _useMergedState2 = (0,slicedToArray/* default */.A)(_useMergedState, 2),
    current = _useMergedState2[0],
    setCurrent = _useMergedState2[1];
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    keepOpenIndex = _useState2[0],
    setKeepOpenIndex = _useState2[1];

  // >>> Image
  var _ref3 = ((_mergedItems$current = mergedItems[current]) === null || _mergedItems$current === void 0 ? void 0 : _mergedItems$current.data) || {},
    src = _ref3.src,
    imgCommonProps = (0,objectWithoutProperties/* default */.A)(_ref3, PreviewGroup_excluded2);
  // >>> Visible
  var _useMergedState3 = (0,useMergedState/* default */.A)(!!previewVisible, {
      value: previewVisible,
      onChange: function onChange(val, prevVal) {
        onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(val, prevVal, current);
      }
    }),
    _useMergedState4 = (0,slicedToArray/* default */.A)(_useMergedState3, 2),
    isShowPreview = _useMergedState4[0],
    setShowPreview = _useMergedState4[1];

  // >>> Position
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    mousePosition = _useState4[0],
    setMousePosition = _useState4[1];
  var onPreviewFromImage = react.useCallback(function (id, imageSrc, mouseX, mouseY) {
    var index = fromItems ? mergedItems.findIndex(function (item) {
      return item.data.src === imageSrc;
    }) : mergedItems.findIndex(function (item) {
      return item.id === id;
    });
    setCurrent(index < 0 ? 0 : index);
    setShowPreview(true);
    setMousePosition({
      x: mouseX,
      y: mouseY
    });
    setKeepOpenIndex(true);
  }, [mergedItems, fromItems]);

  // Reset current when reopen
  react.useEffect(function () {
    if (isShowPreview) {
      if (!keepOpenIndex) {
        setCurrent(0);
      }
    } else {
      setKeepOpenIndex(false);
    }
  }, [isShowPreview]);

  // ========================== Events ==========================
  var onInternalChange = function onInternalChange(next, prev) {
    setCurrent(next);
    onChange === null || onChange === void 0 || onChange(next, prev);
  };
  var onPreviewClose = function onPreviewClose() {
    setShowPreview(false);
    setMousePosition(null);
  };

  // ========================= Context ==========================
  var previewGroupContext = react.useMemo(function () {
    return {
      register: register,
      onPreview: onPreviewFromImage
    };
  }, [register, onPreviewFromImage]);

  // ========================== Render ==========================
  return /*#__PURE__*/react.createElement(PreviewGroupContext.Provider, {
    value: previewGroupContext
  }, children, /*#__PURE__*/react.createElement(es_Preview, (0,esm_extends/* default */.A)({
    "aria-hidden": !isShowPreview,
    movable: movable,
    visible: isShowPreview,
    prefixCls: previewPrefixCls,
    closeIcon: closeIcon,
    onClose: onPreviewClose,
    mousePosition: mousePosition,
    imgCommonProps: imgCommonProps,
    src: src,
    fallback: fallback,
    icons: icons,
    minScale: minScale,
    maxScale: maxScale,
    getContainer: getContainer,
    current: current,
    count: mergedItems.length,
    countRender: countRender,
    onTransform: onTransform,
    toolbarRender: toolbarRender,
    imageRender: imageRender,
    onChange: onInternalChange
  }, dialogProps)));
};
/* harmony default export */ const PreviewGroup = (Group);
;// ./node_modules/rc-image/es/hooks/useRegisterImage.js



var uid = 0;
function useRegisterImage(canPreview, data) {
  var _React$useState = react.useState(function () {
      uid += 1;
      return String(uid);
    }),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 1),
    id = _React$useState2[0];
  var groupContext = react.useContext(PreviewGroupContext);
  var registerData = {
    data: data,
    canPreview: canPreview
  };

  // Keep order start
  // Resolve https://github.com/ant-design/ant-design/issues/28881
  // Only need unRegister when component unMount
  react.useEffect(function () {
    if (groupContext) {
      return groupContext.register(id, registerData);
    }
  }, []);
  react.useEffect(function () {
    if (groupContext) {
      groupContext.register(id, registerData);
    }
  }, [canPreview, data]);
  return id;
}
;// ./node_modules/rc-image/es/Image.js






var Image_excluded = ["src", "alt", "onPreviewClose", "prefixCls", "previewPrefixCls", "placeholder", "fallback", "width", "height", "style", "preview", "className", "onClick", "onError", "wrapperClassName", "wrapperStyle", "rootClassName"],
  Image_excluded2 = ["src", "visible", "onVisibleChange", "getContainer", "mask", "maskClassName", "movable", "icons", "scaleStep", "minScale", "maxScale", "imageRender", "toolbarRender"];











var ImageInternal = function ImageInternal(props) {
  var imgSrc = props.src,
    alt = props.alt,
    onInitialPreviewClose = props.onPreviewClose,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-image' : _props$prefixCls,
    _props$previewPrefixC = props.previewPrefixCls,
    previewPrefixCls = _props$previewPrefixC === void 0 ? "".concat(prefixCls, "-preview") : _props$previewPrefixC,
    placeholder = props.placeholder,
    fallback = props.fallback,
    width = props.width,
    height = props.height,
    style = props.style,
    _props$preview = props.preview,
    preview = _props$preview === void 0 ? true : _props$preview,
    className = props.className,
    onClick = props.onClick,
    onError = props.onError,
    wrapperClassName = props.wrapperClassName,
    wrapperStyle = props.wrapperStyle,
    rootClassName = props.rootClassName,
    otherProps = (0,objectWithoutProperties/* default */.A)(props, Image_excluded);
  var isCustomPlaceholder = placeholder && placeholder !== true;
  var _ref = (0,esm_typeof/* default */.A)(preview) === 'object' ? preview : {},
    previewSrc = _ref.src,
    _ref$visible = _ref.visible,
    previewVisible = _ref$visible === void 0 ? undefined : _ref$visible,
    _ref$onVisibleChange = _ref.onVisibleChange,
    onPreviewVisibleChange = _ref$onVisibleChange === void 0 ? onInitialPreviewClose : _ref$onVisibleChange,
    _ref$getContainer = _ref.getContainer,
    getPreviewContainer = _ref$getContainer === void 0 ? undefined : _ref$getContainer,
    previewMask = _ref.mask,
    maskClassName = _ref.maskClassName,
    movable = _ref.movable,
    icons = _ref.icons,
    scaleStep = _ref.scaleStep,
    minScale = _ref.minScale,
    maxScale = _ref.maxScale,
    imageRender = _ref.imageRender,
    toolbarRender = _ref.toolbarRender,
    dialogProps = (0,objectWithoutProperties/* default */.A)(_ref, Image_excluded2);
  var src = previewSrc !== null && previewSrc !== void 0 ? previewSrc : imgSrc;
  var _useMergedState = (0,useMergedState/* default */.A)(!!previewVisible, {
      value: previewVisible,
      onChange: onPreviewVisibleChange
    }),
    _useMergedState2 = (0,slicedToArray/* default */.A)(_useMergedState, 2),
    isShowPreview = _useMergedState2[0],
    setShowPreview = _useMergedState2[1];
  var _useStatus = useStatus({
      src: imgSrc,
      isCustomPlaceholder: isCustomPlaceholder,
      fallback: fallback
    }),
    _useStatus2 = (0,slicedToArray/* default */.A)(_useStatus, 3),
    getImgRef = _useStatus2[0],
    srcAndOnload = _useStatus2[1],
    status = _useStatus2[2];
  var _useState = (0,react.useState)(null),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    mousePosition = _useState2[0],
    setMousePosition = _useState2[1];
  var groupContext = (0,react.useContext)(PreviewGroupContext);
  var canPreview = !!preview;
  var onPreviewClose = function onPreviewClose() {
    setShowPreview(false);
    setMousePosition(null);
  };
  var wrapperClass = classnames_default()(prefixCls, wrapperClassName, rootClassName, (0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-error"), status === 'error'));

  // ========================= ImageProps =========================
  var imgCommonProps = (0,react.useMemo)(function () {
    var obj = {};
    COMMON_PROPS.forEach(function (prop) {
      if (props[prop] !== undefined) {
        obj[prop] = props[prop];
      }
    });
    return obj;
  }, COMMON_PROPS.map(function (prop) {
    return props[prop];
  }));

  // ========================== Register ==========================
  var registerData = (0,react.useMemo)(function () {
    return (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, imgCommonProps), {}, {
      src: src
    });
  }, [src, imgCommonProps]);
  var imageId = useRegisterImage(canPreview, registerData);

  // ========================== Preview ===========================
  var onPreview = function onPreview(e) {
    var _getOffset = (0,css/* getOffset */.A3)(e.target),
      left = _getOffset.left,
      top = _getOffset.top;
    if (groupContext) {
      groupContext.onPreview(imageId, src, left, top);
    } else {
      setMousePosition({
        x: left,
        y: top
      });
      setShowPreview(true);
    }
    onClick === null || onClick === void 0 || onClick(e);
  };

  // =========================== Render ===========================
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.A)({}, otherProps, {
    className: wrapperClass,
    onClick: canPreview ? onPreview : onClick,
    style: (0,objectSpread2/* default */.A)({
      width: width,
      height: height
    }, wrapperStyle)
  }), /*#__PURE__*/react.createElement("img", (0,esm_extends/* default */.A)({}, imgCommonProps, {
    className: classnames_default()("".concat(prefixCls, "-img"), (0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-img-placeholder"), placeholder === true), className),
    style: (0,objectSpread2/* default */.A)({
      height: height
    }, style),
    ref: getImgRef
  }, srcAndOnload, {
    width: width,
    height: height,
    onError: onError
  })), status === 'loading' && /*#__PURE__*/react.createElement("div", {
    "aria-hidden": "true",
    className: "".concat(prefixCls, "-placeholder")
  }, placeholder), previewMask && canPreview && /*#__PURE__*/react.createElement("div", {
    className: classnames_default()("".concat(prefixCls, "-mask"), maskClassName),
    style: {
      display: (style === null || style === void 0 ? void 0 : style.display) === 'none' ? 'none' : undefined
    }
  }, previewMask)), !groupContext && canPreview && /*#__PURE__*/react.createElement(es_Preview, (0,esm_extends/* default */.A)({
    "aria-hidden": !isShowPreview,
    visible: isShowPreview,
    prefixCls: previewPrefixCls,
    onClose: onPreviewClose,
    mousePosition: mousePosition,
    src: src,
    alt: alt,
    imageInfo: {
      width: width,
      height: height
    },
    fallback: fallback,
    getContainer: getPreviewContainer,
    icons: icons,
    movable: movable,
    scaleStep: scaleStep,
    minScale: minScale,
    maxScale: maxScale,
    rootClassName: rootClassName,
    imageRender: imageRender,
    imgCommonProps: imgCommonProps,
    toolbarRender: toolbarRender
  }, dialogProps)));
};
ImageInternal.PreviewGroup = PreviewGroup;
if (false) {}
/* harmony default export */ const Image = (ImageInternal);
;// ./node_modules/rc-image/es/index.js


/* harmony default export */ const rc_image_es = (Image);

/***/ })

}]);