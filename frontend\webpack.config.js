const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');

// Load environment variables from .env files
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });

// Determine if we should use real API
const useRealApi = process.env.REACT_APP_USE_REAL_API === 'true';

// Environment variables
const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  // Entry point for the application
  entry: {
    main: ['./src/index.js']
  },

  // Output configuration
  output: {
    path: path.resolve(__dirname, 'build'),
    publicPath: '/',
    filename: 'static/js/[name].[contenthash:8].js',
    chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
    // Removed chunkFormat: 'array-push' to fix build error
  },

  // Module rules for different file types
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      },
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader']
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'static/media/[name].[hash:8][ext]'
        }
      }
    ]
  },

  // Development server configuration
  devServer: {
    host: '0.0.0.0',
    port: 3000,
    historyApiFallback: {
      index: '/index.html',
      disableDotRule: true,
      rewrites: [
        {
          from: /^\/api\/.*$/, to: function (context) {
            return context.parsedUrl.pathname;
          }
        },
        { from: /./, to: '/index.html' }
      ]
    },
    static: {
      directory: path.join(__dirname, 'public'),
      watch: false, // Disable file watching to prevent I/O errors
      staticOptions: {
        // Exclude index.html from static serving so webpack can serve its generated version
        ignore: ['**/index.html']
      }
    },
    hot: true,
    allowedHosts: 'all',
    // Only add proxy when using real API
    ...(useRealApi ? {
      proxy: {
        '/api': {
          target: process.env.API_TARGET || 'http://localhost:8000',
          changeOrigin: true,
          pathRewrite: { '^/api': '/api' },
          onProxyReq: (proxyReq) => {
            console.log('Proxying to:', proxyReq.path);
          },
          onError: (err, _req, res) => {
            console.error('Proxy error:', err);
            if (res && !res.headersSent) {
              res.writeHead(503, {
                'Content-Type': 'application/json',
              });
              res.end(JSON.stringify({
                error: 'Backend service unavailable',
                message: 'The backend service is currently unavailable. Please try again later.'
              }));
            }
          }
        },
        '/ws': {
          target: process.env.REACT_APP_WS_PROXY_TARGET || 'http://localhost:8000',
          changeOrigin: true,
          ws: true,
          pathRewrite: {
            '^/ws': '/ws'
          },
          headers: {
            'Upgrade': 'websocket',
            'Connection': 'upgrade',
          },
          logLevel: 'debug',
          onProxyReq: (proxyReq) => {
            const target = process.env.REACT_APP_WS_PROXY_TARGET || 'http://localhost:8000';
            console.log('🔌 Proxying WebSocket to:', target, proxyReq.path);
          },
          onError: (err, _req, res) => {
            console.error('🔌 WebSocket proxy error:', err);
            // Don't try to write response for WebSocket upgrade requests
            if (res && !res.headersSent && !res.upgrade) {
              res.writeHead(503, {
                'Content-Type': 'application/json',
              });
              res.end(JSON.stringify({
                error: 'WebSocket proxy error',
                message: 'The WebSocket service is currently unavailable. Please try again later.'
              }));
            }
          }
        }
      }
    } : {})
  },

  // Plugins
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html',
      filename: 'index.html'
    }),
    new MiniCssExtractPlugin({
      filename: 'static/css/[name].[contenthash:8].css'
    }),
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer']
    }),
    // Define process.env for client-side code
    new webpack.DefinePlugin({
      'process.env': JSON.stringify(process.env)
    }),
    // Explicitly provide process/browser
    new webpack.ProvidePlugin({
      process: 'process/browser'
    })
  ],

  // Resolve configuration
  // Enhanced optimization configuration for better chunk splitting and lazy loading
  optimization: {
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 240000, // 240KB limit for initial bundle (slightly under target)
      maxInitialRequests: 3, // Limit initial requests for faster loading
      maxAsyncRequests: 30,
      automaticNameDelimiter: '.',
      cacheGroups: {
        // Critical vendor libraries (loaded immediately)
        criticalVendor: {
          test: /[\\/]node_modules[\\/](react|react-dom|react-router|react-router-dom)[\\/]/,
          name: 'critical-vendor',
          chunks: 'initial',
          priority: 40,
          reuseExistingChunk: true,
          enforce: true,
        },
        // Ant Design core (lazy loaded)
        antdCore: {
          test: /[\\/]node_modules[\\/]antd[\\/]es[\\/](button|input|form|layout|grid|space|typography)[\\/]/,
          name: 'antd-core',
          chunks: 'async',
          priority: 35,
          reuseExistingChunk: true,
          enforce: true,
        },
        // Ant Design advanced components (lazy loaded)
        antdAdvanced: {
          test: /[\\/]node_modules[\\/]antd[\\/]es[\\/](?!(button|input|form|layout|grid|space|typography))/,
          name: 'antd-advanced',
          chunks: 'async',
          priority: 30,
          reuseExistingChunk: true,
          enforce: true,
        },
        // Ant Design icons (lazy loaded)
        antdIcons: {
          test: /[\\/]node_modules[\\/]@ant-design[\\/]icons[\\/]/,
          name: 'antd-icons',
          chunks: 'async',
          priority: 25,
          reuseExistingChunk: true,
          enforce: true,
        },
        // Feature-specific chunks
        tutorialFeature: {
          test: /[\\/]src[\\/]components[\\/](tutorial|ai|templates|export|collaboration)[\\/]/,
          name: 'features',
          chunks: 'async',
          priority: 22,
          reuseExistingChunk: true,
          minChunks: 1,
        },
        // Property editors (lazy loaded)
        propertyEditors: {
          test: /[\\/]src[\\/]components[\\/]properties[\\/]/,
          name: 'property-editors',
          chunks: 'async',
          priority: 21,
          reuseExistingChunk: true,
          minChunks: 1,
        },
        // Other vendor libraries (lazy loaded)
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'async',
          priority: 15,
          reuseExistingChunk: true,
          minChunks: 1,
          maxSize: 150000, // Even smaller chunks for vendors
        },
        // Common utilities and hooks
        common: {
          test: /[\\/]src[\\/](utils|hooks|contexts)[\\/]/,
          name: 'common',
          chunks: 'all',
          priority: 10,
          reuseExistingChunk: true,
          minChunks: 2,
          minSize: 10000,
        },
        // Default group for remaining code
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
          chunks: 'async',
        }
      }
    },
    runtimeChunk: {
      name: 'runtime'
    },
    // Enable module concatenation for better tree shaking
    concatenateModules: true,
    // Enable side effects optimization
    sideEffects: false,
    // Enable usedExports for better tree shaking
    usedExports: true,
    // Minimize initial bundle size
    minimize: isProduction,
    minimizer: isProduction ? [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug'],
          },
          mangle: {
            safari10: true,
          },
          output: {
            comments: false,
            ascii_only: true,
          },
        },
        extractComments: false,
      }),
    ] : [],
  },

  resolve: {
    extensions: ['.js', '.jsx', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'process/browser': require.resolve('process/browser')
    },
    fallback: {
      "stream": require.resolve("stream-browserify"),
      "buffer": require.resolve("buffer/"),
      "util": require.resolve("util/"),
      "process": require.resolve("process/browser"),
      "path": require.resolve("path-browserify"),
      "os": require.resolve("os-browserify/browser"),
      "crypto": require.resolve("crypto-browserify"),
      "assert": require.resolve("assert/"),
      "url": require.resolve("url/"),
      "querystring": require.resolve("querystring-es3"),
      "constants": require.resolve("constants-browserify"),
      "vm": require.resolve("vm-browserify"),
      "tty": require.resolve("tty-browserify"),
      "fs": false,
      // Explicitly set these to false to avoid axios Node.js adapter issues
      "http": false,
      "https": false,
      "zlib": false
    }
  },
  target: 'web', // Ensure build targets browsers
};






