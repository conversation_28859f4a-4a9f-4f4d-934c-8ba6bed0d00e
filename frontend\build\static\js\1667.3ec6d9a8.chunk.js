"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1667],{

/***/ 71667:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ ThemeManager)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./src/redux/actions.js
var actions = __webpack_require__(81616);
// EXTERNAL MODULE: ./src/redux/actions/types.js
var actions_types = __webpack_require__(4318);
;// ./src/redux/actions/themeActions.js
/**
 * Theme Actions
 * 
 * This file contains action creators for theme-related actions.
 */



/**
 * Add a new theme
 * @param {Object} theme - Theme object to add
 * @returns {Object} Action object
 */
var addTheme = function addTheme(theme) {
  return {
    type: types.ADD_THEME,
    payload: theme
  };
};

/**
 * Update an existing theme
 * @param {Object} theme - Updated theme object
 * @returns {Object} Action object
 */
var updateTheme = function updateTheme(theme) {
  return {
    type: types.UPDATE_THEME,
    payload: theme
  };
};

/**
 * Remove a theme
 * @param {string} themeId - ID of the theme to remove
 * @returns {Object} Action object
 */
var removeTheme = function removeTheme(themeId) {
  return {
    type: types.REMOVE_THEME,
    payload: {
      id: themeId
    }
  };
};

/**
 * Set the active theme
 * @param {string} themeId - ID of the theme to set as active
 * @returns {Object} Action object
 */
var setActiveTheme = function setActiveTheme(themeId) {
  return {
    type: types.SET_ACTIVE_THEME,
    payload: themeId
  };
};

/**
 * Save user theme preference
 * @param {string} themeId - ID of the theme to save as preference
 * @returns {Object} Action object
 */
var saveUserThemePreference = function saveUserThemePreference(themeId) {
  return {
    type: types.SAVE_USER_THEME_PREFERENCE,
    payload: themeId
  };
};

/**
 * Toggle auto-apply theme setting
 * @returns {Object} Action object
 */
var toggleAutoApplyTheme = function toggleAutoApplyTheme() {
  return {
    type: actions_types/* TOGGLE_AUTO_APPLY_THEME */._E
  };
};

/**
 * Load user theme preferences from localStorage
 * @returns {Function} Thunk function
 */
var loadUserThemePreferences = function loadUserThemePreferences() {
  return function (dispatch) {
    try {
      // Try to get saved preferences from localStorage
      var savedPreferences = localStorage.getItem('themePreferences');
      if (savedPreferences) {
        var preferences = JSON.parse(savedPreferences);

        // If there's a saved theme, apply it
        if (preferences.savedTheme) {
          dispatch(setActiveTheme(preferences.savedTheme));
        }

        // If auto-apply setting exists and is different from default, update it
        if (preferences.autoApplyTheme !== undefined && preferences.autoApplyTheme !== true) {
          dispatch(toggleAutoApplyTheme());
        }
      }
    } catch (error) {
      console.error('Error loading theme preferences:', error);
    }
  };
};

/**
 * Save user theme preferences to localStorage
 * @returns {Function} Thunk function
 */
var saveUserThemePreferences = function saveUserThemePreferences() {
  return function (dispatch, getState) {
    try {
      var userPreferences = getState().themes.userPreferences;
      localStorage.setItem('themePreferences', JSON.stringify(userPreferences));
    } catch (error) {
      console.error('Error saving theme preferences:', error);
    }
  };
};
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
// EXTERNAL MODULE: ./src/design-system/theme.js
var theme = __webpack_require__(86020);
;// ./src/components/enhanced/ThemeManager.js



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









var ThemeManagerContainer = design_system.styled.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);
var ThemeGrid = design_system.styled.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);
var ColorInput = design_system.styled.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n\n  .color-preview {\n    width: 36px;\n    height: 36px;\n    border-radius: ", ";\n    border: 1px solid ", ";\n    overflow: hidden;\n    position: relative;\n\n    input[type=\"color\"] {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: none;\n      padding: 0;\n      margin: 0;\n      cursor: pointer;\n    }\n  }\n\n  .color-input {\n    flex: 1;\n  }\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.colors */.Ay.colors.neutral[300]);
var FontSelector = design_system.styled.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n\n  .font-preview {\n    padding: ", ";\n    border: 1px solid ", ";\n    border-radius: ", ";\n    min-height: 60px;\n  }\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.neutral[300], theme/* default.borderRadius */.Ay.borderRadius.md);
var ThemePreview = design_system.styled.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", ";\n  border-radius: ", ";\n  background-color: ", ";\n  color: ", ";\n  font-family: ", ";\n  transition: all 0.3s ease;\n\n  h3 {\n    margin-top: 0;\n    margin-bottom: ", ";\n    color: ", ";\n  }\n\n  p {\n    margin-bottom: ", ";\n  }\n\n  .buttons {\n    display: flex;\n    gap: ", ";\n  }\n\n  .primary-button {\n    padding: ", " ", ";\n    background-color: ", ";\n    color: white;\n    border: none;\n    border-radius: ", ";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .secondary-button {\n    padding: ", " ", ";\n    background-color: ", ";\n    color: white;\n    border: none;\n    border-radius: ", ";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .card-example {\n    margin-top: ", ";\n    padding: ", ";\n    border-radius: ", ";\n    background-color: ", ";\n    border: 1px solid ", ";\n  }\n\n  .input-example {\n    margin-top: ", ";\n    padding: ", ";\n    border-radius: ", ";\n    border: 1px solid ", ";\n    background-color: ", ";\n    color: ", ";\n    width: 100%;\n    font-family: ", ";\n  }\n"])), theme/* default.spacing */.Ay.spacing[4], theme/* default.borderRadius */.Ay.borderRadius.md, function (props) {
  return props.backgroundColor || 'white';
}, function (props) {
  return props.textColor || 'black';
}, function (props) {
  return props.fontFamily || 'inherit';
}, theme/* default.spacing */.Ay.spacing[3], function (props) {
  return props.textColor || 'black';
}, theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3], function (props) {
  return props.primaryColor || theme/* default.colors */.Ay.colors.primary.main;
}, theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3], function (props) {
  return props.secondaryColor || theme/* default.colors */.Ay.colors.secondary.main;
}, theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[3], theme/* default.borderRadius */.Ay.borderRadius.md, function (props) {
  return props.backgroundColor === '#FFFFFF' ? '#F9FAFB' : 'rgba(255, 255, 255, 0.1)';
}, function (props) {
  return props.backgroundColor === '#FFFFFF' ? '#E5E7EB' : 'rgba(255, 255, 255, 0.2)';
}, theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[2], theme/* default.borderRadius */.Ay.borderRadius.sm, function (props) {
  return props.backgroundColor === '#FFFFFF' ? '#D1D5DB' : 'rgba(255, 255, 255, 0.2)';
}, function (props) {
  return props.backgroundColor === '#FFFFFF' ? 'white' : 'rgba(255, 255, 255, 0.05)';
}, function (props) {
  return props.textColor;
}, function (props) {
  return props.fontFamily;
});
var ColorPalette = design_system.styled.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: ", ";\n  margin-top: ", ";\n\n  .color-swatch {\n    width: 24px;\n    height: 24px;\n    border-radius: 50%;\n    border: 1px solid ", ";\n    cursor: pointer;\n  }\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.neutral[300]);
var PropertyEditor = design_system.styled.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[3]);
var PropertyGroup = design_system.styled.div(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2]);
var EmptyState = design_system.styled.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ", ";\n  background-color: ", ";\n  border-radius: ", ";\n  text-align: center;\n"])), theme/* default.spacing */.Ay.spacing[8], theme/* default.colors */.Ay.colors.neutral[100], theme/* default.borderRadius */.Ay.borderRadius.md);
var ThemeCard = (0,design_system.styled)(design_system.Card)(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  border: ", ";\n  transition: ", ";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ", ";\n  }\n"])), function (props) {
  return props.isActive ? "2px solid ".concat(theme/* default.colors */.Ay.colors.primary.main) : 'none';
}, theme/* default.transitions */.Ay.transitions["default"], theme/* default.shadows */.Ay.shadows.md);
var fontFamilies = ['Inter, sans-serif', 'Arial, sans-serif', 'Helvetica, sans-serif', 'Georgia, serif', 'Times New Roman, serif', 'Courier New, monospace', 'Verdana, sans-serif', 'Roboto, sans-serif', 'Open Sans, sans-serif', 'Lato, sans-serif'];
var colorPalettes = [{
  name: 'Blue',
  primary: '#2563EB',
  secondary: '#10B981',
  background: '#FFFFFF',
  text: '#111827'
}, {
  name: 'Purple',
  primary: '#8B5CF6',
  secondary: '#EC4899',
  background: '#FFFFFF',
  text: '#111827'
}, {
  name: 'Green',
  primary: '#10B981',
  secondary: '#3B82F6',
  background: '#FFFFFF',
  text: '#111827'
}, {
  name: 'Red',
  primary: '#EF4444',
  secondary: '#F59E0B',
  background: '#FFFFFF',
  text: '#111827'
}, {
  name: 'Dark',
  primary: '#3B82F6',
  secondary: '#10B981',
  background: '#111827',
  text: '#F9FAFB'
}, {
  name: 'Monochrome',
  primary: '#000000',
  secondary: '#666666',
  background: '#FFFFFF',
  text: '#333333'
}, {
  name: 'Sunset',
  primary: '#FF5733',
  secondary: '#FFC300',
  background: '#FFFAF0',
  text: '#333333'
}, {
  name: 'Ocean',
  primary: '#1A5276',
  secondary: '#2E86C1',
  background: '#EBF5FB',
  text: '#17202A'
}, {
  name: 'Forest',
  primary: '#1E8449',
  secondary: '#F1C40F',
  background: '#F4F6F6',
  text: '#145A32'
}, {
  name: 'Night Mode',
  primary: '#BB86FC',
  secondary: '#03DAC5',
  background: '#121212',
  text: '#E1E1E1'
}];
var EnhancedThemeManager = function EnhancedThemeManager() {
  var _themes$find, _themes$find2;
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  // Get themes from Redux store with error handling
  var themes = (0,react_redux/* useSelector */.d4)(function (state) {
    // Ensure we're getting an array or return an empty array
    if (!state || !state.themes) {
      console.warn('Redux state or themes slice not found, using fallback values');
      return [];
    }
    return Array.isArray(state.themes.themes) ? state.themes.themes : [];
  });

  // Get active theme from Redux store with error handling
  var activeTheme = (0,react_redux/* useSelector */.d4)(function (state) {
    if (!state || !state.themes) {
      return 'default';
    }
    return state.themes.activeTheme || 'default';
  });

  // Get user preferences from Redux store
  var userPreferences = (0,react_redux/* useSelector */.d4)(function (state) {
    if (!state || !state.themes || !state.themes.userPreferences) {
      return {
        savedTheme: null,
        autoApplyTheme: true
      };
    }
    return state.themes.userPreferences;
  });
  var _useState = (0,react.useState)(''),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    themeName = _useState2[0],
    setThemeName = _useState2[1];
  var _useState3 = (0,react.useState)('#2563EB'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    primaryColor = _useState4[0],
    setPrimaryColor = _useState4[1];
  var _useState5 = (0,react.useState)('#10B981'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    secondaryColor = _useState6[0],
    setSecondaryColor = _useState6[1];
  var _useState7 = (0,react.useState)('#FFFFFF'),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    backgroundColor = _useState8[0],
    setBackgroundColor = _useState8[1];
  var _useState9 = (0,react.useState)('#111827'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    textColor = _useState0[0],
    setTextColor = _useState0[1];
  var _useState1 = (0,react.useState)('Inter, sans-serif'),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    fontFamily = _useState10[0],
    setFontFamily = _useState10[1];
  var _useState11 = (0,react.useState)(null),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    selectedTheme = _useState12[0],
    setSelectedTheme = _useState12[1];
  var _useState13 = (0,react.useState)(false),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    editMode = _useState14[0],
    setEditMode = _useState14[1];
  var _useState15 = (0,react.useState)({}),
    _useState16 = (0,slicedToArray/* default */.A)(_useState15, 2),
    errors = _useState16[0],
    setErrors = _useState16[1];

  // Add default theme if it doesn't exist - only run once on mount
  (0,react.useEffect)(function () {
    var defaultTheme = {
      id: 'default',
      name: 'Default Theme',
      primaryColor: '#2563EB',
      secondaryColor: '#10B981',
      backgroundColor: '#FFFFFF',
      textColor: '#111827',
      fontFamily: 'Inter, sans-serif'
    };

    // Safely check if themes is an array and if it's empty
    if (!Array.isArray(themes) || themes.length === 0) {
      try {
        dispatch((0,actions/* addTheme */.zp)(defaultTheme));
      } catch (error) {
        console.error('Error dispatching addTheme action:', error);
      }
    }
    if (!activeTheme) {
      try {
        dispatch((0,actions/* setActiveTheme */.Ic)('default'));
      } catch (error) {
        console.error('Error dispatching setActiveTheme action:', error);
      }
    }
    // Only run this effect once on component mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  // Add service worker message listener - only run once on mount
  (0,react.useEffect)(function () {
    var handleServiceWorkerMessage = function handleServiceWorkerMessage(event) {
      if (event.data && event.data.type === 'THEME_CACHE_UPDATED') {
        console.log('Theme cache updated at:', event.data.timestamp);
      }
    };

    // Add event listener
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
    }

    // Clean up
    return function () {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
    // We intentionally don't include dependencies here to avoid re-registering the listener
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  var validateForm = function validateForm() {
    var newErrors = {};
    if (!themeName.trim()) {
      newErrors.name = 'Theme name is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  var handleAddTheme = function handleAddTheme() {
    if (!validateForm()) return;
    var newTheme = {
      id: Date.now().toString(),
      name: themeName.trim(),
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundColor: backgroundColor,
      textColor: textColor,
      fontFamily: fontFamily,
      createdAt: new Date().toISOString()
    };
    dispatch((0,actions/* addTheme */.zp)(newTheme));

    // Notify service worker to update theme cache
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'THEME_UPDATED',
        theme: newTheme
      });

      // Also update the theme cache
      navigator.serviceWorker.controller.postMessage({
        type: 'UPDATE_THEME_CACHE'
      });
    }

    // Reset form
    setThemeName('');
    setPrimaryColor('#2563EB');
    setSecondaryColor('#10B981');
    setBackgroundColor('#FFFFFF');
    setTextColor('#111827');
    setFontFamily('Inter, sans-serif');
    setErrors({});
  };
  var handleUpdateTheme = function handleUpdateTheme() {
    if (!selectedTheme || !validateForm()) return;
    var updatedTheme = _objectSpread(_objectSpread({}, selectedTheme), {}, {
      name: themeName.trim(),
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundColor: backgroundColor,
      textColor: textColor,
      fontFamily: fontFamily,
      updatedAt: new Date().toISOString()
    });

    // Update the theme in the store
    dispatch((0,actions/* updateTheme */.V_)(updatedTheme));

    // Notify service worker to update theme cache
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'THEME_UPDATED',
        theme: updatedTheme
      });

      // Also update the theme cache
      navigator.serviceWorker.controller.postMessage({
        type: 'UPDATE_THEME_CACHE'
      });
    }

    // Reset form and exit edit mode
    setThemeName('');
    setPrimaryColor('#2563EB');
    setSecondaryColor('#10B981');
    setBackgroundColor('#FFFFFF');
    setTextColor('#111827');
    setFontFamily('Inter, sans-serif');
    setSelectedTheme(null);
    setEditMode(false);
    setErrors({});
  };
  var handleRemoveTheme = function handleRemoveTheme(id) {
    // Don't allow removing the default theme
    if (id === 'default') return;

    // Remove the theme from the store
    dispatch((0,actions/* removeTheme */.Qo)(id));

    // If the removed theme was selected, reset the form
    if (selectedTheme && selectedTheme.id === id) {
      setThemeName('');
      setPrimaryColor('#2563EB');
      setSecondaryColor('#10B981');
      setBackgroundColor('#FFFFFF');
      setTextColor('#111827');
      setFontFamily('Inter, sans-serif');
      setSelectedTheme(null);
      setEditMode(false);
    }

    // If the removed theme was active, set the default theme as active
    if (activeTheme === id) {
      dispatch((0,actions/* setActiveTheme */.Ic)('default'));
    }
  };
  var handleSelectTheme = function handleSelectTheme(theme) {
    setSelectedTheme(theme);
    setThemeName(theme.name);
    setPrimaryColor(theme.primaryColor);
    setSecondaryColor(theme.secondaryColor);
    setBackgroundColor(theme.backgroundColor);
    setTextColor(theme.textColor);
    setFontFamily(theme.fontFamily);
    setEditMode(true);
    setErrors({});
  };
  var handleCancelEdit = function handleCancelEdit() {
    setThemeName('');
    setPrimaryColor('#2563EB');
    setSecondaryColor('#10B981');
    setBackgroundColor('#FFFFFF');
    setTextColor('#111827');
    setFontFamily('Inter, sans-serif');
    setSelectedTheme(null);
    setEditMode(false);
    setErrors({});
  };
  var handleDuplicateTheme = function handleDuplicateTheme(theme) {
    var duplicatedTheme = _objectSpread(_objectSpread({}, theme), {}, {
      id: Date.now().toString(),
      name: "".concat(theme.name, " (Copy)"),
      createdAt: new Date().toISOString()
    });
    dispatch((0,actions/* addTheme */.zp)(duplicatedTheme));
  };

  // Handle toggling auto-apply theme setting
  var handleToggleAutoApply = function handleToggleAutoApply() {
    dispatch(toggleAutoApplyTheme());
    dispatch(saveUserThemePreferences());

    // Show success message
    var newState = !userPreferences.autoApplyTheme;
    es/* message */.iU.success("Auto-apply theme ".concat(newState ? 'enabled' : 'disabled'));
  };
  var handleSetActiveTheme = function handleSetActiveTheme(themeId) {
    try {
      // Set the active theme in the store
      dispatch((0,actions/* setActiveTheme */.Ic)(themeId));

      // Show success message
      es/* message */.iU.success("Theme activated successfully");

      // Find the theme object
      var defaultTheme = {
        id: 'default',
        name: 'Default Theme',
        primaryColor: '#2563EB',
        secondaryColor: '#10B981',
        backgroundColor: '#FFFFFF',
        textColor: '#111827',
        fontFamily: 'Inter, sans-serif'
      };

      // Safely handle themes array without using spread operator
      var safeThemes = Array.isArray(themes) ? themes : [];
      var _activeTheme = safeThemes.concat([defaultTheme]).find(function (theme) {
        return theme.id === themeId;
      });

      // Notify service worker about the active theme change
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller && _activeTheme) {
        navigator.serviceWorker.controller.postMessage({
          type: 'THEME_UPDATED',
          theme: _activeTheme
        });
      }
    } catch (error) {
      console.error('Error setting active theme:', error);
      es/* message */.iU.error('Failed to activate theme. Please try again.');
    }
  };
  var handleApplyPalette = function handleApplyPalette(palette) {
    setPrimaryColor(palette.primary);
    setSecondaryColor(palette.secondary);
    setBackgroundColor(palette.background);
    setTextColor(palette.text);
  };
  var getActiveThemeObject = function getActiveThemeObject() {
    var defaultTheme = {
      id: 'default',
      name: 'Default Theme',
      primaryColor: '#2563EB',
      secondaryColor: '#10B981',
      backgroundColor: '#FFFFFF',
      textColor: '#111827',
      fontFamily: 'Inter, sans-serif'
    };

    // Safely handle themes array without using spread operator
    var safeThemes = Array.isArray(themes) ? themes : [];

    // If activeTheme is not defined or not found in themes, return defaultTheme
    if (!activeTheme) {
      return defaultTheme;
    }

    // Find the active theme in the themes array
    var foundTheme = safeThemes.concat([defaultTheme]).find(function (theme) {
      return theme.id === activeTheme;
    });

    // Return the found theme or defaultTheme if not found
    return foundTheme || defaultTheme;
  };

  // Export theme to JSON
  var handleExportTheme = function handleExportTheme(theme) {
    var themeToExport = theme || selectedTheme;
    if (!themeToExport) return;

    // Create a JSON string
    var themeJson = JSON.stringify(themeToExport, null, 2);

    // Create a blob
    var blob = new Blob([themeJson], {
      type: 'application/json'
    });

    // Create a URL for the blob
    var url = URL.createObjectURL(blob);

    // Create a link element
    var link = document.createElement('a');
    link.href = url;
    link.download = "".concat(themeToExport.name.replace(/\s+/g, '-').toLowerCase(), "-theme.json");

    // Append the link to the body
    document.body.appendChild(link);

    // Click the link
    link.click();

    // Remove the link
    document.body.removeChild(link);

    // Revoke the URL
    URL.revokeObjectURL(url);
  };

  // Import theme from JSON
  var handleImportTheme = function handleImportTheme(event) {
    var file = event.target.files[0];
    if (!file) return;
    var reader = new FileReader();
    reader.onload = function (e) {
      try {
        var importedTheme = JSON.parse(e.target.result);

        // Validate the theme
        if (!importedTheme.name || !importedTheme.primaryColor || !importedTheme.secondaryColor || !importedTheme.backgroundColor || !importedTheme.textColor || !importedTheme.fontFamily) {
          throw new Error('Invalid theme format');
        }

        // Create a new theme with a new ID
        var newTheme = _objectSpread(_objectSpread({}, importedTheme), {}, {
          id: Date.now().toString(),
          name: "".concat(importedTheme.name, " (Imported)"),
          createdAt: new Date().toISOString()
        });

        // Add the theme
        dispatch((0,actions/* addTheme */.zp)(newTheme));

        // Notify service worker
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: 'THEME_UPDATED',
            theme: newTheme
          });
          navigator.serviceWorker.controller.postMessage({
            type: 'UPDATE_THEME_CACHE'
          });
        }

        // Reset the file input
        event.target.value = '';
      } catch (error) {
        console.error('Error importing theme:', error);
        // Reset the file input
        event.target.value = '';
      }
    };
    reader.readAsText(file);
  };
  return /*#__PURE__*/react.createElement(ThemeManagerContainer, null, /*#__PURE__*/react.createElement(design_system.Card, null, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement(design_system.Card.Title, null, editMode ? 'Edit Theme' : 'Create Theme'), editMode && /*#__PURE__*/react.createElement(design_system.Button, {
    variant: "text",
    size: "small",
    onClick: handleCancelEdit,
    startIcon: /*#__PURE__*/react.createElement(icons_es/* CloseOutlined */.r$3, null)
  }, "Cancel")), /*#__PURE__*/react.createElement(design_system.Card.Content, null, /*#__PURE__*/react.createElement(PropertyEditor, null, /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement(design_system.Input, {
    label: "Theme Name",
    value: themeName,
    onChange: function onChange(e) {
      return setThemeName(e.target.value);
    },
    placeholder: "Enter theme name",
    fullWidth: true,
    error: !!errors.name,
    helperText: errors.name
  })), /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: theme/* default.spacing */.Ay.spacing[2]
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      marginBottom: theme/* default.spacing */.Ay.spacing[2]
    }
  }, "Color Palettes"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      flexWrap: 'wrap',
      gap: theme/* default.spacing */.Ay.spacing[2]
    }
  }, colorPalettes.map(function (palette, index) {
    return /*#__PURE__*/react.createElement(design_system.Button, {
      key: index,
      variant: "outline",
      size: "small",
      onClick: function onClick() {
        return handleApplyPalette(palette);
      }
    }, palette.name);
  }))), /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement("label", null, "Primary Color"), /*#__PURE__*/react.createElement(ColorInput, null, /*#__PURE__*/react.createElement("div", {
    className: "color-preview"
  }, /*#__PURE__*/react.createElement("input", {
    type: "color",
    value: primaryColor,
    onChange: function onChange(e) {
      return setPrimaryColor(e.target.value);
    }
  })), /*#__PURE__*/react.createElement(design_system.Input, {
    className: "color-input",
    value: primaryColor,
    onChange: function onChange(e) {
      return setPrimaryColor(e.target.value);
    },
    fullWidth: true
  }))), /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement("label", null, "Secondary Color"), /*#__PURE__*/react.createElement(ColorInput, null, /*#__PURE__*/react.createElement("div", {
    className: "color-preview"
  }, /*#__PURE__*/react.createElement("input", {
    type: "color",
    value: secondaryColor,
    onChange: function onChange(e) {
      return setSecondaryColor(e.target.value);
    }
  })), /*#__PURE__*/react.createElement(design_system.Input, {
    className: "color-input",
    value: secondaryColor,
    onChange: function onChange(e) {
      return setSecondaryColor(e.target.value);
    },
    fullWidth: true
  }))), /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement("label", null, "Background Color"), /*#__PURE__*/react.createElement(ColorInput, null, /*#__PURE__*/react.createElement("div", {
    className: "color-preview"
  }, /*#__PURE__*/react.createElement("input", {
    type: "color",
    value: backgroundColor,
    onChange: function onChange(e) {
      return setBackgroundColor(e.target.value);
    }
  })), /*#__PURE__*/react.createElement(design_system.Input, {
    className: "color-input",
    value: backgroundColor,
    onChange: function onChange(e) {
      return setBackgroundColor(e.target.value);
    },
    fullWidth: true
  }))), /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement("label", null, "Text Color"), /*#__PURE__*/react.createElement(ColorInput, null, /*#__PURE__*/react.createElement("div", {
    className: "color-preview"
  }, /*#__PURE__*/react.createElement("input", {
    type: "color",
    value: textColor,
    onChange: function onChange(e) {
      return setTextColor(e.target.value);
    }
  })), /*#__PURE__*/react.createElement(design_system.Input, {
    className: "color-input",
    value: textColor,
    onChange: function onChange(e) {
      return setTextColor(e.target.value);
    },
    fullWidth: true
  }))), /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement("label", null, "Font Family"), /*#__PURE__*/react.createElement("select", {
    value: fontFamily,
    onChange: function onChange(e) {
      return setFontFamily(e.target.value);
    },
    style: {
      width: '100%',
      padding: theme/* default.spacing */.Ay.spacing[2],
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md,
      border: "1px solid ".concat(theme/* default.colors */.Ay.colors.neutral[300])
    }
  }, fontFamilies.map(function (font) {
    return /*#__PURE__*/react.createElement("option", {
      key: font,
      value: font
    }, font);
  })), /*#__PURE__*/react.createElement(FontSelector, null, /*#__PURE__*/react.createElement("div", {
    className: "font-preview",
    style: {
      fontFamily: fontFamily
    }
  }, "The quick brown fox jumps over the lazy dog."))))), /*#__PURE__*/react.createElement(design_system.Card.Footer, null, editMode ? /*#__PURE__*/react.createElement(design_system.Button, {
    variant: "primary",
    onClick: handleUpdateTheme,
    startIcon: /*#__PURE__*/react.createElement(icons_es/* SaveOutlined */.ylI, null),
    disabled: (selectedTheme === null || selectedTheme === void 0 ? void 0 : selectedTheme.id) === 'default'
  }, "Update Theme") : /*#__PURE__*/react.createElement(design_system.Button, {
    variant: "primary",
    onClick: handleAddTheme,
    startIcon: /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null)
  }, "Add Theme"))), /*#__PURE__*/react.createElement(design_system.Card, null, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement(design_system.Card.Title, null, "Theme Preview")), /*#__PURE__*/react.createElement(design_system.Card.Content, null, /*#__PURE__*/react.createElement(ThemePreview, {
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    backgroundColor: backgroundColor,
    textColor: textColor,
    fontFamily: fontFamily
  }, /*#__PURE__*/react.createElement("h3", null, "Theme Preview"), /*#__PURE__*/react.createElement("p", null, "This is a preview of how your theme will look. The text color, background color, and font family are applied to this preview."), /*#__PURE__*/react.createElement("div", {
    className: "buttons"
  }, /*#__PURE__*/react.createElement("button", {
    className: "primary-button"
  }, "Primary Button"), /*#__PURE__*/react.createElement("button", {
    className: "secondary-button"
  }, "Secondary Button")), /*#__PURE__*/react.createElement("div", {
    className: "card-example"
  }, /*#__PURE__*/react.createElement("h4", {
    style: {
      margin: '0 0 8px 0',
      color: textColor
    }
  }, "Card Example"), /*#__PURE__*/react.createElement("p", {
    style: {
      margin: '0 0 8px 0',
      fontSize: '14px'
    }
  }, "This shows how cards will appear with your theme.")), /*#__PURE__*/react.createElement("label", {
    style: {
      display: 'block',
      marginTop: '16px',
      marginBottom: '8px'
    }
  }, "Input Example:"), /*#__PURE__*/react.createElement("input", {
    type: "text",
    className: "input-example",
    placeholder: "Enter text here..."
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: '16px',
      display: 'flex',
      justifyContent: 'space-between'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      backgroundColor: primaryColor,
      border: '1px solid rgba(0,0,0,0.1)'
    }
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      backgroundColor: secondaryColor,
      border: '1px solid rgba(0,0,0,0.1)'
    }
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      backgroundColor: backgroundColor,
      border: '1px solid rgba(0,0,0,0.1)'
    }
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      backgroundColor: textColor,
      border: '1px solid rgba(0,0,0,0.1)'
    }
  }))))), /*#__PURE__*/react.createElement(design_system.Card, null, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement(design_system.Card.Title, null, "Theme Preferences"), /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, {
    style: {
      fontSize: '18px',
      color: theme/* default.colors */.Ay.colors.neutral[500]
    }
  })), /*#__PURE__*/react.createElement(design_system.Card.Content, null, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: theme/* default.spacing */.Ay.spacing[2]
    }
  }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("h4", {
    style: {
      margin: 0,
      marginBottom: theme/* default.spacing */.Ay.spacing[1]
    }
  }, "Auto-apply Theme"), /*#__PURE__*/react.createElement("p", {
    style: {
      margin: 0,
      color: theme/* default.colors */.Ay.colors.neutral[500],
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm
    }
  }, "Automatically save your theme selection as a preference")), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: userPreferences.autoApplyTheme,
    onChange: handleToggleAutoApply,
    style: {
      backgroundColor: userPreferences.autoApplyTheme ? theme/* default.colors */.Ay.colors.primary.main : undefined
    }
  })), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: theme/* default.spacing */.Ay.spacing[3],
      padding: theme/* default.spacing */.Ay.spacing[3],
      backgroundColor: theme/* default.colors */.Ay.colors.neutral[100],
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md
    }
  }, /*#__PURE__*/react.createElement("h4", {
    style: {
      margin: 0,
      marginBottom: theme/* default.spacing */.Ay.spacing[2]
    }
  }, "Current Preferences"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      gap: theme/* default.spacing */.Ay.spacing[2],
      alignItems: 'center'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      width: '20px',
      height: '20px',
      borderRadius: '50%',
      backgroundColor: userPreferences.savedTheme ? ((_themes$find = themes.find(function (t) {
        return t.id === userPreferences.savedTheme;
      })) === null || _themes$find === void 0 ? void 0 : _themes$find.primaryColor) || '#2563EB' : '#2563EB',
      border: '1px solid #e5e7eb'
    }
  }), /*#__PURE__*/react.createElement("span", null, userPreferences.savedTheme ? ((_themes$find2 = themes.find(function (t) {
    return t.id === userPreferences.savedTheme;
  })) === null || _themes$find2 === void 0 ? void 0 : _themes$find2.name) || 'Default Theme' : 'No saved preference'))))), /*#__PURE__*/react.createElement(design_system.Card, null, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement(design_system.Card.Title, null, "Available Themes"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      gap: theme/* default.spacing */.Ay.spacing[2]
    }
  }, /*#__PURE__*/react.createElement("input", {
    type: "file",
    id: "theme-import",
    accept: ".json",
    style: {
      display: 'none'
    },
    onChange: handleImportTheme
  }), /*#__PURE__*/react.createElement(design_system.Button, {
    variant: "outline",
    size: "small",
    onClick: function onClick() {
      return document.getElementById('theme-import').click();
    }
  }, "Import Theme"))), /*#__PURE__*/react.createElement(design_system.Card.Content, null, themes.length === 0 ? /*#__PURE__*/react.createElement(EmptyState, null, /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: '48px',
      color: theme/* default.colors */.Ay.colors.neutral[400],
      marginBottom: theme/* default.spacing */.Ay.spacing[4]
    }
  }, /*#__PURE__*/react.createElement(icons_es/* BgColorsOutlined */.Ebl, null)), /*#__PURE__*/react.createElement("h3", null, "No Themes Yet"), /*#__PURE__*/react.createElement("p", null, "Create your first theme to get started")) : /*#__PURE__*/react.createElement(ThemeGrid, null, (Array.isArray(themes) ? themes : []).map(function (theme) {
    return /*#__PURE__*/react.createElement(ThemeCard, {
      key: theme.id,
      elevation: "sm",
      isActive: activeTheme === theme.id
    }, /*#__PURE__*/react.createElement(design_system.Card.Header, null, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
      style: {
        fontWeight: theme.typography.fontWeight.semibold
      }
    }, theme.name), /*#__PURE__*/react.createElement("div", {
      style: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.neutral[500]
      }
    }, theme.fontFamily.split(',')[0])), /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        gap: theme.spacing[1]
      }
    }, theme.id !== 'default' && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleExportTheme(theme);
      },
      title: "Export Theme"
    }, /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null)), /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleDuplicateTheme(theme);
      },
      title: "Duplicate Theme"
    }, /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null)), /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleSelectTheme(theme);
      },
      title: "Edit Theme"
    }, /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null)), /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleRemoveTheme(theme.id);
      },
      title: "Delete Theme"
    }, /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null))))), /*#__PURE__*/react.createElement(design_system.Card.Content, {
      onClick: function onClick() {
        return handleSelectTheme(theme);
      }
    }, /*#__PURE__*/react.createElement(ThemePreview, {
      primaryColor: theme.primaryColor,
      secondaryColor: theme.secondaryColor,
      backgroundColor: theme.backgroundColor,
      textColor: theme.textColor,
      fontFamily: theme.fontFamily,
      style: {
        height: '120px',
        overflow: 'hidden'
      }
    }, /*#__PURE__*/react.createElement("h3", {
      style: {
        fontSize: '16px'
      }
    }, "Preview"), /*#__PURE__*/react.createElement("p", {
      style: {
        fontSize: '14px'
      }
    }, "Sample text with this theme applied."), /*#__PURE__*/react.createElement("div", {
      className: "buttons"
    }, /*#__PURE__*/react.createElement("button", {
      className: "primary-button",
      style: {
        padding: '4px 8px',
        fontSize: '12px'
      }
    }, "Button"), /*#__PURE__*/react.createElement("button", {
      className: "secondary-button",
      style: {
        padding: '4px 8px',
        fontSize: '12px'
      }
    }, "Button")))), /*#__PURE__*/react.createElement(design_system.Card.Footer, null, /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        gap: theme.spacing[2]
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        width: '20px',
        height: '20px',
        backgroundColor: theme.primaryColor,
        borderRadius: '50%',
        border: '1px solid #e5e7eb'
      }
    }), /*#__PURE__*/react.createElement("div", {
      style: {
        width: '20px',
        height: '20px',
        backgroundColor: theme.secondaryColor,
        borderRadius: '50%',
        border: '1px solid #e5e7eb'
      }
    })), activeTheme === theme.id ? /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "text",
      size: "small",
      startIcon: /*#__PURE__*/react.createElement(icons_es/* CheckOutlined */.JIb, null),
      style: {
        color: theme.colors.success.main
      }
    }, "Active") : /*#__PURE__*/react.createElement(design_system.Button, {
      variant: "outline",
      size: "small",
      onClick: function onClick() {
        return handleSetActiveTheme(theme.id);
      }
    }, "Activate")));
  })))));
};
/* harmony default export */ const ThemeManager = (EnhancedThemeManager);

/***/ })

}]);