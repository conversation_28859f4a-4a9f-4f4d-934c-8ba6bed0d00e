"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6827],{

/***/ 6827:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82569);
/* harmony import */ var _ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(57683);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9;






var Header = antd__WEBPACK_IMPORTED_MODULE_3__/* .Layout */ .PE.Header;
var Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title;

// Styled components with WCAG-compliant contrast ratios
var StyledHeader = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(Header)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 24px;\n  background-color: var(--color-surface);\n  border-bottom: 1px solid var(--color-border-light);\n  box-shadow: var(--shadow-md);\n  position: sticky;\n  top: 0;\n  z-index: var(--z-sticky);\n  transition: all 0.3s ease;\n  min-height: 64px;\n\n  /* Ensure proper contrast for header background */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: var(--color-surface);\n    opacity: 0.98;\n    z-index: -1;\n  }\n\n  /* Focus management for accessibility */\n  &:focus-within {\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: 0 16px;\n    min-height: 56px;\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n  }\n"])));
var LogoSection = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  z-index: 1;\n\n  /* Ensure proper focus styles for accessibility */\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n    border-radius: 4px;\n  }\n\n  &:hover {\n    transform: scale(1.02);\n  }\n\n  &:active {\n    transform: scale(0.98);\n  }\n\n  .logo-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));\n    border-radius: 8px;\n    color: white;\n    font-size: 20px;\n    box-shadow: var(--shadow-sm);\n    transition: all 0.3s ease;\n\n    /* Ensure icon contrast meets WCAG AA standards */\n    filter: contrast(1.1);\n\n    &:hover {\n      box-shadow: var(--shadow-md);\n      transform: translateY(-1px);\n    }\n  }\n\n  .logo-text {\n    color: var(--color-text);\n    margin: 0;\n    font-weight: 600;\n    font-size: 20px;\n    line-height: 1.2;\n\n    /* Ensure text contrast meets WCAG AA standards (4.5:1) */\n    text-shadow: 0 0 1px var(--color-background);\n\n    @media (max-width: 480px) {\n      display: none;\n    }\n\n    /* High contrast mode support */\n    @media (prefers-contrast: high) {\n      font-weight: 700;\n      text-shadow: 1px 1px 2px var(--color-background);\n    }\n  }\n\n  /* Keyboard navigation support */\n  &[tabindex] {\n    border-radius: 4px;\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"])));
var HeaderActions = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    gap: 8px;\n  }\n"])));
var MobileMenuButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: none;\n\n  @media (max-width: 768px) {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    border-radius: 8px;\n    border: 1px solid var(--color-border);\n    background-color: var(--color-surface);\n    color: var(--color-text);\n    transition: all 0.3s ease;\n\n    /* Ensure button meets WCAG contrast requirements */\n    &:hover, &:focus {\n      border-color: var(--color-primary);\n      color: var(--color-primary);\n      background-color: var(--color-background-secondary);\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &:active {\n      transform: scale(0.95);\n    }\n\n    /* High contrast mode support */\n    @media (prefers-contrast: high) {\n      border-width: 2px;\n      font-weight: 600;\n    }\n\n    /* Keyboard focus indicator */\n    &:focus-visible {\n      outline: 2px solid var(--color-primary);\n      outline-offset: 2px;\n    }\n  }\n"])));
var DesktopActions = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n\n  @media (max-width: 768px) {\n    display: none;\n  }\n"])));
var StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border-radius: 20px;\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  font-size: 12px;\n  color: var(--color-text);\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  /* Ensure status text meets WCAG AA contrast ratio (4.5:1) */\n  text-shadow: 0 0 1px var(--color-background-secondary);\n\n  /* Enhanced visual hierarchy */\n  box-shadow: var(--shadow-sm);\n\n  &:hover {\n    background-color: var(--color-background-tertiary);\n    box-shadow: var(--shadow-md);\n  }\n\n  .status-dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background-color: var(--color-success);\n    box-shadow: 0 0 4px var(--color-success);\n    animation: pulse 2s infinite;\n    position: relative;\n\n    /* Add a subtle glow for better visibility */\n    &::after {\n      content: '';\n      position: absolute;\n      top: -2px;\n      left: -2px;\n      right: -2px;\n      bottom: -2px;\n      border-radius: 50%;\n      background-color: var(--color-success);\n      opacity: 0.3;\n      animation: pulse 2s infinite;\n    }\n  }\n\n  /* High contrast mode adjustments */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n    background-color: var(--color-surface);\n\n    .status-dot {\n      border: 2px solid var(--color-text);\n    }\n  }\n\n  @keyframes pulse {\n    0% {\n      opacity: 1;\n      transform: scale(1);\n    }\n    50% {\n      opacity: 0.7;\n      transform: scale(1.1);\n    }\n    100% {\n      opacity: 1;\n      transform: scale(1);\n    }\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    .status-dot {\n      animation: none;\n\n      &::after {\n        animation: none;\n      }\n    }\n  }\n"])));
var MobileDrawer = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_3__/* .Drawer */ ._s)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  .ant-drawer-content {\n    background-color: var(--color-surface);\n    border-left: 1px solid var(--color-border-light);\n  }\n\n  .ant-drawer-header {\n    background-color: var(--color-surface);\n    border-bottom: 1px solid var(--color-border-light);\n    padding: 16px 24px;\n\n    .ant-drawer-title {\n      color: var(--color-text);\n      font-weight: 600;\n      font-size: 18px;\n    }\n\n    .ant-drawer-close {\n      color: var(--color-text-secondary);\n      transition: all 0.3s ease;\n\n      &:hover {\n        color: var(--color-primary);\n        background-color: var(--color-background-secondary);\n      }\n\n      &:focus {\n        outline: 2px solid var(--color-primary);\n        outline-offset: 2px;\n      }\n    }\n  }\n\n  .ant-drawer-body {\n    padding: 24px;\n    background-color: var(--color-surface);\n  }\n\n  /* Ensure proper contrast for drawer overlay */\n  .ant-drawer-mask {\n    background-color: rgba(0, 0, 0, 0.45);\n    backdrop-filter: blur(4px);\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    .ant-drawer-content {\n      border-left-width: 3px;\n    }\n\n    .ant-drawer-header {\n      border-bottom-width: 2px;\n    }\n  }\n"])));
var DrawerContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n"])));
var DrawerSection = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  padding: 16px;\n  border-radius: 8px;\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  transition: all 0.3s ease;\n\n  &:hover {\n    background-color: var(--color-background-tertiary);\n    box-shadow: var(--shadow-sm);\n  }\n\n  .section-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: var(--color-text);\n    margin-bottom: 8px;\n\n    /* Ensure title text meets WCAG AA contrast */\n    text-shadow: 0 0 1px var(--color-background-secondary);\n  }\n\n  /* High contrast mode adjustments */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n\n    .section-title {\n      font-weight: 700;\n      text-shadow: 1px 1px 2px var(--color-background);\n    }\n  }\n"])));
var EnhancedHeader = function EnhancedHeader(_ref) {
  var _ref$title = _ref.title,
    title = _ref$title === void 0 ? "App Builder 201" : _ref$title,
    _ref$showStatus = _ref.showStatus,
    showStatus = _ref$showStatus === void 0 ? true : _ref$showStatus,
    onLogoClick = _ref.onLogoClick,
    children = _ref.children;
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    themeMode = _useEnhancedTheme.themeMode;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    mobileMenuVisible = _useState2[0],
    setMobileMenuVisible = _useState2[1];
  var handleLogoClick = function handleLogoClick(event) {
    // Handle both click and keyboard events
    if (event.type === 'keydown' && event.key !== 'Enter' && event.key !== ' ') {
      return;
    }
    event.preventDefault();
    if (onLogoClick) {
      onLogoClick();
    } else {
      // Default behavior - scroll to top or navigate to home
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };
  var toggleMobileMenu = function toggleMobileMenu() {
    setMobileMenuVisible(!mobileMenuVisible);
  };
  var closeMobileMenu = function closeMobileMenu() {
    setMobileMenuVisible(false);
  };

  // Handle escape key to close mobile menu
  var handleKeyDown = function handleKeyDown(event) {
    if (event.key === 'Escape' && mobileMenuVisible) {
      closeMobileMenu();
    }
  };

  // Add keyboard event listener
  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [mobileMenuVisible]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StyledHeader, {
    role: "banner",
    "aria-label": "Main navigation"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(LogoSection, {
    onClick: handleLogoClick,
    onKeyDown: handleLogoClick,
    tabIndex: 0,
    role: "button",
    "aria-label": "".concat(title, " - Go to homepage")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "logo-icon",
    "aria-hidden": "true"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .AppstoreOutlined */ .rS9, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
    level: 4,
    className: "logo-text",
    "aria-hidden": "true"
  }, title)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(HeaderActions, {
    role: "toolbar",
    "aria-label": "Header actions"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DesktopActions, null, showStatus && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StatusIndicator, {
    role: "status",
    "aria-label": "Application status: Online",
    title: "Application is online and ready"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "status-dot",
    "aria-hidden": "true"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", null, "Online")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    role: "group",
    "aria-label": "Theme controls"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, null)), children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    role: "group",
    "aria-label": "Additional actions"
  }, children)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MobileMenuButton, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MenuOutlined */ .imj, null),
    onClick: toggleMobileMenu,
    "aria-label": "".concat(mobileMenuVisible ? 'Close' : 'Open', " mobile menu"),
    "aria-expanded": mobileMenuVisible,
    "aria-controls": "mobile-navigation-drawer"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MobileDrawer, {
    title: "Navigation Menu",
    placement: "right",
    onClose: closeMobileMenu,
    open: mobileMenuVisible,
    width: 280,
    id: "mobile-navigation-drawer",
    "aria-label": "Mobile navigation menu",
    closeIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
      "aria-label": "Close menu"
    }, "\xD7"),
    maskClosable: true,
    keyboard: true,
    destroyOnClose: false
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DrawerContent, {
    role: "navigation",
    "aria-label": "Mobile menu content"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DrawerSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "section-title",
    id: "theme-section"
  }, "Theme Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    role: "group",
    "aria-labelledby": "theme-section"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
    showDropdown: false
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: '8px',
      fontSize: '12px',
      color: 'var(--color-text-secondary)'
    }
  }, "Current: ", themeMode === 'system' ? "System (".concat(isDarkMode ? 'Dark' : 'Light', ")") : themeMode === 'dark' ? 'Dark' : 'Light'))), showStatus && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DrawerSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "section-title",
    id: "status-section"
  }, "Application Status"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    role: "group",
    "aria-labelledby": "status-section"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StatusIndicator, {
    role: "status",
    "aria-label": "Application status: Online"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "status-dot",
    "aria-hidden": "true"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", null, "Online")))), children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DrawerSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "section-title",
    id: "actions-section"
  }, "Additional Actions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    role: "group",
    "aria-labelledby": "actions-section"
  }, children)))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedHeader);

/***/ }),

/***/ 57683:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(16918);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82569);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8;

// Optimized Ant Design imports for better tree-shaking





// Animations
var rotate = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"])));
var fadeIn = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"])));

// Styled components
var ToggleContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var ToggleButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 1px solid var(--color-border);\n  background-color: var(--color-surface);\n  color: var(--color-text);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure WCAG AA contrast compliance */\n  &:hover, &:focus {\n    border-color: var(--color-primary);\n    color: var(--color-primary);\n    background-color: var(--color-background-secondary);\n    transform: scale(1.05);\n    box-shadow: var(--shadow-md);\n  }\n\n  &:focus-visible {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n\n  .anticon {\n    font-size: 18px;\n    animation: ", " 0.3s ease;\n\n    /* Ensure icon has sufficient contrast */\n    filter: contrast(1.1);\n  }\n\n  &.rotating .anticon {\n    animation: ", " 0.5s ease;\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n\n    &:hover, &:focus {\n      border-width: 3px;\n    }\n\n    .anticon {\n      filter: contrast(1.3);\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n\n    &:active {\n      transform: none;\n    }\n\n    .anticon {\n      animation: none;\n    }\n\n    &.rotating .anticon {\n      animation: none;\n    }\n  }\n"])), fadeIn, rotate);
var DropdownContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  min-width: 180px;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: 8px;\n  box-shadow: var(--shadow-lg);\n  padding: 4px;\n"])));
var ThemeOption = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-radius: 6px;\n  margin: 2px 0;\n  color: var(--color-text);\n\n  &:hover {\n    background-color: var(--color-background-secondary);\n    transform: translateX(2px);\n  }\n\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 1px;\n  }\n\n  &.active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-sm);\n  }\n\n  .option-content {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n  }\n\n  .anticon {\n    font-size: 16px;\n\n    /* Ensure icon contrast in active state */\n    filter: ", ";\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 1px solid var(--color-border);\n\n    &:hover {\n      border-color: var(--color-primary);\n    }\n\n    &.active {\n      border: 2px solid white;\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: background-color 0.2s ease;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"])), function (props) {
  var _props$className;
  return (_props$className = props.className) !== null && _props$className !== void 0 && _props$className.includes('active') ? 'none' : 'contrast(1.1)';
});
var ThemeLabel = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.span(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n"])));
var ThemeDescription = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: ", ";\n  margin-top: 2px;\n  line-height: 1.2;\n\n  /* Ensure description text meets contrast requirements */\n  opacity: ", ";\n"])), function (props) {
  return props.active ? 'rgba(255, 255, 255, 0.8)' : 'var(--color-text-secondary)';
}, function (props) {
  return props.active ? 0.9 : 0.8;
});
var DarkModeToggle = function DarkModeToggle(_ref) {
  var _ref$showDropdown = _ref.showDropdown,
    showDropdown = _ref$showDropdown === void 0 ? true : _ref$showDropdown,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'default' : _ref$size;
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    themeMode = _useEnhancedTheme.themeMode,
    toggleDarkMode = _useEnhancedTheme.toggleDarkMode,
    setThemeMode = _useEnhancedTheme.setThemeMode,
    systemPrefersDark = _useEnhancedTheme.systemPrefersDark;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    isRotating = _useState2[0],
    setIsRotating = _useState2[1];
  var handleToggle = function handleToggle() {
    setIsRotating(true);
    toggleDarkMode();
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var handleThemeChange = function handleThemeChange(mode) {
    setIsRotating(true);
    setThemeMode(mode);
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var getIcon = function getIcon() {
    if (themeMode === 'system') {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null);
    }
    return isDarkMode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null);
  };
  var getTooltipTitle = function getTooltipTitle() {
    switch (themeMode) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return "System mode (".concat(systemPrefersDark ? 'dark' : 'light', ")");
      default:
        return 'Toggle theme';
    }
  };
  var themeOptions = [{
    key: 'light',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null),
    label: 'Light',
    description: 'Light theme'
  }, {
    key: 'dark',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null),
    label: 'Dark',
    description: 'Dark theme'
  }, {
    key: 'system',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null),
    label: 'System',
    description: 'Follow system preference'
  }];
  var dropdownMenu = {
    items: themeOptions.map(function (option) {
      return {
        key: option.key,
        label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeOption, {
          className: themeMode === option.key ? 'active' : '',
          onClick: function onClick() {
            return handleThemeChange(option.key);
          },
          role: "menuitem",
          tabIndex: 0,
          "aria-selected": themeMode === option.key,
          onKeyDown: function onKeyDown(e) {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleThemeChange(option.key);
            }
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
          className: "option-content"
        }, option.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeLabel, null, option.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeDescription, {
          active: themeMode === option.key
        }, option.description))), themeMode === option.key && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckOutlined */ .JIb, {
          "aria-label": "Selected"
        }))
      };
    })
  };
  if (!showDropdown) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: getTooltipTitle(),
      placement: "bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
      type: "text",
      size: size,
      className: isRotating ? 'rotating' : '',
      onClick: handleToggle,
      "aria-label": "Switch to ".concat(isDarkMode ? 'light' : 'dark', " mode. Current theme: ").concat(getTooltipTitle()),
      "aria-pressed": isDarkMode,
      role: "switch"
    }, getIcon())));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Dropdown */ .ms, {
    menu: dropdownMenu,
    trigger: ['click'],
    placement: "bottomRight",
    arrow: true,
    dropdownRender: function dropdownRender(menu) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DropdownContent, {
        role: "menu",
        "aria-label": "Theme selection menu"
      }, menu);
    },
    onOpenChange: function onOpenChange(open) {
      // Announce to screen readers when menu opens/closes
      if (open) {
        // Focus management for accessibility
        setTimeout(function () {
          var firstMenuItem = document.querySelector('[role="menuitem"]');
          if (firstMenuItem) {
            firstMenuItem.focus();
          }
        }, 100);
      }
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: getTooltipTitle(),
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
    type: "text",
    size: size,
    className: isRotating ? 'rotating' : '',
    "aria-label": "Theme options menu. Current theme: ".concat(getTooltipTitle()),
    "aria-haspopup": "menu",
    "aria-expanded": "false",
    role: "button"
  }, getIcon()))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DarkModeToggle);

/***/ })

}]);