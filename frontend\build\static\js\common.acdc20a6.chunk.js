"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[2076],{

/***/ 16918:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $n: () => (/* reexport safe */ antd_es_button__WEBPACK_IMPORTED_MODULE_0__.Ay),
/* harmony export */   $x: () => (/* reexport safe */ antd_es_space__WEBPACK_IMPORTED_MODULE_6__.A),
/* harmony export */   B8: () => (/* reexport safe */ antd_es_list__WEBPACK_IMPORTED_MODULE_20__.A),
/* harmony export */   Fc: () => (/* reexport safe */ antd_es_alert__WEBPACK_IMPORTED_MODULE_8__.A),
/* harmony export */   Zp: () => (/* reexport safe */ antd_es_card__WEBPACK_IMPORTED_MODULE_1__.A),
/* harmony export */   cG: () => (/* reexport safe */ antd_es_divider__WEBPACK_IMPORTED_MODULE_7__.A),
/* harmony export */   fI: () => (/* reexport safe */ antd_es_grid__WEBPACK_IMPORTED_MODULE_5__.fI),
/* harmony export */   fv: () => (/* reexport safe */ antd_es_grid__WEBPACK_IMPORTED_MODULE_5__.fv),
/* harmony export */   jL: () => (/* reexport safe */ antd_es_statistic__WEBPACK_IMPORTED_MODULE_26__.A),
/* harmony export */   m_: () => (/* reexport safe */ antd_es_tooltip__WEBPACK_IMPORTED_MODULE_12__.A),
/* harmony export */   ms: () => (/* reexport safe */ antd_es_dropdown__WEBPACK_IMPORTED_MODULE_17__.A),
/* harmony export */   o5: () => (/* reexport safe */ antd_es_typography__WEBPACK_IMPORTED_MODULE_2__.A),
/* harmony export */   pd: () => (/* reexport safe */ antd_es_input__WEBPACK_IMPORTED_MODULE_34__.A),
/* harmony export */   tK: () => (/* reexport safe */ antd_es_spin__WEBPACK_IMPORTED_MODULE_9__.A),
/* harmony export */   vw: () => (/* reexport safe */ antd_es_tag__WEBPACK_IMPORTED_MODULE_15__.A)
/* harmony export */ });
/* unused harmony exports importComponentCSS, CommonComponents, PageImports, OptimizationTips */
/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(49103);
/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(677);
/* harmony import */ var antd_es_typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(75475);
/* harmony import */ var antd_es_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(45448);
/* harmony import */ var antd_es_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(87206);
/* harmony import */ var antd_es_grid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(36768);
/* harmony import */ var antd_es_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(28392);
/* harmony import */ var antd_es_divider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(36552);
/* harmony import */ var antd_es_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(27197);
/* harmony import */ var antd_es_spin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(29029);
/* harmony import */ var antd_es_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(49222);
/* harmony import */ var antd_es_drawer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(1849);
/* harmony import */ var antd_es_tooltip__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(37977);
/* harmony import */ var antd_es_popover__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(28073);
/* harmony import */ var antd_es_badge__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(52120);
/* harmony import */ var antd_es_tag__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(67034);
/* harmony import */ var antd_es_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(81427);
/* harmony import */ var antd_es_dropdown__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(88603);
/* harmony import */ var antd_es_tabs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(12075);
/* harmony import */ var antd_es_collapse__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(39356);
/* harmony import */ var antd_es_list__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(42652);
/* harmony import */ var antd_es_table__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(42729);
/* harmony import */ var antd_es_pagination__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(44485);
/* harmony import */ var antd_es_breadcrumb__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(94431);
/* harmony import */ var antd_es_steps__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(53515);
/* harmony import */ var antd_es_progress__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(6754);
/* harmony import */ var antd_es_statistic__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(37122);
/* harmony import */ var antd_es_empty__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(17308);
/* harmony import */ var antd_es_result__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(14378);
/* harmony import */ var antd_es_skeleton__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(97072);
/* harmony import */ var antd_es_back_top__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(53308);
/* harmony import */ var antd_es_affix__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(18719);
/* harmony import */ var antd_es_anchor__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(8787);
/* harmony import */ var antd_es_form__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(28792);
/* harmony import */ var antd_es_input__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(17355);
/* harmony import */ var antd_es_select__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(36492);
/* harmony import */ var antd_es_checkbox__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(91196);
/* harmony import */ var antd_es_radio__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(50770);
/* harmony import */ var antd_es_switch__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(15039);
/* harmony import */ var antd_es_slider__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(6531);
/* harmony import */ var antd_es_rate__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(1285);
/* harmony import */ var antd_es_upload__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(77829);
/* harmony import */ var antd_es_date_picker__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(95082);
/* harmony import */ var antd_es_time_picker__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(15622);
/* harmony import */ var antd_es_transfer__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(80296);
/* harmony import */ var antd_es_cascader__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(93438);
/* harmony import */ var antd_es_tree_select__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(21102);
/* harmony import */ var antd_es_auto_complete__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(1062);
/* harmony import */ var antd_es_tree__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(87937);
/* harmony import */ var antd_es_calendar__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(40248);
/* harmony import */ var antd_es_image__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(62070);
/* harmony import */ var antd_es_carousel__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(88845);
/* harmony import */ var antd_es_descriptions__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(55957);
/* harmony import */ var antd_es_timeline__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(33835);
/* harmony import */ var antd_es_message__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(87959);
/* harmony import */ var antd_es_notification__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(76511);
/* harmony import */ var antd_es_popconfirm__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(16044);
/* harmony import */ var antd_es_float_button__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(75245);
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(38674);
/* harmony import */ var antd_dist_reset_css__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(58545);
/**
 * Optimized Ant Design Imports Utility
 * 
 * This utility provides optimized imports for Ant Design components
 * to enable better tree-shaking and reduce bundle sizes.
 */

// Core Components - Import from specific paths for better tree-shaking


































// Form Components
















// Data Display







// Feedback




// Navigation


// Other


// Import CSS for commonly used components
// Note: Using the reset CSS file for basic styling
// Component-specific styles are handled by the components themselves


/**
 * Utility function for component CSS (deprecated)
 * CSS is now imported globally via antd.css
 */
var importComponentCSS = function importComponentCSS(componentName) {
  // CSS is now imported globally, this function is kept for compatibility
  console.log("CSS for ".concat(componentName, " is already included globally"));
};

/**
 * Common component combinations for convenience
 */
var CommonComponents = {
  // Layout components
  Layout: {
    Layout: Layout,
    Header: Layout.Header,
    Content: Layout.Content,
    Footer: Layout.Footer,
    Sider: Layout.Sider
  },
  // Typography components
  Typography: {
    Typography: Typography,
    Title: Typography.Title,
    Text: Typography.Text,
    Paragraph: Typography.Paragraph,
    Link: Typography.Link
  },
  // Grid components
  Grid: {
    Row: Row,
    Col: Col
  },
  // Form components
  FormComponents: {
    Form: Form,
    FormItem: Form.Item,
    FormList: Form.List,
    Input: Input,
    TextArea: Input.TextArea,
    Password: Input.Password,
    Search: Input.Search,
    Select: Select,
    Option: Select.Option,
    OptGroup: Select.OptGroup
  }
};

/**
 * Optimized imports for specific page types
 */
var PageImports = {
  // For dashboard/home pages
  dashboard: function dashboard() {
    return {
      Card: Card,
      Button: Button,
      Typography: Typography,
      Row: Row,
      Col: Col,
      Statistic: Statistic,
      List: List,
      Badge: Badge,
      Space: Space
    };
  },
  // For form pages
  form: function form() {
    return {
      Form: Form,
      Input: Input,
      Select: Select,
      Checkbox: Checkbox,
      Radio: Radio,
      Button: Button,
      Card: Card,
      Space: Space
    };
  },
  // For data display pages
  dataDisplay: function dataDisplay() {
    return {
      Table: Table,
      Pagination: Pagination,
      Empty: Empty,
      Card: Card,
      Button: Button,
      Space: Space
    };
  },
  // For layout pages
  layout: function layout() {
    return {
      Layout: Layout,
      Menu: Menu,
      Drawer: Drawer,
      Dropdown: Dropdown,
      Button: Button,
      Avatar: Avatar
    };
  }
};

/**
 * Bundle size optimization tips
 */
var OptimizationTips = {
  // Use specific imports instead of full library
  GOOD: "import { Button } from 'antd/es/button';",
  BAD: "import { Button } from 'antd';",
  // Import CSS only for used components
  CSS_GOOD: "import 'antd/es/button/style/css';",
  CSS_BAD: "import 'antd/dist/antd.css';",
  // Use this utility for consistent imports
  UTILITY: "import { Button, Card } from '../utils/optimizedAntdImports';"
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  CommonComponents: CommonComponents,
  PageImports: PageImports,
  OptimizationTips: OptimizationTips,
  importComponentCSS: importComponentCSS
});

/***/ }),

/***/ 17050:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(70405);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/**
 * Custom hook for managing responsive preview functionality
 * Handles device switching, breakpoints, and responsive behavior
 */
var useResponsivePreview = function useResponsivePreview(_ref) {
  var _DEVICE_PRESETS$initi;
  var _ref$initialDevice = _ref.initialDevice,
    initialDevice = _ref$initialDevice === void 0 ? 'desktop' : _ref$initialDevice,
    _ref$initialVariant = _ref.initialVariant,
    initialVariant = _ref$initialVariant === void 0 ? null : _ref$initialVariant,
    _ref$enableBreakpoint = _ref.enableBreakpointDetection,
    enableBreakpointDetection = _ref$enableBreakpoint === void 0 ? true : _ref$enableBreakpoint,
    _ref$customBreakpoint = _ref.customBreakpoints,
    customBreakpoints = _ref$customBreakpoint === void 0 ? null : _ref$customBreakpoint;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialDevice),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    currentDevice = _useState2[0],
    setCurrentDevice = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialVariant || ((_DEVICE_PRESETS$initi = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[initialDevice]) === null || _DEVICE_PRESETS$initi === void 0 ? void 0 : _DEVICE_PRESETS$initi.defaultVariant)),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    currentVariant = _useState4[0],
    setCurrentVariant = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('portrait'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    orientation = _useState6[0],
    setOrientation = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    isFullscreen = _useState8[0],
    setIsFullscreen = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      width: 0,
      height: 0
    }),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    viewportSize = _useState0[0],
    setViewportSize = _useState0[1];

  // Default breakpoints (can be overridden)
  var defaultBreakpoints = {
    mobile: {
      min: 0,
      max: 767
    },
    tablet: {
      min: 768,
      max: 1023
    },
    desktop: {
      min: 1024,
      max: Infinity
    }
  };
  var breakpoints = customBreakpoints || defaultBreakpoints;

  // Get current device configuration
  var deviceConfig = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    var device = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[currentDevice];
    if (!device) return null;
    var variant = device.variants[currentVariant];
    if (!variant) return null;
    return _objectSpread(_objectSpread(_objectSpread({}, device), variant), {}, {
      category: device.category,
      orientation: orientation
    });
  }, [currentDevice, currentVariant, orientation]);

  // Get responsive styles based on current device
  var responsiveStyles = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    if (!deviceConfig) return {};
    var baseStyles = {
      fontSize: deviceConfig.category === 'mobile' ? '14px' : deviceConfig.category === 'tablet' ? '15px' : '16px',
      padding: deviceConfig.category === 'mobile' ? '8px' : deviceConfig.category === 'tablet' ? '12px' : '16px',
      margin: deviceConfig.category === 'mobile' ? '4px' : '8px',
      borderRadius: deviceConfig.category === 'mobile' ? '4px' : '6px'
    };

    // Add device-specific styles
    var deviceSpecificStyles = {
      mobile: {
        maxWidth: '100%',
        touchAction: 'manipulation',
        WebkitTapHighlightColor: 'transparent'
      },
      tablet: {
        maxWidth: '100%',
        touchAction: 'manipulation'
      },
      desktop: {
        cursor: 'pointer',
        userSelect: 'none'
      }
    };
    return _objectSpread(_objectSpread({}, baseStyles), deviceSpecificStyles[deviceConfig.category]);
  }, [deviceConfig]);

  // Get component size based on device
  var getComponentSize = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentType) {
    var _sizeMap$deviceConfig;
    if (!deviceConfig) return 'middle';
    var sizeMap = {
      mobile: {
        button: 'small',
        input: 'small',
        card: 'small',
        table: 'small',
        form: 'small'
      },
      tablet: {
        button: 'middle',
        input: 'middle',
        card: 'default',
        table: 'middle',
        form: 'middle'
      },
      desktop: {
        button: 'middle',
        input: 'middle',
        card: 'default',
        table: 'middle',
        form: 'middle'
      }
    };
    return ((_sizeMap$deviceConfig = sizeMap[deviceConfig.category]) === null || _sizeMap$deviceConfig === void 0 ? void 0 : _sizeMap$deviceConfig[componentType]) || 'middle';
  }, [deviceConfig]);

  // Detect viewport size changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enableBreakpointDetection) return;
    var updateViewportSize = function updateViewportSize() {
      setViewportSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };
    updateViewportSize();
    window.addEventListener('resize', updateViewportSize);
    return function () {
      window.removeEventListener('resize', updateViewportSize);
    };
  }, [enableBreakpointDetection]);

  // Auto-detect device based on viewport size
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var _Object$entries$find;
    if (!enableBreakpointDetection || viewportSize.width === 0) return;
    var detectedDevice = (_Object$entries$find = Object.entries(breakpoints).find(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref2, 2),
        _ = _ref3[0],
        range = _ref3[1];
      return viewportSize.width >= range.min && viewportSize.width <= range.max;
    })) === null || _Object$entries$find === void 0 ? void 0 : _Object$entries$find[0];
    if (detectedDevice && detectedDevice !== currentDevice) {
      var _DEVICE_PRESETS$detec;
      setCurrentDevice(detectedDevice);
      setCurrentVariant((_DEVICE_PRESETS$detec = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[detectedDevice]) === null || _DEVICE_PRESETS$detec === void 0 ? void 0 : _DEVICE_PRESETS$detec.defaultVariant);
    }
  }, [viewportSize, breakpoints, enableBreakpointDetection, currentDevice]);

  // Handle device change
  var handleDeviceChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newDevice) {
    var _DEVICE_PRESETS$newDe;
    var newVariant = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    setCurrentDevice(newDevice);
    var variant = newVariant || ((_DEVICE_PRESETS$newDe = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[newDevice]) === null || _DEVICE_PRESETS$newDe === void 0 ? void 0 : _DEVICE_PRESETS$newDe.defaultVariant);
    setCurrentVariant(variant);

    // Reset orientation for desktop
    if (newDevice === 'desktop') {
      setOrientation('portrait');
    }
  }, []);

  // Handle variant change
  var handleVariantChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newVariant) {
    setCurrentVariant(newVariant);
  }, []);

  // Handle orientation change
  var handleOrientationChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if ((deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop') return;
    setOrientation(function (prev) {
      return prev === 'portrait' ? 'landscape' : 'portrait';
    });
  }, [deviceConfig]);

  // Handle fullscreen toggle
  var handleFullscreenToggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setIsFullscreen(function (prev) {
      return !prev;
    });
  }, []);

  // Get media queries for CSS
  var getMediaQueries = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return {
      mobile: "@media (max-width: ".concat(breakpoints.mobile.max, "px)"),
      tablet: "@media (min-width: ".concat(breakpoints.tablet.min, "px) and (max-width: ").concat(breakpoints.tablet.max, "px)"),
      desktop: "@media (min-width: ".concat(breakpoints.desktop.min, "px)"),
      isMobile: "(max-width: ".concat(breakpoints.mobile.max, "px)"),
      isTablet: "(min-width: ".concat(breakpoints.tablet.min, "px) and (max-width: ").concat(breakpoints.tablet.max, "px)"),
      isDesktop: "(min-width: ".concat(breakpoints.desktop.min, "px)")
    };
  }, [breakpoints]);

  // Check if current device matches a category
  var isDevice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (deviceCategory) {
    return (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === deviceCategory;
  }, [deviceConfig]);

  // Get responsive props for Ant Design components
  var getResponsiveProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentType) {
    var size = getComponentSize(componentType);
    var props = {
      size: size
    };

    // Add device-specific props
    if ((deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'mobile') {
      if (componentType === 'table') {
        props.scroll = {
          x: true
        };
        props.pagination = {
          simple: true,
          size: 'small'
        };
      }
      if (componentType === 'form') {
        props.layout = 'vertical';
      }
    }
    return props;
  }, [deviceConfig, getComponentSize]);

  // Get current dimensions (considering orientation)
  var getCurrentDimensions = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!deviceConfig) return {
      width: 0,
      height: 0
    };
    var width = deviceConfig.width,
      height = deviceConfig.height;
    if (orientation === 'landscape' && deviceConfig.category !== 'desktop') {
      return {
        width: height,
        height: width
      };
    }
    return {
      width: width,
      height: height
    };
  }, [deviceConfig, orientation]);

  // Preview state utilities
  var previewUtils = {
    // Check if component should be hidden on current device
    shouldHideComponent: function shouldHideComponent(componentConfig) {
      var _componentConfig$resp;
      var hideOn = (componentConfig === null || componentConfig === void 0 || (_componentConfig$resp = componentConfig.responsive) === null || _componentConfig$resp === void 0 ? void 0 : _componentConfig$resp.hideOn) || [];
      return hideOn.includes(deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category);
    },
    // Get component-specific responsive styles
    getComponentStyles: function getComponentStyles(componentConfig) {
      var responsive = (componentConfig === null || componentConfig === void 0 ? void 0 : componentConfig.responsive) || {};
      var deviceStyles = responsive[deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category] || {};
      return _objectSpread(_objectSpread({}, responsiveStyles), deviceStyles);
    },
    // Check if feature is supported on current device
    isFeatureSupported: function isFeatureSupported(feature) {
      var supportMap = {
        hover: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop',
        touch: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) !== 'desktop',
        keyboard: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop',
        contextMenu: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop'
      };
      return supportMap[feature] !== false;
    }
  };
  return _objectSpread(_objectSpread({
    // Current state
    currentDevice: currentDevice,
    currentVariant: currentVariant,
    orientation: orientation,
    isFullscreen: isFullscreen,
    deviceConfig: deviceConfig,
    viewportSize: viewportSize,
    // Computed values
    responsiveStyles: responsiveStyles,
    mediaQueries: getMediaQueries(),
    dimensions: getCurrentDimensions(),
    // Actions
    handleDeviceChange: handleDeviceChange,
    handleVariantChange: handleVariantChange,
    handleOrientationChange: handleOrientationChange,
    handleFullscreenToggle: handleFullscreenToggle,
    // Utilities
    isDevice: isDevice,
    getComponentSize: getComponentSize,
    getResponsiveProps: getResponsiveProps
  }, previewUtils), {}, {
    // Available devices and variants
    availableDevices: Object.keys(_components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c),
    availableVariants: deviceConfig ? Object.keys(deviceConfig.variants || {}) : [],
    // Breakpoints
    breakpoints: breakpoints
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useResponsivePreview);

/***/ }),

/***/ 25577:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71468);
/* harmony import */ var _services_PreviewWebSocketService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(75121);




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * Custom hook for collaborative preview functionality
 * Handles real-time synchronization, cursor tracking, and collaborative editing
 */
var useCollaborativePreview = function useCollaborativePreview(_ref) {
  var sessionId = _ref.sessionId,
    userId = _ref.userId,
    _ref$username = _ref.username,
    username = _ref$username === void 0 ? 'Anonymous' : _ref$username,
    _ref$avatar = _ref.avatar,
    avatar = _ref$avatar === void 0 ? null : _ref$avatar,
    _ref$enableCollaborat = _ref.enableCollaboration,
    enableCollaboration = _ref$enableCollaborat === void 0 ? true : _ref$enableCollaborat,
    _ref$enableCursorTrac = _ref.enableCursorTracking,
    enableCursorTracking = _ref$enableCursorTrac === void 0 ? true : _ref$enableCursorTrac,
    _ref$enableDeviceSync = _ref.enableDeviceSync,
    enableDeviceSync = _ref$enableDeviceSync === void 0 ? true : _ref$enableDeviceSync;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    collaborators = _useState2[0],
    setCollaborators = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    cursors = _useState4[0],
    setCursors = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    isConnected = _useState6[0],
    setIsConnected = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('disconnected'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    connectionStatus = _useState8[0],
    setConnectionStatus = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    syncedComponents = _useState0[0],
    setSyncedComponents = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({}),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    deviceState = _useState10[0],
    setDeviceState = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    conflictResolution = _useState12[0],
    setConflictResolution = _useState12[1];

  // Refs
  var wsServiceRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var cursorTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(new Map());
  var lastSyncTime = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(new Date());

  // Redux state
  var websocketConfig = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return ((_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.config) || {};
  });

  // Initialize WebSocket service
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (!enableCollaboration || !sessionId) return;
    var wsService = new _services_PreviewWebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A({
      url: websocketConfig.url || 'ws://localhost:8000/ws/collaboration/',
      autoConnect: true,
      reconnectOptions: {
        maxAttempts: 10,
        initialDelay: 1000,
        maxDelay: 30000
      }
    });
    wsServiceRef.current = wsService;

    // Connection event handlers
    wsService.on('connect', function () {
      setIsConnected(true);
      setConnectionStatus('connected');

      // Join the collaborative session
      wsService.joinSession(sessionId, {
        username: username,
        avatar: avatar
      });
    });
    wsService.on('disconnect', function () {
      setIsConnected(false);
      setConnectionStatus('disconnected');
    });
    wsService.on('error', function (error) {
      setConnectionStatus('error');
      console.error('Collaborative WebSocket error:', error);
    });

    // Preview event handlers
    setupPreviewEventHandlers(wsService);
    return function () {
      if (wsService) {
        wsService.leaveSession(sessionId);
        wsService.disconnect();
      }

      // Clear cursor timeouts
      cursorTimeoutRef.current.forEach(function (timeout) {
        return clearTimeout(timeout);
      });
      cursorTimeoutRef.current.clear();
    };
  }, [sessionId, enableCollaboration, username, avatar, websocketConfig.url]);

  // Setup preview event handlers
  var setupPreviewEventHandlers = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (wsService) {
    // Collaborator events
    wsService.onPreviewEvent('collaborator_joined', function (data) {
      var user_id = data.user_id,
        collaboratorName = data.username,
        collaboratorAvatar = data.avatar;
      setCollaborators(function (prev) {
        return new Map(prev.set(user_id, {
          id: user_id,
          username: collaboratorName,
          avatar: collaboratorAvatar,
          joinedAt: new Date(),
          isActive: true
        }));
      });
    });
    wsService.onPreviewEvent('collaborator_left', function (data) {
      var user_id = data.user_id;
      setCollaborators(function (prev) {
        var newMap = new Map(prev);
        newMap["delete"](user_id);
        return newMap;
      });
      setCursors(function (prev) {
        var newMap = new Map(prev);
        newMap["delete"](user_id);
        return newMap;
      });
    });

    // Cursor tracking events
    if (enableCursorTracking) {
      wsService.onPreviewEvent('cursor_moved', function (data) {
        var user_id = data.user_id,
          position = data.position;
        if (user_id !== userId) {
          setCursors(function (prev) {
            return new Map(prev.set(user_id, {
              position: position,
              timestamp: new Date(),
              userId: user_id
            }));
          });

          // Auto-hide cursor after inactivity
          var existingTimeout = cursorTimeoutRef.current.get(user_id);
          if (existingTimeout) {
            clearTimeout(existingTimeout);
          }
          var timeout = setTimeout(function () {
            setCursors(function (prev) {
              var newMap = new Map(prev);
              newMap["delete"](user_id);
              return newMap;
            });
            cursorTimeoutRef.current["delete"](user_id);
          }, 5000);
          cursorTimeoutRef.current.set(user_id, timeout);
        }
      });
    }

    // Component synchronization events
    wsService.onPreviewEvent('component_updated', function (data) {
      var component_id = data.component_id,
        component_data = data.component_data,
        updatedBy = data.user_id;
      if (updatedBy !== userId) {
        setSyncedComponents(function (prev) {
          return new Map(prev.set(component_id, _objectSpread(_objectSpread({}, component_data), {}, {
            lastUpdatedBy: updatedBy,
            lastUpdated: new Date(),
            synced: true
          })));
        });
      }
    });
    wsService.onPreviewEvent('component_added', function (data) {
      var component = data.component,
        addedBy = data.user_id;
      if (addedBy !== userId && component !== null && component !== void 0 && component.id) {
        setSyncedComponents(function (prev) {
          return new Map(prev.set(component.id, _objectSpread(_objectSpread({}, component), {}, {
            addedBy: addedBy,
            synced: true
          })));
        });
      }
    });
    wsService.onPreviewEvent('component_deleted', function (data) {
      var component_id = data.component_id,
        deletedBy = data.user_id;
      if (deletedBy !== userId) {
        setSyncedComponents(function (prev) {
          var newMap = new Map(prev);
          newMap["delete"](component_id);
          return newMap;
        });
      }
    });

    // Device synchronization events
    if (enableDeviceSync) {
      wsService.onPreviewEvent('device_changed', function (data) {
        var device_type = data.device_type,
          device_config = data.device_config,
          changedBy = data.user_id;
        if (changedBy !== userId) {
          setDeviceState({
            type: device_type,
            config: device_config,
            changedBy: changedBy,
            timestamp: new Date()
          });
        }
      });
    }

    // Preview state synchronization
    wsService.onPreviewEvent('preview_state_synced', function (data) {
      var state = data.state;
      if (state.components) {
        var componentsMap = new Map(state.components);
        setSyncedComponents(componentsMap);
      }
      if (state.deviceSettings && enableDeviceSync) {
        setDeviceState(state.deviceSettings);
      }
      lastSyncTime.current = new Date();
    });
  }, [userId, enableCursorTracking, enableDeviceSync]);

  // Send component update
  var sendComponentUpdate = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(componentId, componentData) {
      var immediate,
        _args = arguments;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            immediate = _args.length > 2 && _args[2] !== undefined ? _args[2] : false;
            if (!(!wsServiceRef.current || !isConnected)) {
              _context.next = 3;
              break;
            }
            return _context.abrupt("return", false);
          case 3:
            _context.prev = 3;
            _context.next = 6;
            return wsServiceRef.current.sendComponentUpdate(componentId, componentData, {
              userId: userId,
              sessionId: sessionId,
              immediate: immediate
            });
          case 6:
            return _context.abrupt("return", true);
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](3);
            console.error('Failed to send component update:', _context.t0);
            return _context.abrupt("return", false);
          case 13:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[3, 9]]);
    }));
    return function (_x, _x2) {
      return _ref2.apply(this, arguments);
    };
  }(), [isConnected, userId, sessionId]);

  // Send cursor position
  var sendCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(position) {
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (!(!wsServiceRef.current || !isConnected || !enableCursorTracking)) {
              _context2.next = 2;
              break;
            }
            return _context2.abrupt("return");
          case 2:
            _context2.prev = 2;
            _context2.next = 5;
            return wsServiceRef.current.sendCursorPosition(position, {
              userId: userId,
              sessionId: sessionId
            });
          case 5:
            _context2.next = 10;
            break;
          case 7:
            _context2.prev = 7;
            _context2.t0 = _context2["catch"](2);
            console.error('Failed to send cursor position:', _context2.t0);
          case 10:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[2, 7]]);
    }));
    return function (_x3) {
      return _ref3.apply(this, arguments);
    };
  }(), [isConnected, userId, sessionId, enableCursorTracking]);

  // Send device change
  var sendDeviceChange = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3(deviceType, deviceConfig) {
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            if (!(!wsServiceRef.current || !isConnected || !enableDeviceSync)) {
              _context3.next = 2;
              break;
            }
            return _context3.abrupt("return", false);
          case 2:
            _context3.prev = 2;
            _context3.next = 5;
            return wsServiceRef.current.sendDeviceChange(deviceType, deviceConfig, {
              userId: userId,
              sessionId: sessionId
            });
          case 5:
            return _context3.abrupt("return", true);
          case 8:
            _context3.prev = 8;
            _context3.t0 = _context3["catch"](2);
            console.error('Failed to send device change:', _context3.t0);
            return _context3.abrupt("return", false);
          case 12:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[2, 8]]);
    }));
    return function (_x4, _x5) {
      return _ref4.apply(this, arguments);
    };
  }(), [isConnected, userId, sessionId, enableDeviceSync]);

  // Request preview state sync
  var requestSync = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee4() {
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          if (!(!wsServiceRef.current || !isConnected)) {
            _context4.next = 2;
            break;
          }
          return _context4.abrupt("return", false);
        case 2:
          _context4.prev = 2;
          _context4.next = 5;
          return wsServiceRef.current.requestPreviewState(sessionId);
        case 5:
          return _context4.abrupt("return", true);
        case 8:
          _context4.prev = 8;
          _context4.t0 = _context4["catch"](2);
          console.error('Failed to request preview state:', _context4.t0);
          return _context4.abrupt("return", false);
        case 12:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[2, 8]]);
  })), [isConnected, sessionId]);

  // Resolve component conflicts
  var resolveConflict = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (componentId, resolution) {
    setConflictResolution(function (prev) {
      return new Map(prev.set(componentId, {
        resolution: resolution,
        timestamp: new Date(),
        resolvedBy: userId
      }));
    });
  }, [userId]);

  // Get component with conflict resolution
  var getResolvedComponent = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (componentId, localComponent) {
    var syncedComponent = syncedComponents.get(componentId);
    var conflict = conflictResolution.get(componentId);
    if (!syncedComponent) return localComponent;
    if (!conflict) {
      // Check for conflicts based on timestamps
      var localTime = new Date((localComponent === null || localComponent === void 0 ? void 0 : localComponent.lastUpdated) || 0);
      var syncedTime = new Date((syncedComponent === null || syncedComponent === void 0 ? void 0 : syncedComponent.lastUpdated) || 0);
      if (Math.abs(localTime - syncedTime) < 1000) {
        // Recent conflict, use last-writer-wins
        return syncedTime > localTime ? syncedComponent : localComponent;
      }
    }

    // Apply conflict resolution
    switch (conflict === null || conflict === void 0 ? void 0 : conflict.resolution) {
      case 'use_local':
        return localComponent;
      case 'use_remote':
        return syncedComponent;
      case 'merge':
        return _objectSpread(_objectSpread({}, localComponent), syncedComponent);
      default:
        return syncedComponent;
      // Default to remote
    }
  }, [syncedComponents, conflictResolution]);

  // Get collaboration status
  var getCollaborationStatus = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    return {
      isConnected: isConnected,
      connectionStatus: connectionStatus,
      collaboratorCount: collaborators.size,
      hasActiveCollaborators: collaborators.size > 0,
      lastSyncTime: lastSyncTime.current,
      syncedComponentCount: syncedComponents.size
    };
  }, [isConnected, connectionStatus, collaborators.size, syncedComponents.size]);
  return {
    // Connection state
    isConnected: isConnected,
    connectionStatus: connectionStatus,
    // Collaboration data
    collaborators: Array.from(collaborators.values()),
    cursors: Array.from(cursors.values()),
    syncedComponents: Array.from(syncedComponents.entries()),
    deviceState: deviceState,
    // Actions
    sendComponentUpdate: sendComponentUpdate,
    sendCursorPosition: sendCursorPosition,
    sendDeviceChange: sendDeviceChange,
    requestSync: requestSync,
    resolveConflict: resolveConflict,
    // Utilities
    getResolvedComponent: getResolvedComponent,
    getCollaborationStatus: getCollaborationStatus,
    // WebSocket service reference
    wsService: wsServiceRef.current
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCollaborativePreview);

/***/ }),

/***/ 79459:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(71468);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_5__);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * Custom hook for real-time preview functionality
 * Handles instant updates, performance optimization, and WebSocket synchronization
 */
var useRealTimePreview = function useRealTimePreview(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onUpdateComponent = _ref.onUpdateComponent,
    onAddComponent = _ref.onAddComponent,
    onDeleteComponent = _ref.onDeleteComponent,
    websocketService = _ref.websocketService,
    _ref$updateDelay = _ref.updateDelay,
    updateDelay = _ref$updateDelay === void 0 ? 300 : _ref$updateDelay,
    _ref$throttleDelay = _ref.throttleDelay,
    throttleDelay = _ref$throttleDelay === void 0 ? 100 : _ref$throttleDelay,
    _ref$enableWebSocket = _ref.enableWebSocket,
    enableWebSocket = _ref$enableWebSocket === void 0 ? true : _ref$enableWebSocket;
  // State for tracking updates
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    isUpdating = _useState2[0],
    setIsUpdating = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    lastUpdateTime = _useState4[0],
    setLastUpdateTime = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Map()),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    pendingUpdates = _useState6[0],
    setPendingUpdates = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    updateQueue = _useState8[0],
    setUpdateQueue = _useState8[1];

  // Refs for cleanup and performance
  var updateTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
  var websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(websocketService);
  var componentCacheRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(new Map());

  // Redux state
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useDispatch */ .wA)();
  var websocketConnected = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return ((_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });

  // Update WebSocket reference when service changes
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    websocketRef.current = websocketService;
  }, [websocketService]);

  // Debounced update function for batching changes
  var debouncedUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_5__.debounce)(function (updates) {
    if (updates.length === 0) return;
    setIsUpdating(true);

    // Process all pending updates
    updates.forEach(function (_ref2) {
      var type = _ref2.type,
        componentId = _ref2.componentId,
        data = _ref2.data;
      switch (type) {
        case 'update':
          if (onUpdateComponent) {
            onUpdateComponent(componentId, data);
          }
          break;
        case 'add':
          if (onAddComponent) {
            onAddComponent(data);
          }
          break;
        case 'delete':
          if (onDeleteComponent) {
            onDeleteComponent(componentId);
          }
          break;
        default:
          console.warn('Unknown update type:', type);
      }
    });

    // Send WebSocket updates if enabled and connected
    if (enableWebSocket && websocketConnected && websocketRef.current) {
      updates.forEach(function (_ref3) {
        var type = _ref3.type,
          componentId = _ref3.componentId,
          data = _ref3.data;
        websocketRef.current.send({
          type: "component_".concat(type),
          component_id: componentId,
          component_data: data,
          timestamp: new Date().toISOString()
        });
      });
    }
    setLastUpdateTime(new Date());
    setUpdateQueue([]);
    setPendingUpdates(new Map());

    // Clear updating state after a short delay
    setTimeout(function () {
      return setIsUpdating(false);
    }, 500);
  }, updateDelay), [onUpdateComponent, onAddComponent, onDeleteComponent, enableWebSocket, websocketConnected, updateDelay]);

  // Throttled function for immediate visual feedback
  var throttledVisualUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_5__.throttle)(function (componentId, updates) {
    // Update component cache for immediate visual feedback
    var currentCache = componentCacheRef.current.get(componentId) || {};
    componentCacheRef.current.set(componentId, _objectSpread(_objectSpread({}, currentCache), updates));

    // Force re-render by updating a timestamp
    setLastUpdateTime(new Date());
  }, throttleDelay), [throttleDelay]);

  // Main update function
  var updateComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId, updates) {
    var immediate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    if (!componentId) return;

    // Add to pending updates
    var currentPending = pendingUpdates.get(componentId) || {};
    var newPending = _objectSpread(_objectSpread({}, currentPending), updates);
    setPendingUpdates(function (prev) {
      return new Map(prev.set(componentId, newPending));
    });

    // Add to update queue
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.filter(function (item) {
        return !(item.type === 'update' && item.componentId === componentId);
      })), [{
        type: 'update',
        componentId: componentId,
        data: newPending
      }]);
    });

    // Immediate visual feedback
    if (immediate) {
      throttledVisualUpdate(componentId, updates);
    }

    // Trigger debounced update
    debouncedUpdate(updateQueue);
  }, [pendingUpdates, updateQueue, debouncedUpdate, throttledVisualUpdate]);

  // Add component function
  var addComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentData) {
    var immediate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var componentId = componentData.id || Date.now().toString();
    var newComponent = _objectSpread(_objectSpread({}, componentData), {}, {
      id: componentId
    });
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [{
        type: 'add',
        componentId: componentId,
        data: newComponent
      }]);
    });
    if (immediate) {
      componentCacheRef.current.set(componentId, newComponent);
      setLastUpdateTime(new Date());
    }
    debouncedUpdate(updateQueue);
    return componentId;
  }, [updateQueue, debouncedUpdate]);

  // Delete component function
  var deleteComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId) {
    var immediate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    if (!componentId) return;
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [{
        type: 'delete',
        componentId: componentId
      }]);
    });
    if (immediate) {
      componentCacheRef.current["delete"](componentId);
      setLastUpdateTime(new Date());
    }
    debouncedUpdate(updateQueue);
  }, [updateQueue, debouncedUpdate]);

  // Get component with cached updates
  var getComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId) {
    var originalComponent = components.find(function (c) {
      return c.id === componentId;
    });
    var cachedUpdates = componentCacheRef.current.get(componentId);
    var pendingUpdate = pendingUpdates.get(componentId);
    return _objectSpread(_objectSpread(_objectSpread({}, originalComponent), cachedUpdates), pendingUpdate);
  }, [components, pendingUpdates]);

  // Get all components with cached updates
  var getAllComponents = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    return components.map(function (component) {
      return getComponent(component.id);
    });
  }, [components, getComponent]);

  // Force update function for manual refresh
  var forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    debouncedUpdate.flush();
    componentCacheRef.current.clear();
    setPendingUpdates(new Map());
    setUpdateQueue([]);
  }, [debouncedUpdate]);

  // WebSocket message handler
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!enableWebSocket || !websocketRef.current) return;
    var handleWebSocketMessage = function handleWebSocketMessage(message) {
      var _message$type;
      if ((_message$type = message.type) !== null && _message$type !== void 0 && _message$type.startsWith('component_')) {
        var component_id = message.component_id,
          component_data = message.component_data,
          timestamp = message.timestamp;

        // Update component cache with remote changes
        if (component_data && component_id) {
          componentCacheRef.current.set(component_id, component_data);
          setLastUpdateTime(new Date(timestamp));
        }
      }
    };
    websocketRef.current.addEventListener('message', handleWebSocketMessage);
    return function () {
      if (websocketRef.current) {
        websocketRef.current.removeEventListener('message', handleWebSocketMessage);
      }
    };
  }, [enableWebSocket]);

  // Cleanup on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    return function () {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      debouncedUpdate.cancel();
      throttledVisualUpdate.cancel();
    };
  }, [debouncedUpdate, throttledVisualUpdate]);
  return {
    // State
    isUpdating: isUpdating,
    lastUpdateTime: lastUpdateTime,
    websocketConnected: websocketConnected,
    hasPendingUpdates: pendingUpdates.size > 0,
    // Functions
    updateComponent: updateComponent,
    addComponent: addComponent,
    deleteComponent: deleteComponent,
    getComponent: getComponent,
    getAllComponents: getAllComponents,
    forceUpdate: forceUpdate,
    // Utilities
    clearCache: function clearCache() {
      return componentCacheRef.current.clear();
    },
    getPendingUpdates: function getPendingUpdates() {
      return Array.from(pendingUpdates.entries());
    },
    getUpdateQueueSize: function getUpdateQueueSize() {
      return updateQueue.length;
    }
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRealTimePreview);

/***/ }),

/***/ 81415:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* unused harmony export usePerformanceOptimization */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);




/**
 * Custom hook for performance optimization
 * Provides utilities for measuring and optimizing performance
 */
var usePerformanceOptimization = function usePerformanceOptimization() {
  var _useState = useState(0),
    _useState2 = _slicedToArray(_useState, 2),
    renderCount = _useState2[0],
    setRenderCount = _useState2[1];
  var _useState3 = useState(0),
    _useState4 = _slicedToArray(_useState3, 2),
    lastRenderTime = _useState4[0],
    setLastRenderTime = _useState4[1];
  var _useState5 = useState([]),
    _useState6 = _slicedToArray(_useState5, 2),
    renderTimes = _useState6[0],
    setRenderTimes = _useState6[1];
  var componentMountTime = useRef(performance.now());

  // Increment render count on each render
  useEffect(function () {
    var now = performance.now();
    setRenderCount(function (prev) {
      return prev + 1;
    });
    if (lastRenderTime > 0) {
      var renderTime = now - lastRenderTime;
      setRenderTimes(function (prev) {
        return [].concat(_toConsumableArray(prev), [renderTime]).slice(-10);
      }); // Keep last 10 render times
    }
    setLastRenderTime(now);

    // Mark the render in the performance timeline
    if (window.performance && window.performance.mark) {
      window.performance.mark("render-".concat(renderCount));
    }
  });

  // Calculate average render time
  var averageRenderTime = useMemo(function () {
    if (renderTimes.length === 0) return 0;
    return renderTimes.reduce(function (sum, time) {
      return sum + time;
    }, 0) / renderTimes.length;
  }, [renderTimes]);

  // Calculate time since mount
  var timeSinceMount = useMemo(function () {
    return performance.now() - componentMountTime.current;
  }, [renderCount]);

  // Measure function execution time
  var measureExecutionTime = useCallback(function (fn) {
    var start = performance.now();
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    var result = fn.apply(void 0, args);
    var end = performance.now();
    return {
      result: result,
      executionTime: end - start
    };
  }, []);

  // Debounce function
  var debounce = useCallback(function (fn, delay) {
    var timeoutId;
    return function () {
      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        args[_key2] = arguments[_key2];
      }
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(function () {
        fn.apply(void 0, args);
      }, delay);
    };
  }, []);

  // Throttle function
  var throttle = useCallback(function (fn, limit) {
    var lastCall = 0;
    return function () {
      var now = performance.now();
      if (now - lastCall < limit) return;
      lastCall = now;
      return fn.apply(void 0, arguments);
    };
  }, []);

  // Memoize expensive function
  var memoize = useCallback(function (fn) {
    var cache = new Map();
    return function () {
      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        args[_key3] = arguments[_key3];
      }
      var key = JSON.stringify(args);
      if (cache.has(key)) return cache.get(key);
      var result = fn.apply(void 0, args);
      cache.set(key, result);
      return result;
    };
  }, []);

  // Log performance metrics
  var logPerformanceMetrics = useCallback(function () {
    console.log('Performance Metrics:');
    console.log("Render Count: ".concat(renderCount));
    console.log("Average Render Time: ".concat(averageRenderTime.toFixed(2), "ms"));
    console.log("Time Since Mount: ".concat(timeSinceMount.toFixed(2), "ms"));
    console.log("Render Times: ".concat(renderTimes.map(function (t) {
      return t.toFixed(2);
    }).join(', '), "ms"));

    // Get performance entries
    if (window.performance && window.performance.getEntriesByType) {
      var paintMetrics = window.performance.getEntriesByType('paint');
      console.log('Paint Metrics:', paintMetrics);
      var navigationMetrics = window.performance.getEntriesByType('navigation');
      console.log('Navigation Metrics:', navigationMetrics);
      var resourceMetrics = window.performance.getEntriesByType('resource');
      console.log('Resource Metrics:', resourceMetrics);
    }
  }, [renderCount, averageRenderTime, timeSinceMount, renderTimes]);
  return {
    renderCount: renderCount,
    averageRenderTime: averageRenderTime,
    timeSinceMount: timeSinceMount,
    renderTimes: renderTimes,
    measureExecutionTime: measureExecutionTime,
    debounce: debounce,
    throttle: throttle,
    memoize: memoize,
    logPerformanceMetrics: logPerformanceMetrics
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (usePerformanceOptimization)));

/***/ }),

/***/ 82569:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZV: () => (/* binding */ useEnhancedTheme),
/* harmony export */   fx: () => (/* binding */ EnhancedThemeProvider)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);




// Enhanced theme context
var EnhancedThemeContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({
  isDarkMode: false,
  themeMode: 'light',
  // 'light', 'dark', 'system'
  toggleDarkMode: function toggleDarkMode() {},
  setThemeMode: function setThemeMode() {},
  colors: {},
  systemPrefersDark: false
});

// Theme colors for light and dark modes
var lightTheme = {
  primary: '#1890ff',
  primaryHover: '#40a9ff',
  secondary: '#52c41a',
  background: '#ffffff',
  backgroundSecondary: '#f5f5f5',
  backgroundTertiary: '#fafafa',
  surface: '#ffffff',
  text: '#000000d9',
  textSecondary: '#00000073',
  textTertiary: '#00000040',
  border: '#d9d9d9',
  borderLight: '#f0f0f0',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#1890ff'
};
var darkTheme = {
  primary: '#1890ff',
  primaryHover: '#40a9ff',
  secondary: '#52c41a',
  background: '#141414',
  backgroundSecondary: '#1f1f1f',
  backgroundTertiary: '#262626',
  surface: '#1f1f1f',
  text: '#ffffffd9',
  textSecondary: '#ffffff73',
  textTertiary: '#ffffff40',
  border: '#434343',
  borderLight: '#303030',
  shadow: 'rgba(0, 0, 0, 0.3)',
  shadowLight: 'rgba(0, 0, 0, 0.2)',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#1890ff'
};

// Enhanced Theme Provider Component
var EnhancedThemeProvider = function EnhancedThemeProvider(_ref) {
  var children = _ref.children;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
      // Check localStorage first, then system preference
      var savedTheme = localStorage.getItem('app-theme-mode');
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        return savedTheme;
      }
      return 'system';
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    themeMode = _useState2[0],
    setThemeMode = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
      return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    systemPrefersDark = _useState4[0],
    setSystemPrefersDark = _useState4[1];

  // Calculate effective dark mode state
  var isDarkMode = themeMode === 'dark' || themeMode === 'system' && systemPrefersDark;

  // Get current theme colors
  var colors = isDarkMode ? darkTheme : lightTheme;

  // Listen for system theme changes
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    var handleChange = function handleChange(e) {
      setSystemPrefersDark(e.matches);
    };
    mediaQuery.addEventListener('change', handleChange);
    return function () {
      return mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  // Apply theme to document
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var root = document.documentElement;

    // Set CSS custom properties
    Object.entries(colors).forEach(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref2, 2),
        key = _ref3[0],
        value = _ref3[1];
      root.style.setProperty("--color-".concat(key), value);
    });

    // Set data attribute for CSS selectors
    root.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

    // Add/remove dark class for compatibility
    if (isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }

    // Update meta theme-color for mobile browsers
    var metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', colors.primary);
    }
  }, [colors, isDarkMode]);

  // Save theme preference to localStorage
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    localStorage.setItem('app-theme-mode', themeMode);
  }, [themeMode]);

  // Toggle between light and dark mode
  var toggleDarkMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {
    setThemeMode(function (current) {
      if (current === 'system') {
        return systemPrefersDark ? 'light' : 'dark';
      }
      return current === 'light' ? 'dark' : 'light';
    });
  }, [systemPrefersDark]);

  // Set specific theme mode
  var handleSetThemeMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (mode) {
    if (['light', 'dark', 'system'].includes(mode)) {
      setThemeMode(mode);
    }
  }, []);

  // Ant Design theme configuration
  var antdThemeConfig = {
    algorithm: isDarkMode ? antd__WEBPACK_IMPORTED_MODULE_2__/* .theme */ .w4.darkAlgorithm : antd__WEBPACK_IMPORTED_MODULE_2__/* .theme */ .w4.defaultAlgorithm,
    token: {
      colorPrimary: colors.primary,
      colorSuccess: colors.success,
      colorWarning: colors.warning,
      colorError: colors.error,
      colorInfo: colors.info,
      colorBgBase: colors.background,
      colorBgContainer: colors.surface,
      colorText: colors.text,
      colorTextSecondary: colors.textSecondary,
      colorBorder: colors.border,
      borderRadius: 6,
      wireframe: false
    },
    components: {
      Layout: {
        bodyBg: colors.background,
        headerBg: colors.surface,
        footerBg: colors.surface
      },
      Card: {
        colorBgContainer: colors.surface
      },
      Menu: {
        colorBgContainer: colors.surface
      }
    }
  };
  var contextValue = {
    isDarkMode: isDarkMode,
    themeMode: themeMode,
    toggleDarkMode: toggleDarkMode,
    setThemeMode: handleSetThemeMode,
    colors: colors,
    systemPrefersDark: systemPrefersDark
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnhancedThemeContext.Provider, {
    value: contextValue
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .ConfigProvider */ .sG, {
    theme: antdThemeConfig
  }, children));
};

// Custom hook to use the enhanced theme context
var useEnhancedTheme = function useEnhancedTheme() {
  var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(EnhancedThemeContext);
  if (!context) {
    throw new Error('useEnhancedTheme must be used within an EnhancedThemeProvider');
  }
  return context;
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (EnhancedThemeContext)));

/***/ })

}]);