"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8552],{

/***/ 38552:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(11080);







var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text;

/**
 * ResetPasswordPage component
 * Allows users to reset their password using a token
 */
var ResetPasswordPage = function ResetPasswordPage() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    tokenValid = _useState4[0],
    setTokenValid = _useState4[1];
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useParams = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .useParams */ .g)(),
    token = _useParams.token;
  var navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .useNavigate */ .Zp)();

  // Validate token on component mount
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    var validateToken = /*#__PURE__*/function () {
      var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return new Promise(function (resolve) {
                return setTimeout(resolve, 500);
              });
            case 3:
              // For demo purposes, consider token valid if it exists and has length > 10
              if (!token || token.length < 10) {
                setTokenValid(false);
                antd__WEBPACK_IMPORTED_MODULE_4__/* .message */ .iU.error('Invalid or expired password reset link');
              }
              _context.next = 11;
              break;
            case 6:
              _context.prev = 6;
              _context.t0 = _context["catch"](0);
              setTokenValid(false);
              antd__WEBPACK_IMPORTED_MODULE_4__/* .message */ .iU.error('Failed to validate reset token');
              console.error('Error validating token:', _context.t0);
            case 11:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 6]]);
      }));
      return function validateToken() {
        return _ref.apply(this, arguments);
      };
    }();
    validateToken();
  }, [token]);

  // Handle form submission
  var handleSubmit = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2(values) {
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            setLoading(true);

            // Simulate API call
            _context2.next = 4;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 1000);
            });
          case 4:
            antd__WEBPACK_IMPORTED_MODULE_4__/* .message */ .iU.success('Password has been reset successfully');
            navigate('/login');
            _context2.next = 12;
            break;
          case 8:
            _context2.prev = 8;
            _context2.t0 = _context2["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_4__/* .message */ .iU.error('Failed to reset password');
            console.error('Error resetting password:', _context2.t0);
          case 12:
            _context2.prev = 12;
            setLoading(false);
            return _context2.finish(12);
          case 15:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 8, 12, 15]]);
    }));
    return function handleSubmit(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  if (!tokenValid) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        padding: '20px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
      style: {
        width: '100%',
        maxWidth: 400,
        textAlign: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Invalid Reset Link"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      type: "secondary"
    }, "The password reset link is invalid or has expired."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        marginTop: 24
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, {
      to: "/forgot-password"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: "primary"
    }, "Request New Link")))));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      padding: '20px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    style: {
      width: '100%',
      maxWidth: 400
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      textAlign: 'center',
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 3
  }, "Reset Password"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Enter your new password below")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: form,
    layout: "vertical",
    onFinish: handleSubmit
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "password",
    rules: [{
      required: true,
      message: 'Please enter your new password'
    }, {
      min: 8,
      message: 'Password must be at least 8 characters'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .LockOutlined */ .sXv, null),
    placeholder: "New Password",
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "confirmPassword",
    dependencies: ['password'],
    rules: [{
      required: true,
      message: 'Please confirm your password'
    }, function (_ref3) {
      var getFieldValue = _ref3.getFieldValue;
      return {
        validator: function validator(_, value) {
          if (!value || getFieldValue('password') === value) {
            return Promise.resolve();
          }
          return Promise.reject(new Error('The two passwords do not match'));
        }
      };
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .LockOutlined */ .sXv, null),
    placeholder: "Confirm Password",
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "primary",
    htmlType: "submit",
    loading: loading,
    block: true,
    size: "large"
  }, "Reset Password")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, {
    to: "/login"
  }, "Back to Login")))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResetPasswordPage);

/***/ })

}]);