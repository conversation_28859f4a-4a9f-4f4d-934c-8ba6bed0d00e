"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4043],{

/***/ 54043:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ builder_EnhancedComponentBuilder)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./src/hooks/useAIDesignSuggestions.js
var useAIDesignSuggestions = __webpack_require__(87169);
// EXTERNAL MODULE: ./node_modules/prop-types/index.js
var prop_types = __webpack_require__(5556);
var prop_types_default = /*#__PURE__*/__webpack_require__.n(prop_types);
;// ./src/components/ai/ComponentCombinationCard.js








var Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;

/**
 * Component Combination Card
 * Displays individual component combination suggestions
 */
var ComponentCombinationCard = function ComponentCombinationCard(_ref) {
  var suggestion = _ref.suggestion,
    onApply = _ref.onApply,
    onPreview = _ref.onPreview,
    _ref$applied = _ref.applied,
    applied = _ref$applied === void 0 ? false : _ref$applied,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    _ref$showScore = _ref.showScore,
    showScore = _ref$showScore === void 0 ? true : _ref$showScore,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    previewVisible = _useState2[0],
    setPreviewVisible = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    applying = _useState4[0],
    setApplying = _useState4[1];

  // Handle apply button click
  var handleApply = /*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
      return regenerator_default().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(applied || applying)) {
              _context.next = 2;
              break;
            }
            return _context.abrupt("return");
          case 2:
            setApplying(true);
            _context.prev = 3;
            if (!onApply) {
              _context.next = 7;
              break;
            }
            _context.next = 7;
            return onApply(suggestion);
          case 7:
            _context.next = 12;
            break;
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](3);
            console.error('Error applying component combination:', _context.t0);
          case 12:
            _context.prev = 12;
            setApplying(false);
            return _context.finish(12);
          case 15:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[3, 9, 12, 15]]);
    }));
    return function handleApply() {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle preview button click
  var handlePreview = function handlePreview() {
    if (onPreview) {
      onPreview(suggestion);
    } else {
      setPreviewVisible(true);
    }
  };

  // Get score color based on value
  var getScoreColor = function getScoreColor(score) {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#1890ff';
    if (score >= 40) return '#faad14';
    return '#ff4d4f';
  };

  // Get component icon
  var getComponentIcon = function getComponentIcon(componentType) {
    var iconMap = {
      button: '🔘',
      form: '📝',
      input: '📝',
      text: '📄',
      image: '🖼️',
      card: '🃏',
      header: '📋',
      nav: '🧭',
      list: '📋',
      divider: '➖',
      section: '📦',
      modal: '🪟',
      table: '📊',
      chart: '📈'
    };
    return iconMap[componentType] || '🔧';
  };

  // Render component combination preview
  var renderCombinationPreview = function renderCombinationPreview() {
    var _suggestion$component = suggestion.components,
      components = _suggestion$component === void 0 ? [] : _suggestion$component,
      _suggestion$missing_c = suggestion.missing_components,
      missing_components = _suggestion$missing_c === void 0 ? [] : _suggestion$missing_c;
    var allComponents = (0,toConsumableArray/* default */.A)(components);
    return /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px',
        background: '#fafafa',
        borderRadius: '6px',
        border: '1px solid #d9d9d9',
        minHeight: '80px'
      }
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
      size: "small",
      wrap: true
    }, allComponents.map(function (component, index) {
      return /*#__PURE__*/react.createElement("div", {
        key: index,
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
        size: "small",
        style: {
          backgroundColor: missing_components.includes(component) ? '#ff4d4f' : '#1890ff',
          color: 'white',
          fontSize: '12px'
        }
      }, getComponentIcon(component)), /*#__PURE__*/react.createElement("div", {
        style: {
          fontSize: '10px',
          marginTop: '2px',
          color: missing_components.includes(component) ? '#ff4d4f' : '#666'
        }
      }, component));
    }), missing_components.length > 0 && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, {
      style: {
        color: '#999',
        margin: '0 4px'
      }
    }), missing_components.map(function (component, index) {
      return /*#__PURE__*/react.createElement("div", {
        key: "missing-".concat(index),
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
        size: "small",
        style: {
          backgroundColor: '#52c41a',
          color: 'white',
          fontSize: '12px',
          border: '2px dashed #52c41a'
        }
      }, getComponentIcon(component)), /*#__PURE__*/react.createElement("div", {
        style: {
          fontSize: '10px',
          marginTop: '2px',
          color: '#52c41a'
        }
      }, "+", component));
    }))));
  };

  // Render detailed preview modal
  var renderPreviewModal = function renderPreviewModal() {
    return /*#__PURE__*/react.createElement(es/* Modal */.aF, {
      title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null), suggestion.name, showScore && /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
        count: suggestion.score,
        style: {
          backgroundColor: getScoreColor(suggestion.score)
        }
      })),
      open: previewVisible,
      onCancel: function onCancel() {
        return setPreviewVisible(false);
      },
      footer: [/*#__PURE__*/react.createElement(es/* Button */.$n, {
        key: "cancel",
        onClick: function onClick() {
          return setPreviewVisible(false);
        }
      }, "Close"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
        key: "apply",
        type: "primary",
        onClick: function onClick() {
          setPreviewVisible(false);
          handleApply();
        },
        disabled: applied,
        loading: applying
      }, applied ? 'Applied' : 'Add Components')],
      width: 600
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        marginBottom: '16px'
      }
    }, renderCombinationPreview()), /*#__PURE__*/react.createElement(Paragraph, null, suggestion.description), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, "Why this combination?"), /*#__PURE__*/react.createElement(Paragraph, {
      type: "secondary"
    }, suggestion.explanation)), suggestion.missing_components && suggestion.missing_components.length > 0 && /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, "Components to add:"), /*#__PURE__*/react.createElement("div", {
      style: {
        marginTop: '8px'
      }
    }, suggestion.missing_components.map(function (component, index) {
      return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
        key: index,
        color: "green",
        style: {
          marginBottom: '4px'
        }
      }, /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, {
        style: {
          marginRight: '4px'
        }
      }), component);
    }))), suggestion.use_cases && /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, "Best for:"), /*#__PURE__*/react.createElement("div", {
      style: {
        marginTop: '8px'
      }
    }, suggestion.use_cases.map(function (useCase, index) {
      return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
        key: index,
        color: "blue",
        style: {
          marginBottom: '4px'
        }
      }, useCase.replace('_', ' '));
    })))));
  };
  if (compact) {
    var _suggestion$component2, _suggestion$missing_c2, _suggestion$missing_c3;
    return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        padding: '8px',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        marginBottom: '8px',
        background: applied ? '#f6ffed' : 'white'
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        width: '60px',
        marginRight: '12px'
      }
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
      size: "small"
    }, (_suggestion$component2 = suggestion.components) === null || _suggestion$component2 === void 0 ? void 0 : _suggestion$component2.slice(0, 2).map(function (component, index) {
      return /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
        key: index,
        size: "small",
        style: {
          backgroundColor: '#1890ff',
          fontSize: '10px'
        }
      }, getComponentIcon(component));
    }), ((_suggestion$missing_c2 = suggestion.missing_components) === null || _suggestion$missing_c2 === void 0 ? void 0 : _suggestion$missing_c2.length) > 0 && /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
      size: "small",
      style: {
        backgroundColor: '#52c41a',
        fontSize: '10px'
      }
    }, "+"))), /*#__PURE__*/react.createElement("div", {
      style: {
        flex: 1
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '2px'
      }
    }, /*#__PURE__*/react.createElement(Text, {
      strong: true,
      style: {
        fontSize: '12px'
      }
    }, suggestion.name), showScore && /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      count: suggestion.score,
      style: {
        backgroundColor: getScoreColor(suggestion.score),
        marginLeft: '8px'
      }
    })), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '11px'
      }
    }, ((_suggestion$missing_c3 = suggestion.missing_components) === null || _suggestion$missing_c3 === void 0 ? void 0 : _suggestion$missing_c3.length) > 0 ? "Add ".concat(suggestion.missing_components.join(', ')) : suggestion.explanation)), /*#__PURE__*/react.createElement(es/* Space */.$x, null, showPreview && /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Preview"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
      onClick: handlePreview
    })), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: applied ? 'default' : 'primary',
      size: "small",
      icon: applied ? /*#__PURE__*/react.createElement(icons_es/* CheckOutlined */.JIb, null) : /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null),
      onClick: handleApply,
      disabled: applied,
      loading: applying
    }, applied ? 'Added' : 'Add'))), renderPreviewModal());
  }
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    size: "small",
    style: {
      marginBottom: '12px',
      border: applied ? '2px solid #52c41a' : '1px solid #d9d9d9'
    },
    title: /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, suggestion.name), showScore && /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      count: suggestion.score,
      style: {
        backgroundColor: getScoreColor(suggestion.score)
      }
    })), applied && /*#__PURE__*/react.createElement(icons_es/* CheckOutlined */.JIb, {
      style: {
        color: '#52c41a'
      }
    })),
    extra: /*#__PURE__*/react.createElement(es/* Space */.$x, null, showPreview && /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Preview combination"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
      onClick: handlePreview
    })), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: applied ? 'default' : 'primary',
      size: "small",
      icon: applied ? /*#__PURE__*/react.createElement(icons_es/* CheckOutlined */.JIb, null) : /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null),
      onClick: handleApply,
      disabled: applied,
      loading: applying
    }, applied ? 'Added' : 'Add Components'))
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: '12px'
    }
  }, renderCombinationPreview()), /*#__PURE__*/react.createElement(Paragraph, {
    style: {
      margin: '0 0 8px 0',
      fontSize: '12px',
      color: '#666'
    }
  }, suggestion.description), suggestion.missing_components && suggestion.missing_components.length > 0 && /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: '8px'
    }
  }, /*#__PURE__*/react.createElement(Text, {
    strong: true,
    style: {
      fontSize: '11px'
    }
  }, "Missing components: "), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    size: "small",
    wrap: true
  }, suggestion.missing_components.map(function (component, index) {
    return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
      key: index,
      color: "green",
      size: "small"
    }, component);
  }))), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, {
    style: {
      marginRight: '4px',
      color: '#1890ff'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    style: {
      fontSize: '11px',
      fontStyle: 'italic'
    }
  }, suggestion.explanation))), renderPreviewModal());
};
ComponentCombinationCard.propTypes = {
  suggestion: prop_types_default().shape({
    id: (prop_types_default()).string.isRequired,
    name: (prop_types_default()).string.isRequired,
    description: (prop_types_default()).string.isRequired,
    score: (prop_types_default()).number.isRequired,
    explanation: (prop_types_default()).string.isRequired,
    components: (prop_types_default()).array,
    missing_components: (prop_types_default()).array,
    use_cases: (prop_types_default()).array
  }).isRequired,
  onApply: (prop_types_default()).func,
  onPreview: (prop_types_default()).func,
  applied: (prop_types_default()).bool,
  showPreview: (prop_types_default()).bool,
  showScore: (prop_types_default()).bool,
  compact: (prop_types_default()).bool
};
/* harmony default export */ const ai_ComponentCombinationCard = ((/* unused pure expression or super */ null && (ComponentCombinationCard)));

/**
 * Component Combinations List Component
 * Container for multiple component combination cards
 */
var ComponentCombinationsList = function ComponentCombinationsList(_ref3) {
  var _ref3$suggestions = _ref3.suggestions,
    suggestions = _ref3$suggestions === void 0 ? [] : _ref3$suggestions,
    onApply = _ref3.onApply,
    onPreview = _ref3.onPreview,
    _ref3$appliedSuggesti = _ref3.appliedSuggestions,
    appliedSuggestions = _ref3$appliedSuggesti === void 0 ? new Set() : _ref3$appliedSuggesti,
    _ref3$loading = _ref3.loading,
    loading = _ref3$loading === void 0 ? false : _ref3$loading,
    _ref3$compact = _ref3.compact,
    compact = _ref3$compact === void 0 ? false : _ref3$compact,
    _ref3$showScore = _ref3.showScore,
    showScore = _ref3$showScore === void 0 ? true : _ref3$showScore,
    _ref3$showPreview = _ref3.showPreview,
    showPreview = _ref3$showPreview === void 0 ? true : _ref3$showPreview,
    _ref3$emptyMessage = _ref3.emptyMessage,
    emptyMessage = _ref3$emptyMessage === void 0 ? "No component combinations available" : _ref3$emptyMessage;
  if (loading) {
    return /*#__PURE__*/react.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px'
      }
    }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
      tip: "Finding component combinations..."
    }));
  }
  if (suggestions.length === 0) {
    return /*#__PURE__*/react.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px',
        color: '#999'
      }
    }, /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, {
      style: {
        fontSize: '48px',
        marginBottom: '16px'
      }
    }), /*#__PURE__*/react.createElement("div", null, emptyMessage));
  }
  return /*#__PURE__*/react.createElement("div", {
    style: {
      maxHeight: '400px',
      overflowY: 'auto'
    }
  }, suggestions.map(function (suggestion) {
    return /*#__PURE__*/react.createElement(ComponentCombinationCard, {
      key: suggestion.id,
      suggestion: suggestion,
      onApply: onApply,
      onPreview: onPreview,
      applied: appliedSuggestions.has(suggestion.id),
      compact: compact,
      showScore: showScore,
      showPreview: showPreview
    });
  }));
};
ComponentCombinationsList.propTypes = {
  suggestions: (prop_types_default()).array,
  onApply: (prop_types_default()).func,
  onPreview: (prop_types_default()).func,
  appliedSuggestions: prop_types_default().instanceOf(Set),
  loading: (prop_types_default()).bool,
  compact: (prop_types_default()).bool,
  showScore: (prop_types_default()).bool,
  showPreview: (prop_types_default()).bool,
  emptyMessage: (prop_types_default()).string
};
;// ./src/components/builder/EnhancedComponentPalette.js





var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }







var Search = es/* Input */.pd.Search;
var EnhancedComponentPalette_Text = es/* Typography */.o5.Text,
  Title = es/* Typography */.o5.Title;
var Panel = es/* Collapse */.SD.Panel;

// Styled components for enhanced visual design
var PaletteContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n"])));
var PaletteHeader = styled_components_browser_esm/* default */.Ay.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 16px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  \n  .ant-typography {\n    color: white !important;\n    margin-bottom: 8px;\n  }\n"])));
var SearchContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n"])));
var ComponentCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin: 4px;\n  cursor: grab;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: ", ";\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n    border-color: #1890ff;\n  }\n  \n  &:active {\n    cursor: grabbing;\n    transform: scale(0.98);\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    text-align: center;\n  }\n"])), function (props) {
  return props.isDragging ? '#e6f7ff' : '#fff';
});
var ComponentIcon = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 24px;\n  color: #1890ff;\n  margin-bottom: 8px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: rgba(24, 144, 255, 0.1);\n  margin: 0 auto 8px;\n  transition: all 0.3s ease;\n  \n  ", ":hover & {\n    background: rgba(24, 144, 255, 0.2);\n    transform: scale(1.1);\n  }\n"])), ComponentCard);
var ComponentLabel = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n"])));
var ComponentDescription = styled_components_browser_esm/* default */.Ay.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 10px;\n  color: #666;\n  line-height: 1.2;\n"])));
var CategoryHeader = styled_components_browser_esm/* default */.Ay.div(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: #f0f2f5;\n  border-bottom: 1px solid #d9d9d9;\n  font-weight: 600;\n  color: #333;\n"])));
var ComponentGrid = styled_components_browser_esm/* default */.Ay.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n  gap: 8px;\n  padding: 16px;\n"])));
var DragIndicator = styled_components_browser_esm/* default */.Ay.div(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  color: #bbb;\n  font-size: 12px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ", ":hover & {\n    opacity: 1;\n  }\n"])), ComponentCard);
var EnhancedComponentPalette = function EnhancedComponentPalette(_ref) {
  var onAddComponent = _ref.onAddComponent,
    onDragStart = _ref.onDragStart,
    onDragEnd = _ref.onDragEnd,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$selectedComponen = _ref.selectedComponent,
    selectedComponent = _ref$selectedComponen === void 0 ? null : _ref$selectedComponen,
    _ref$showAISuggestion = _ref.showAISuggestions,
    showAISuggestions = _ref$showAISuggestion === void 0 ? true : _ref$showAISuggestion;
  var _useState = (0,react.useState)(''),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    searchTerm = _useState2[0],
    setSearchTerm = _useState2[1];
  var _useState3 = (0,react.useState)(['AI Suggestions', 'Layout', 'Basic Components']),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    expandedCategories = _useState4[0],
    setExpandedCategories = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    showDescriptions = _useState6[0],
    setShowDescriptions = _useState6[1];
  var _useState7 = (0,react.useState)(null),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    draggedComponent = _useState8[0],
    setDraggedComponent = _useState8[1];
  var dragPreviewRef = (0,react.useRef)(null);

  // AI suggestions hook
  var _useAIDesignSuggestio = (0,useAIDesignSuggestions/* default */.A)({
      autoRefresh: true,
      context: {
        selectedComponent: selectedComponent
      }
    }),
    suggestions = _useAIDesignSuggestio.suggestions,
    loading = _useAIDesignSuggestio.loading,
    applyComponentCombination = _useAIDesignSuggestio.applyComponentCombination,
    hasLayoutSuggestions = _useAIDesignSuggestio.hasLayoutSuggestions,
    hasCombinationSuggestions = _useAIDesignSuggestio.hasCombinationSuggestions;

  // Handle AI component combination application
  var handleApplyAICombination = /*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(suggestion) {
      var success;
      return regenerator_default().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            success = applyComponentCombination(suggestion);
            if (!success) {
              _context.next = 6;
              break;
            }
            // Also add components via the parent callback
            if (suggestion.missing_components) {
              suggestion.missing_components.forEach(function (componentType) {
                onAddComponent(componentType);
              });
            }
            es/* message */.iU.success("Applied AI suggestion: ".concat(suggestion.name));
            return _context.abrupt("return", true);
          case 6:
            _context.next = 12;
            break;
          case 8:
            _context.prev = 8;
            _context.t0 = _context["catch"](0);
            console.error('Error applying AI combination:', _context.t0);
            es/* message */.iU.error('Failed to apply AI suggestion');
          case 12:
            return _context.abrupt("return", false);
          case 13:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 8]]);
    }));
    return function handleApplyAICombination(_x) {
      return _ref2.apply(this, arguments);
    };
  }();

  // Get smart component suggestions based on current selection
  var getSmartSuggestions = function getSmartSuggestions() {
    if (!selectedComponent) return [];
    var componentType = selectedComponent.type;
    var currentTypes = components.map(function (c) {
      return c.type;
    });
    var smartSuggestions = [];

    // Context-aware suggestions
    if (componentType === 'button' && !currentTypes.includes('form')) {
      smartSuggestions.push({
        type: 'form',
        reason: 'Buttons often work with forms',
        priority: 'high',
        icon: /*#__PURE__*/react.createElement(icons_es/* FormOutlined */.XDk, null),
        label: 'Form'
      });
    }
    if (componentType === 'form' && !currentTypes.includes('input')) {
      smartSuggestions.push({
        type: 'input',
        reason: 'Forms need input fields',
        priority: 'high',
        icon: /*#__PURE__*/react.createElement(icons_es/* FormOutlined */.XDk, null),
        label: 'Input'
      });
    }
    if (componentType === 'text' && !currentTypes.includes('image')) {
      smartSuggestions.push({
        type: 'image',
        reason: 'Text and images work well together',
        priority: 'medium',
        icon: /*#__PURE__*/react.createElement(icons_es/* PictureOutlined */.JZT, null),
        label: 'Image'
      });
    }
    if (componentType === 'card' && !currentTypes.includes('button')) {
      smartSuggestions.push({
        type: 'button',
        reason: 'Cards often need action buttons',
        priority: 'medium',
        icon: /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null),
        label: 'Button'
      });
    }
    return smartSuggestions;
  };
  var smartSuggestions = getSmartSuggestions();

  // Enhanced component data with descriptions and usage hints
  var componentGroups = [].concat((0,toConsumableArray/* default */.A)(showAISuggestions && (hasCombinationSuggestions || smartSuggestions.length > 0) ? [{
    title: 'AI Suggestions',
    description: 'Smart component recommendations based on your current app',
    color: '#1890ff',
    isAI: true,
    components: (0,toConsumableArray/* default */.A)(smartSuggestions.map(function (suggestion) {
      return {
        type: suggestion.type,
        icon: suggestion.icon,
        label: suggestion.label,
        description: suggestion.reason,
        usage: "Recommended for your ".concat(selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.type),
        tags: ['ai', 'smart', 'contextual'],
        priority: suggestion.priority,
        isAISuggestion: true
      };
    }))
  }] : []), [{
    title: 'Layout',
    description: 'Structural components for organizing content',
    color: '#52c41a',
    components: [{
      type: 'header',
      icon: /*#__PURE__*/react.createElement(icons_es/* FontSizeOutlined */.ld1, null),
      label: 'Header',
      description: 'Page or section header with title and navigation',
      usage: 'Use for page titles, navigation bars, or section headers',
      tags: ['layout', 'navigation', 'title']
    }, {
      type: 'section',
      icon: /*#__PURE__*/react.createElement(icons_es/* LayoutOutlined */.hy2, null),
      label: 'Section',
      description: 'Container for grouping related content',
      usage: 'Organize content into logical sections',
      tags: ['layout', 'container', 'organization']
    }, {
      type: 'card',
      icon: /*#__PURE__*/react.createElement(icons_es/* CreditCardOutlined */.wN, null),
      label: 'Card',
      description: 'Flexible content container with optional header and footer',
      usage: 'Display content in a clean, contained format',
      tags: ['layout', 'container', 'content']
    }, {
      type: 'tabs',
      icon: /*#__PURE__*/react.createElement(icons_es/* BarsOutlined */.DX6, null),
      label: 'Tabs',
      description: 'Tabbed interface for organizing content',
      usage: 'Switch between different views or content sections',
      tags: ['layout', 'navigation', 'organization']
    }, {
      type: 'divider',
      icon: /*#__PURE__*/react.createElement(icons_es/* BarsOutlined */.DX6, null),
      label: 'Divider',
      description: 'Visual separator between content sections',
      usage: 'Separate content sections visually',
      tags: ['layout', 'separator', 'visual']
    }]
  }, {
    title: 'Basic Components',
    description: 'Essential UI elements for content and interaction',
    color: '#1890ff',
    components: [{
      type: 'text',
      icon: /*#__PURE__*/react.createElement(icons_es/* FileTextOutlined */.y9H, null),
      label: 'Text',
      description: 'Formatted text content with typography options',
      usage: 'Display paragraphs, headings, and formatted text',
      tags: ['content', 'text', 'typography']
    }, {
      type: 'button',
      icon: /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null),
      label: 'Button',
      description: 'Interactive button for user actions',
      usage: 'Trigger actions, submit forms, or navigate',
      tags: ['interaction', 'action', 'click']
    }, {
      type: 'image',
      icon: /*#__PURE__*/react.createElement(icons_es/* PictureOutlined */.JZT, null),
      label: 'Image',
      description: 'Display images with responsive sizing',
      usage: 'Show photos, illustrations, or graphics',
      tags: ['media', 'visual', 'content']
    }, {
      type: 'list',
      icon: /*#__PURE__*/react.createElement(icons_es/* OrderedListOutlined */.CsJ, null),
      label: 'List',
      description: 'Ordered or unordered list of items',
      usage: 'Display collections of related items',
      tags: ['content', 'organization', 'items']
    }, {
      type: 'tag',
      icon: /*#__PURE__*/react.createElement(icons_es/* TagsOutlined */.owP, null),
      label: 'Tag',
      description: 'Small label for categorization or status',
      usage: 'Label content, show status, or categorize',
      tags: ['label', 'status', 'category']
    }]
  }, {
    title: 'Form Components',
    description: 'Interactive elements for user input and data collection',
    color: '#722ed1',
    components: [{
      type: 'form',
      icon: /*#__PURE__*/react.createElement(icons_es/* FormOutlined */.XDk, null),
      label: 'Form',
      description: 'Container for form fields with validation',
      usage: 'Collect user input with validation and submission',
      tags: ['input', 'validation', 'data']
    }, {
      type: 'input',
      icon: /*#__PURE__*/react.createElement(icons_es/* FormOutlined */.XDk, null),
      label: 'Input',
      description: 'Text input field for user data entry',
      usage: 'Collect text, numbers, or other typed input',
      tags: ['input', 'text', 'data']
    }, {
      type: 'select',
      icon: /*#__PURE__*/react.createElement(icons_es/* FormOutlined */.XDk, null),
      label: 'Select',
      description: 'Dropdown selection from predefined options',
      usage: 'Choose from a list of predefined options',
      tags: ['input', 'selection', 'dropdown']
    }, {
      type: 'checkbox',
      icon: /*#__PURE__*/react.createElement(icons_es/* CheckSquareOutlined */.C50, null),
      label: 'Checkbox',
      description: 'Boolean input for yes/no or multiple selections',
      usage: 'Select multiple options or toggle settings',
      tags: ['input', 'boolean', 'selection']
    }, {
      type: 'datepicker',
      icon: /*#__PURE__*/react.createElement(icons_es/* CalendarOutlined */.hOh, null),
      label: 'Date Picker',
      description: 'Calendar interface for date selection',
      usage: 'Select dates, date ranges, or schedule events',
      tags: ['input', 'date', 'calendar']
    }, {
      type: 'slider',
      icon: /*#__PURE__*/react.createElement(icons_es/* SlidersFilled */.hcX, null),
      label: 'Slider',
      description: 'Range input with visual slider interface',
      usage: 'Select numeric values within a range',
      tags: ['input', 'range', 'numeric']
    }]
  }, {
    title: 'Data Components',
    description: 'Components for displaying and visualizing data',
    color: '#fa8c16',
    components: [{
      type: 'table',
      icon: /*#__PURE__*/react.createElement(icons_es/* TableOutlined */.zDD, null),
      label: 'Table',
      description: 'Structured data display with sorting and filtering',
      usage: 'Display tabular data with advanced features',
      tags: ['data', 'table', 'structured']
    }, {
      type: 'chart',
      icon: /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null),
      label: 'Chart',
      description: 'Visual data representation with multiple chart types',
      usage: 'Visualize data trends and comparisons',
      tags: ['data', 'visualization', 'analytics']
    }, {
      type: 'statistic',
      icon: /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null),
      label: 'Statistic',
      description: 'Highlighted numeric data with formatting',
      usage: 'Display key metrics and KPIs prominently',
      tags: ['data', 'metrics', 'numbers']
    }]
  }]);

  // Filter components based on search term
  var filteredGroups = (0,react.useMemo)(function () {
    if (!searchTerm) return componentGroups;
    return componentGroups.map(function (group) {
      return _objectSpread(_objectSpread({}, group), {}, {
        components: group.components.filter(function (component) {
          return component.label.toLowerCase().includes(searchTerm.toLowerCase()) || component.description.toLowerCase().includes(searchTerm.toLowerCase()) || component.tags.some(function (tag) {
            return tag.toLowerCase().includes(searchTerm.toLowerCase());
          });
        })
      });
    }).filter(function (group) {
      return group.components.length > 0;
    });
  }, [searchTerm]);
  var handleDragStart = function handleDragStart(e, component) {
    setDraggedComponent(component);

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.type,
      label: component.label,
      source: 'palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';

    // Create drag preview
    if (dragPreviewRef.current) {
      var dragImage = dragPreviewRef.current.cloneNode(true);
      dragImage.style.transform = 'rotate(5deg)';
      dragImage.style.opacity = '0.8';
      e.dataTransfer.setDragImage(dragImage, 60, 30);
    }
    if (onDragStart) {
      onDragStart(component);
    }
  };
  var handleDragEnd = function handleDragEnd(e) {
    setDraggedComponent(null);
    if (onDragEnd) {
      onDragEnd();
    }
  };
  var handleCategoryToggle = function handleCategoryToggle(categoryTitle) {
    setExpandedCategories(function (prev) {
      return prev.includes(categoryTitle) ? prev.filter(function (cat) {
        return cat !== categoryTitle;
      }) : [].concat((0,toConsumableArray/* default */.A)(prev), [categoryTitle]);
    });
  };
  return /*#__PURE__*/react.createElement(PaletteContainer, null, /*#__PURE__*/react.createElement(PaletteHeader, null, /*#__PURE__*/react.createElement(Title, {
    level: 5,
    style: {
      margin: 0,
      color: 'white'
    }
  }, "Component Palette"), /*#__PURE__*/react.createElement(EnhancedComponentPalette_Text, {
    style: {
      color: 'rgba(255, 255, 255, 0.8)'
    }
  }, "Drag components to the canvas or click to add")), /*#__PURE__*/react.createElement(SearchContainer, null, /*#__PURE__*/react.createElement(Search, {
    placeholder: "Search components...",
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* SearchOutlined */.VrN, null),
    allowClear: true,
    style: {
      marginBottom: 8
    }
  }), /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(EnhancedComponentPalette_Text, {
    style: {
      fontSize: 12
    }
  }, "Show descriptions:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    size: "small",
    checked: showDescriptions,
    onChange: setShowDescriptions
  }))), filteredGroups.map(function (group) {
    return /*#__PURE__*/react.createElement("div", {
      key: group.title
    }, /*#__PURE__*/react.createElement(CategoryHeader, {
      onClick: function onClick() {
        return handleCategoryToggle(group.title);
      }
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement("div", {
      style: {
        width: 12,
        height: 12,
        borderRadius: '50%',
        backgroundColor: group.color
      }
    }), group.isAI && /*#__PURE__*/react.createElement(icons_es/* RobotOutlined */.J_h, {
      style: {
        color: group.color
      }
    }), /*#__PURE__*/react.createElement("span", null, group.title), /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      count: group.components.length,
      size: "small"
    }), group.isAI && loading.combinations && /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      status: "processing"
    })), /*#__PURE__*/react.createElement(icons_es/* DownOutlined */.lHd, {
      style: {
        transform: expandedCategories.includes(group.title) ? 'rotate(180deg)' : 'rotate(0deg)',
        transition: 'transform 0.3s ease'
      }
    })), expandedCategories.includes(group.title) && /*#__PURE__*/react.createElement(react.Fragment, null, group.isAI && hasCombinationSuggestions && /*#__PURE__*/react.createElement("div", {
      style: {
        padding: '16px',
        borderBottom: '1px solid #f0f0f0'
      }
    }, /*#__PURE__*/react.createElement(EnhancedComponentPalette_Text, {
      type: "secondary",
      style: {
        fontSize: '12px',
        display: 'block',
        marginBottom: '8px'
      }
    }, /*#__PURE__*/react.createElement(icons_es/* BulbOutlined */.o3f, null), " AI-powered component combinations:"), /*#__PURE__*/react.createElement(ComponentCombinationsList, {
      suggestions: suggestions.combinations,
      onApply: handleApplyAICombination,
      loading: loading.combinations,
      compact: true,
      showScore: false,
      showPreview: false,
      emptyMessage: "No AI combinations available"
    })), /*#__PURE__*/react.createElement(ComponentGrid, null, group.components.map(function (component) {
      return /*#__PURE__*/react.createElement(ComponentCard, {
        key: component.type,
        size: "small",
        hoverable: true,
        isDragging: (draggedComponent === null || draggedComponent === void 0 ? void 0 : draggedComponent.type) === component.type,
        draggable: true,
        onDragStart: function onDragStart(e) {
          return handleDragStart(e, component);
        },
        onDragEnd: handleDragEnd,
        onClick: function onClick() {
          return onAddComponent(component.type);
        },
        ref: (draggedComponent === null || draggedComponent === void 0 ? void 0 : draggedComponent.type) === component.type ? dragPreviewRef : null,
        style: {
          border: component.isAISuggestion ? '2px solid #52c41a' : undefined,
          background: component.isAISuggestion ? '#f6ffed' : undefined
        }
      }, /*#__PURE__*/react.createElement(DragIndicator, null, /*#__PURE__*/react.createElement(icons_es/* DragOutlined */.duJ, null)), component.isAISuggestion && /*#__PURE__*/react.createElement("div", {
        style: {
          position: 'absolute',
          top: 4,
          right: 4,
          background: component.priority === 'high' ? '#52c41a' : '#1890ff',
          color: 'white',
          borderRadius: '50%',
          width: 16,
          height: 16,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: 10
        }
      }, /*#__PURE__*/react.createElement(icons_es/* ThunderboltOutlined */.CwG, null)), /*#__PURE__*/react.createElement(ComponentIcon, {
        style: {
          background: component.isAISuggestion ? component.priority === 'high' ? 'rgba(82, 196, 26, 0.1)' : 'rgba(24, 144, 255, 0.1)' : undefined
        }
      }, component.icon), /*#__PURE__*/react.createElement(ComponentLabel, null, component.label), showDescriptions && /*#__PURE__*/react.createElement(ComponentDescription, null, component.description), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
          style: {
            fontWeight: 'bold',
            marginBottom: 4
          }
        }, component.label, component.isAISuggestion && /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
          count: "AI",
          style: {
            backgroundColor: '#52c41a',
            marginLeft: 8
          }
        })), /*#__PURE__*/react.createElement("div", {
          style: {
            marginBottom: 8
          }
        }, component.description), /*#__PURE__*/react.createElement("div", {
          style: {
            fontSize: 11,
            color: '#ccc'
          }
        }, /*#__PURE__*/react.createElement("strong", null, "Usage:"), " ", component.usage), /*#__PURE__*/react.createElement("div", {
          style: {
            fontSize: 11,
            color: '#ccc',
            marginTop: 4
          }
        }, /*#__PURE__*/react.createElement("strong", null, "Tags:"), " ", component.tags.join(', '))),
        placement: "right"
      }, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, {
        style: {
          position: 'absolute',
          top: 4,
          left: 4,
          fontSize: 10,
          color: component.isAISuggestion ? '#52c41a' : '#bbb',
          opacity: 0.7
        }
      })));
    }))));
  }), filteredGroups.length === 0 && /*#__PURE__*/react.createElement("div", {
    style: {
      padding: 32,
      textAlign: 'center',
      color: '#999'
    }
  }, /*#__PURE__*/react.createElement(icons_es/* SearchOutlined */.VrN, {
    style: {
      fontSize: 24,
      marginBottom: 8
    }
  }), /*#__PURE__*/react.createElement("div", null, "No components found matching \"", searchTerm, "\""), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "link",
    size: "small",
    onClick: function onClick() {
      return setSearchTerm('');
    },
    style: {
      padding: 0,
      marginTop: 8
    }
  }, "Clear search")));
};
/* harmony default export */ const builder_EnhancedComponentPalette = (EnhancedComponentPalette);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/lodash/lodash.js
var lodash = __webpack_require__(2543);
// EXTERNAL MODULE: ./src/hooks/useRealTimePreview.js
var useRealTimePreview = __webpack_require__(79459);
// EXTERNAL MODULE: ./src/hooks/usePreviewPerformance.js
var usePreviewPerformance = __webpack_require__(48860);
;// ./src/components/builder/EnhancedPreviewArea.js




var EnhancedPreviewArea_templateObject, EnhancedPreviewArea_templateObject2, EnhancedPreviewArea_templateObject3, EnhancedPreviewArea_templateObject4, EnhancedPreviewArea_templateObject5, EnhancedPreviewArea_templateObject6, EnhancedPreviewArea_templateObject7, EnhancedPreviewArea_templateObject8, EnhancedPreviewArea_templateObject9, EnhancedPreviewArea_templateObject0, _templateObject1, _templateObject10, _templateObject11;
function EnhancedPreviewArea_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function EnhancedPreviewArea_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? EnhancedPreviewArea_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : EnhancedPreviewArea_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








var EnhancedPreviewArea_Title = es/* Typography */.o5.Title,
  EnhancedPreviewArea_Text = es/* Typography */.o5.Text,
  EnhancedPreviewArea_Paragraph = es/* Typography */.o5.Paragraph;
var Option = es/* Select */.l6.Option;

// Device configurations for responsive preview
var DEVICE_CONFIGS = {
  mobile: {
    name: 'Mobile',
    icon: /*#__PURE__*/react.createElement(icons_es/* MobileOutlined */.jHj, null),
    width: 375,
    height: 667,
    scale: 0.8,
    frame: true
  },
  tablet: {
    name: 'Tablet',
    icon: /*#__PURE__*/react.createElement(icons_es/* TabletOutlined */.pLH, null),
    width: 768,
    height: 1024,
    scale: 0.7,
    frame: true
  },
  desktop: {
    name: 'Desktop',
    icon: /*#__PURE__*/react.createElement(icons_es/* DesktopOutlined */.zlw, null),
    width: 1200,
    height: 800,
    scale: 1,
    frame: false
  }
};

// Styled components for enhanced preview area
var PreviewContainer = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject || (EnhancedPreviewArea_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  height: 100%;\n  background: #f5f5f5;\n  border-radius: 8px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n"])));
var PreviewToolbar = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject2 || (EnhancedPreviewArea_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 10;\n  flex-wrap: wrap;\n  gap: 8px;\n\n  @media (max-width: 768px) {\n    padding: 6px 12px;\n    gap: 6px;\n  }\n"])));
var DeviceSelector = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject3 || (EnhancedPreviewArea_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px;\n  background: #f5f5f5;\n  border-radius: 6px;\n"])));
var DeviceButton = (0,styled_components_browser_esm/* default */.Ay)(es/* Button */.$n)(EnhancedPreviewArea_templateObject4 || (EnhancedPreviewArea_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  border: none;\n  background: ", ";\n  color: ", ";\n  box-shadow: none;\n\n  &:hover {\n    background: ", ";\n    color: ", ";\n  }\n"])), function (props) {
  return props.active ? '#1890ff' : 'transparent';
}, function (props) {
  return props.active ? 'white' : '#666';
}, function (props) {
  return props.active ? '#40a9ff' : '#e6f7ff';
}, function (props) {
  return props.active ? 'white' : '#1890ff';
});
var DeviceFrame = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject5 || (EnhancedPreviewArea_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  margin: 20px auto;\n  background: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  box-shadow: ", ";\n  transition: all 0.3s ease;\n\n  ", "\n\n  ", "\n"])), function (props) {
  return props.deviceType === 'mobile' ? '#333' : props.deviceType === 'tablet' ? '#444' : 'transparent';
}, function (props) {
  return props.deviceType === 'mobile' ? '25px' : props.deviceType === 'tablet' ? '15px' : '0';
}, function (props) {
  return props.deviceType === 'mobile' ? '20px 10px' : props.deviceType === 'tablet' ? '15px' : '0';
}, function (props) {
  return props.frame ? '0 8px 32px rgba(0, 0, 0, 0.3)' : 'none';
}, function (props) {
  return props.deviceType === 'mobile' && "\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  ";
}, function (props) {
  return props.deviceType === 'tablet' && "\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  ";
});
var ResponsiveCanvas = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject6 || (EnhancedPreviewArea_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: ", "px;\n  height: ", "px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ", ";\n  overflow: auto;\n  position: relative;\n  transform: scale(", ");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n\n  @media (max-width: 1200px) {\n    transform: scale(", ");\n  }\n\n  @media (max-width: 768px) {\n    transform: scale(", ");\n  }\n"])), function (props) {
  return props.deviceWidth;
}, function (props) {
  return props.deviceHeight;
}, function (props) {
  return props.deviceType === 'mobile' ? '8px' : props.deviceType === 'tablet' ? '6px' : '0';
}, function (props) {
  return props.scale;
}, function (props) {
  return Math.min(props.scale, 0.8);
}, function (props) {
  return Math.min(props.scale, 0.6);
});
var CanvasContainer = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject7 || (EnhancedPreviewArea_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  flex: 1;\n  position: relative;\n  overflow: auto;\n  background: ", ";\n  background-size: ", "px ", "px;\n  background-position: ", "px ", "px;\n"])), function (props) {
  return props.showGrid ? "radial-gradient(circle, #ddd 1px, transparent 1px)" : '#f5f5f5';
}, function (props) {
  return props.gridSize || 20;
}, function (props) {
  return props.gridSize || 20;
}, function (props) {
  var _props$gridOffset;
  return ((_props$gridOffset = props.gridOffset) === null || _props$gridOffset === void 0 ? void 0 : _props$gridOffset.x) || 0;
}, function (props) {
  var _props$gridOffset2;
  return ((_props$gridOffset2 = props.gridOffset) === null || _props$gridOffset2 === void 0 ? void 0 : _props$gridOffset2.y) || 0;
});
var Canvas = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject8 || (EnhancedPreviewArea_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  min-height: 100%;\n  min-width: 100%;\n  position: relative;\n  transform: scale(", ");\n  transform-origin: top left;\n  transition: transform 0.2s ease;\n  padding: ", ";\n"])), function (props) {
  return props.zoom || 1;
}, function (props) {
  return props.previewMode ? '0' : '32px';
});
var ComponentWrapper = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject9 || (EnhancedPreviewArea_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  margin: 8px 0;\n  border: ", ";\n  border-radius: 4px;\n  background: ", ";\n  transition: all 0.3s ease;\n  cursor: ", ";\n  \n  &:hover {\n    border-color: ", ";\n    box-shadow: ", ";\n    transform: ", ";\n  }\n  \n  ", "\n"])), function (props) {
  return props.isSelected ? '2px solid #1890ff' : '1px dashed transparent';
}, function (props) {
  return props.isSelected ? 'rgba(24, 144, 255, 0.05)' : 'white';
}, function (props) {
  return props.previewMode ? 'default' : 'pointer';
}, function (props) {
  return props.previewMode ? 'transparent' : '#1890ff';
}, function (props) {
  return props.previewMode ? 'none' : '0 2px 8px rgba(24, 144, 255, 0.2)';
}, function (props) {
  return props.previewMode ? 'none' : 'translateY(-1px)';
}, function (props) {
  return props.isDragOver && "\n    border-color: #52c41a !important;\n    background: rgba(82, 196, 26, 0.1) !important;\n    transform: scale(1.02);\n  ";
});
var ComponentControls = styled_components_browser_esm/* default */.Ay.div(EnhancedPreviewArea_templateObject0 || (EnhancedPreviewArea_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  display: flex;\n  gap: 4px;\n  background: rgba(255, 255, 255, 0.95);\n  padding: 4px;\n  border-radius: 4px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  opacity: ", ";\n  transform: translateY(", ");\n  transition: all 0.3s ease;\n  z-index: 5;\n"])), function (props) {
  return props.visible ? 1 : 0;
}, function (props) {
  return props.visible ? '0' : '-10px';
});
var ControlButton = (0,styled_components_browser_esm/* default */.Ay)(es/* Button */.$n)(_templateObject1 || (_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n  box-shadow: none;\n  \n  &:hover {\n    background: #f0f0f0;\n    transform: scale(1.1);\n  }\n"])));
var DropZone = styled_components_browser_esm/* default */.Ay.div(_templateObject10 || (_templateObject10 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n  opacity: ", ";\n  pointer-events: ", ";\n  transition: all 0.3s ease;\n  z-index: 1;\n  \n  ", "\n"])), function (props) {
  return props.visible ? 1 : 0;
}, function (props) {
  return props.visible ? 'auto' : 'none';
}, function (props) {
  return props.isActive && "\n    border-color: #52c41a;\n    background: rgba(82, 196, 26, 0.1);\n    \n    &::before {\n      content: 'Drop component here';\n      color: #52c41a;\n      font-weight: 600;\n      font-size: 16px;\n    }\n  ";
});
var Ruler = styled_components_browser_esm/* default */.Ay.div(_templateObject11 || (_templateObject11 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid #e8e8e8;\n  font-size: 10px;\n  color: #666;\n  z-index: 5;\n  \n  ", "\n  \n  ", "\n"])), function (props) {
  return props.orientation === 'horizontal' && "\n    top: 0;\n    left: 32px;\n    right: 0;\n    height: 20px;\n    border-bottom: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to right,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  ";
}, function (props) {
  return props.orientation === 'vertical' && "\n    top: 20px;\n    left: 0;\n    bottom: 0;\n    width: 32px;\n    border-right: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to bottom,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  ";
});
var EnhancedPreviewArea = function EnhancedPreviewArea(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onSelectComponent = _ref.onSelectComponent,
    onDeleteComponent = _ref.onDeleteComponent,
    onUpdateComponent = _ref.onUpdateComponent,
    onMoveComponent = _ref.onMoveComponent,
    _ref$previewMode = _ref.previewMode,
    previewMode = _ref$previewMode === void 0 ? false : _ref$previewMode,
    selectedComponentId = _ref.selectedComponentId,
    onDrop = _ref.onDrop,
    onDragOver = _ref.onDragOver,
    onDragLeave = _ref.onDragLeave,
    _ref$realTimeUpdates = _ref.realTimeUpdates,
    realTimeUpdates = _ref$realTimeUpdates === void 0 ? true : _ref$realTimeUpdates,
    _ref$websocketConnect = _ref.websocketConnected,
    websocketConnected = _ref$websocketConnect === void 0 ? false : _ref$websocketConnect;
  // Device and preview state
  var _useState = (0,react.useState)('desktop'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    deviceType = _useState2[0],
    setDeviceType = _useState2[1];
  var _useState3 = (0,react.useState)(1),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    zoom = _useState4[0],
    setZoom = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    showGrid = _useState6[0],
    setShowGrid = _useState6[1];
  var _useState7 = (0,react.useState)(20),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    gridSize = _useState8[0],
    setGridSize = _useState8[1];
  var _useState9 = (0,react.useState)(false),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    showRulers = _useState0[0],
    setShowRulers = _useState0[1];
  var _useState1 = (0,react.useState)(true),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    snapToGrid = _useState10[0],
    setSnapToGrid = _useState10[1];
  var _useState11 = (0,react.useState)(false),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    isDragOver = _useState12[0],
    setIsDragOver = _useState12[1];
  var _useState13 = (0,react.useState)(null),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    dragOverComponent = _useState14[0],
    setDragOverComponent = _useState14[1];
  var _useState15 = (0,react.useState)(null),
    _useState16 = (0,slicedToArray/* default */.A)(_useState15, 2),
    hoveredComponent = _useState16[0],
    setHoveredComponent = _useState16[1];
  var _useState17 = (0,react.useState)(false),
    _useState18 = (0,slicedToArray/* default */.A)(_useState17, 2),
    isUpdating = _useState18[0],
    setIsUpdating = _useState18[1];
  var _useState19 = (0,react.useState)(null),
    _useState20 = (0,slicedToArray/* default */.A)(_useState19, 2),
    lastUpdateTime = _useState20[0],
    setLastUpdateTime = _useState20[1];

  // Refs
  var canvasRef = (0,react.useRef)(null);
  var updateTimeoutRef = (0,react.useRef)(null);

  // Redux state
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var websocketState = (0,react_redux/* useSelector */.d4)(function (state) {
    return state.websocket || {};
  });
  var websocketService = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$websocket;
    return (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.service;
  });

  // Get current device configuration
  var currentDevice = DEVICE_CONFIGS[deviceType];

  // Memoized device-specific styles
  var deviceStyles = (0,react.useMemo)(function () {
    return {
      width: currentDevice.width,
      height: currentDevice.height,
      scale: currentDevice.scale
    };
  }, [currentDevice]);

  // Real-time preview hook
  var _useRealTimePreview = (0,useRealTimePreview/* default */.A)({
      components: components,
      onUpdateComponent: onUpdateComponent,
      onAddComponent: function onAddComponent(componentData) {
        // Handle adding component through parent callback
        if (onUpdateComponent) {
          onUpdateComponent(componentData);
        }
      },
      onDeleteComponent: onDeleteComponent,
      websocketService: websocketService,
      enableWebSocket: realTimeUpdates && websocketConnected
    }),
    realtimeUpdating = _useRealTimePreview.isUpdating,
    realtimeLastUpdate = _useRealTimePreview.lastUpdateTime,
    realtimeWebsocketConnected = _useRealTimePreview.websocketConnected,
    realtimeUpdateComponent = _useRealTimePreview.updateComponent,
    realtimeAddComponent = _useRealTimePreview.addComponent,
    realtimeDeleteComponent = _useRealTimePreview.deleteComponent,
    realtimeGetAllComponents = _useRealTimePreview.getAllComponents,
    realtimeForceUpdate = _useRealTimePreview.forceUpdate;

  // Performance optimization hook
  var _usePreviewPerformanc = (0,usePreviewPerformance/* default */.A)({
      components: realtimeGetAllComponents(),
      containerHeight: deviceStyles.height,
      itemHeight: deviceType === 'mobile' ? 60 : deviceType === 'tablet' ? 80 : 100,
      enableVirtualization: components.length > 20,
      enablePerformanceMonitoring: true
    }),
    visibleComponents = _usePreviewPerformanc.visibleComponents,
    getContainerProps = _usePreviewPerformanc.getContainerProps,
    getSpacerProps = _usePreviewPerformanc.getSpacerProps,
    renderTime = _usePreviewPerformanc.renderTime,
    frameRate = _usePreviewPerformanc.frameRate,
    memoryUsage = _usePreviewPerformanc.memoryUsage,
    startRenderMeasurement = _usePreviewPerformanc.startRenderMeasurement,
    endRenderMeasurement = _usePreviewPerformanc.endRenderMeasurement,
    getCachedComponent = _usePreviewPerformanc.getCachedComponent;

  // Handle device type change
  var handleDeviceChange = (0,react.useCallback)(function (newDeviceType) {
    setDeviceType(newDeviceType);
    // Adjust zoom for better fit
    var device = DEVICE_CONFIGS[newDeviceType];
    if (device.scale !== zoom) {
      setZoom(device.scale);
    }
  }, [zoom]);

  // Handle zoom controls
  var handleZoomIn = function handleZoomIn() {
    return setZoom(function (prev) {
      return Math.min(prev + 0.1, 2);
    });
  };
  var handleZoomOut = function handleZoomOut() {
    return setZoom(function (prev) {
      return Math.max(prev - 0.1, 0.5);
    });
  };
  var handleZoomReset = function handleZoomReset() {
    return setZoom(currentDevice.scale);
  };

  // Real-time component update handler
  var handleRealTimeUpdate = (0,react.useCallback)(function (componentId, updates) {
    var immediate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
    if (realTimeUpdates) {
      realtimeUpdateComponent(componentId, updates, immediate);
      setIsUpdating(true);
      setLastUpdateTime(new Date());

      // Clear updating state after a short delay
      setTimeout(function () {
        return setIsUpdating(false);
      }, 500);
    }
  }, [realTimeUpdates, realtimeUpdateComponent]);

  // Update state when real-time hooks change
  (0,react.useEffect)(function () {
    if (realtimeUpdating !== isUpdating) {
      setIsUpdating(realtimeUpdating);
    }
    if (realtimeLastUpdate && realtimeLastUpdate !== lastUpdateTime) {
      setLastUpdateTime(realtimeLastUpdate);
    }
  }, [realtimeUpdating, realtimeLastUpdate, isUpdating, lastUpdateTime]);

  // Cleanup on unmount
  (0,react.useEffect)(function () {
    return function () {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Handle drag and drop
  var handleDragEnter = (0,react.useCallback)(function (e) {
    e.preventDefault();
    setIsDragOver(true);
  }, []);
  var handleDragOver = (0,react.useCallback)(function (e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    if (onDragOver) onDragOver(e);
  }, [onDragOver]);
  var handleDragLeave = (0,react.useCallback)(function (e) {
    e.preventDefault();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
      setDragOverComponent(null);
      if (onDragLeave) onDragLeave(e);
    }
  }, [onDragLeave]);
  var handleDrop = (0,react.useCallback)(function (e) {
    e.preventDefault();
    setIsDragOver(false);
    setDragOverComponent(null);
    if (onDrop) {
      var _canvasRef$current;
      var rect = (_canvasRef$current = canvasRef.current) === null || _canvasRef$current === void 0 ? void 0 : _canvasRef$current.getBoundingClientRect();
      if (rect) {
        var x = (e.clientX - rect.left) / zoom;
        var y = (e.clientY - rect.top) / zoom;

        // Snap to grid if enabled
        var finalX = snapToGrid ? Math.round(x / gridSize) * gridSize : x;
        var finalY = snapToGrid ? Math.round(y / gridSize) * gridSize : y;
        onDrop(e, {
          x: finalX,
          y: finalY
        });
      }
    }
  }, [onDrop, zoom, snapToGrid, gridSize]);

  // Component drag over handler
  var handleComponentDragOver = (0,react.useCallback)(function (e, componentId) {
    e.preventDefault();
    e.stopPropagation();
    setDragOverComponent(componentId);
  }, []);
  var handleComponentDragLeave = (0,react.useCallback)(function (e, componentId) {
    e.preventDefault();
    e.stopPropagation();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setDragOverComponent(null);
    }
  }, []);

  // Render individual component with responsive behavior and caching
  var renderComponent = (0,react.useCallback)(function (componentData, index) {
    var component = componentData.component || componentData;
    var componentIndex = componentData.index !== undefined ? componentData.index : index;
    return getCachedComponent(component.id, function () {
      startRenderMeasurement();
      var isSelected = component.id === selectedComponentId;
      var isHovered = hoveredComponent === component.id;
      var isDraggedOver = dragOverComponent === component.id;

      // Get responsive styles based on device type
      var getResponsiveStyles = function getResponsiveStyles() {
        var baseStyles = {
          fontSize: deviceType === 'mobile' ? '14px' : deviceType === 'tablet' ? '15px' : '16px',
          padding: deviceType === 'mobile' ? '8px' : deviceType === 'tablet' ? '12px' : '16px'
        };
        return baseStyles;
      };
      var ComponentContent = function ComponentContent() {
        var _component$props, _component$props2, _component$props3, _component$props4, _component$props5, _component$props6, _component$props7, _component$props8, _component$props9, _component$props0, _component$props1;
        var responsiveStyles = getResponsiveStyles();
        switch (component.type) {
          case 'text':
            return /*#__PURE__*/react.createElement(EnhancedPreviewArea_Text, {
              style: responsiveStyles
            }, ((_component$props = component.props) === null || _component$props === void 0 ? void 0 : _component$props.content) || 'Sample text');
          case 'button':
            return /*#__PURE__*/react.createElement(es/* Button */.$n, {
              type: ((_component$props2 = component.props) === null || _component$props2 === void 0 ? void 0 : _component$props2.type) || 'default',
              size: deviceType === 'mobile' ? 'small' : 'middle',
              style: {
                fontSize: responsiveStyles.fontSize
              }
            }, ((_component$props3 = component.props) === null || _component$props3 === void 0 ? void 0 : _component$props3.text) || 'Button');
          case 'header':
            return /*#__PURE__*/react.createElement(EnhancedPreviewArea_Title, {
              level: ((_component$props4 = component.props) === null || _component$props4 === void 0 ? void 0 : _component$props4.level) || (deviceType === 'mobile' ? 4 : 2),
              style: responsiveStyles
            }, ((_component$props5 = component.props) === null || _component$props5 === void 0 ? void 0 : _component$props5.text) || 'Header');
          case 'card':
            return /*#__PURE__*/react.createElement(es/* Card */.Zp, {
              title: ((_component$props6 = component.props) === null || _component$props6 === void 0 ? void 0 : _component$props6.title) || 'Card Title',
              size: deviceType === 'mobile' ? 'small' : 'default',
              style: {
                fontSize: responsiveStyles.fontSize
              }
            }, ((_component$props7 = component.props) === null || _component$props7 === void 0 ? void 0 : _component$props7.content) || 'Card content');
          case 'image':
            return /*#__PURE__*/react.createElement("img", {
              src: ((_component$props8 = component.props) === null || _component$props8 === void 0 ? void 0 : _component$props8.src) || 'https://via.placeholder.com/150',
              alt: ((_component$props9 = component.props) === null || _component$props9 === void 0 ? void 0 : _component$props9.alt) || 'Image',
              style: {
                maxWidth: '100%',
                height: 'auto',
                borderRadius: deviceType === 'mobile' ? '4px' : '6px'
              }
            });
          case 'divider':
            return /*#__PURE__*/react.createElement(es/* Divider */.cG, {
              style: responsiveStyles
            }, (_component$props0 = component.props) === null || _component$props0 === void 0 ? void 0 : _component$props0.text);
          case 'input':
            return /*#__PURE__*/react.createElement(es/* Input */.pd, {
              placeholder: ((_component$props1 = component.props) === null || _component$props1 === void 0 ? void 0 : _component$props1.placeholder) || 'Enter text',
              disabled: previewMode ? false : true,
              size: deviceType === 'mobile' ? 'small' : 'middle',
              style: responsiveStyles
            });
          case 'form':
            return /*#__PURE__*/react.createElement(es/* Form */.lV, {
              layout: "vertical",
              size: deviceType === 'mobile' ? 'small' : 'middle'
            }, /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
              label: "Sample Field"
            }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
              placeholder: "Sample input",
              disabled: !previewMode,
              style: responsiveStyles
            })));
          case 'table':
            var columns = [{
              title: 'Name',
              dataIndex: 'name',
              key: 'name'
            }, {
              title: 'Age',
              dataIndex: 'age',
              key: 'age'
            }];
            var data = [{
              key: '1',
              name: 'John',
              age: 32
            }, {
              key: '2',
              name: 'Jane',
              age: 28
            }];
            return /*#__PURE__*/react.createElement(es/* Table */.XI, {
              columns: columns,
              dataSource: data,
              size: deviceType === 'mobile' ? 'small' : 'middle',
              scroll: deviceType === 'mobile' ? {
                x: true
              } : undefined
            });
          default:
            return /*#__PURE__*/react.createElement("div", {
              style: {
                padding: responsiveStyles.padding,
                border: '1px dashed #ccc',
                textAlign: 'center',
                fontSize: responsiveStyles.fontSize,
                borderRadius: deviceType === 'mobile' ? '4px' : '6px'
              }
            }, component.type, " Component");
        }
      };
      return /*#__PURE__*/react.createElement(ComponentWrapper, {
        key: component.id,
        isSelected: isSelected,
        previewMode: previewMode,
        isDragOver: isDraggedOver,
        onClick: function onClick(e) {
          e.stopPropagation();
          if (!previewMode && onSelectComponent) {
            onSelectComponent(component);
          }
        },
        onMouseEnter: function onMouseEnter() {
          return setHoveredComponent(component.id);
        },
        onMouseLeave: function onMouseLeave() {
          return setHoveredComponent(null);
        },
        onDragOver: function onDragOver(e) {
          return handleComponentDragOver(e, component.id);
        },
        onDragLeave: function onDragLeave(e) {
          return handleComponentDragLeave(e, component.id);
        },
        style: {
          padding: deviceType === 'mobile' ? '8px' : deviceType === 'tablet' ? '12px' : '16px',
          position: 'relative',
          margin: deviceType === 'mobile' ? '4px 0' : '8px 0'
        }
      }, /*#__PURE__*/react.createElement(ComponentContent, null), !previewMode && (isSelected || isHovered) && /*#__PURE__*/react.createElement(ComponentControls, {
        visible: isSelected || isHovered
      }, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Edit"
      }, /*#__PURE__*/react.createElement(ControlButton, {
        icon: /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null),
        size: "small",
        onClick: function onClick(e) {
          e.stopPropagation();
          // Trigger real-time update for edit mode
          if (realTimeUpdates) {
            handleRealTimeUpdate(component.id, {
              editing: true
            });
          }
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Copy"
      }, /*#__PURE__*/react.createElement(ControlButton, {
        icon: /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null),
        size: "small",
        onClick: function onClick(e) {
          e.stopPropagation();
          // Handle copy with real-time update
          if (realTimeUpdates) {
            var copiedComponent = EnhancedPreviewArea_objectSpread(EnhancedPreviewArea_objectSpread({}, component), {}, {
              id: Date.now().toString()
            });
            handleRealTimeUpdate(copiedComponent.id, copiedComponent);
          }
        }
      })), deviceType === 'desktop' && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Move Up"
      }, /*#__PURE__*/react.createElement(ControlButton, {
        icon: /*#__PURE__*/react.createElement(icons_es/* ArrowUpOutlined */.lu9, null),
        size: "small",
        onClick: function onClick(e) {
          e.stopPropagation();
          if (onMoveComponent) onMoveComponent(component.id, 'up');
          if (realTimeUpdates) {
            handleRealTimeUpdate(component.id, {
              moved: 'up'
            });
          }
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Move Down"
      }, /*#__PURE__*/react.createElement(ControlButton, {
        icon: /*#__PURE__*/react.createElement(icons_es/* ArrowDownOutlined */.Axk, null),
        size: "small",
        onClick: function onClick(e) {
          e.stopPropagation();
          if (onMoveComponent) onMoveComponent(component.id, 'down');
          if (realTimeUpdates) {
            handleRealTimeUpdate(component.id, {
              moved: 'down'
            });
          }
        }
      }))), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Delete"
      }, /*#__PURE__*/react.createElement(ControlButton, {
        icon: /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null),
        size: "small",
        danger: true,
        onClick: function onClick(e) {
          e.stopPropagation();
          if (onDeleteComponent) onDeleteComponent(component.id);
          if (realTimeUpdates) {
            handleRealTimeUpdate(component.id, {
              deleted: true
            });
          }
        }
      }))));
      endRenderMeasurement();
      return result;
    });
  }, [selectedComponentId, hoveredComponent, dragOverComponent, deviceType, previewMode, realTimeUpdates, getCachedComponent, startRenderMeasurement, endRenderMeasurement, handleRealTimeUpdate, onMoveComponent, onDeleteComponent]);
  return /*#__PURE__*/react.createElement(PreviewContainer, null, /*#__PURE__*/react.createElement(PreviewToolbar, null, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(EnhancedPreviewArea_Text, {
    strong: true
  }, "Preview"), /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    type: "vertical"
  }), /*#__PURE__*/react.createElement(DeviceSelector, null, Object.entries(DEVICE_CONFIGS).map(function (_ref2) {
    var _ref3 = (0,slicedToArray/* default */.A)(_ref2, 2),
      key = _ref3[0],
      config = _ref3[1];
    return /*#__PURE__*/react.createElement(DeviceButton, {
      key: key,
      size: "small",
      active: deviceType === key,
      onClick: function onClick() {
        return handleDeviceChange(key);
      },
      icon: config.icon
    }, !previewMode && config.name);
  }))), /*#__PURE__*/react.createElement(es/* Space */.$x, null, realTimeUpdates && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
    status: realtimeWebsocketConnected ? "success" : "error",
    text: realtimeWebsocketConnected ? "Live" : "Offline"
  }), (isUpdating || realtimeUpdating) && /*#__PURE__*/react.createElement(icons_es/* SyncOutlined */.OmY, {
    spin: true
  }),  false && /*#__PURE__*/0, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    type: "vertical"
  })), !previewMode && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Zoom Out"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* ZoomOutOutlined */.uC4, null),
    size: "small",
    onClick: handleZoomOut,
    disabled: zoom <= 0.5
  })), /*#__PURE__*/react.createElement(EnhancedPreviewArea_Text, {
    style: {
      minWidth: 40,
      textAlign: 'center'
    }
  }, Math.round(zoom * 100), "%"), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Zoom In"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* ZoomInOutlined */.$gz, null),
    size: "small",
    onClick: handleZoomIn,
    disabled: zoom >= 2
  })), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    size: "small",
    onClick: handleZoomReset
  }, "Reset"), /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    type: "vertical"
  }))), !previewMode && /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Toggle Grid"
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: showGrid,
    onChange: setShowGrid,
    checkedChildren: /*#__PURE__*/react.createElement(icons_es/* BorderOutlined */.bnM, null),
    unCheckedChildren: /*#__PURE__*/react.createElement(icons_es/* BorderOutlined */.bnM, null),
    size: "small"
  })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Toggle Rulers"
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: showRulers,
    onChange: setShowRulers,
    size: "small"
  })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Snap to Grid"
  }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: snapToGrid,
    onChange: setSnapToGrid,
    size: "small"
  }))), realTimeUpdates && lastUpdateTime && /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(EnhancedPreviewArea_Text, {
    type: "secondary",
    style: {
      fontSize: '12px'
    }
  }, "Updated: ", lastUpdateTime.toLocaleTimeString()))), /*#__PURE__*/react.createElement(CanvasContainer, {
    showGrid: showGrid && !previewMode && deviceType === 'desktop',
    gridSize: gridSize,
    onDragEnter: handleDragEnter,
    onDragOver: handleDragOver,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop
  }, showRulers && !previewMode && deviceType === 'desktop' && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(Ruler, {
    orientation: "horizontal"
  }), /*#__PURE__*/react.createElement(Ruler, {
    orientation: "vertical"
  })), /*#__PURE__*/react.createElement(DeviceFrame, {
    deviceType: deviceType,
    frame: currentDevice.frame
  }, /*#__PURE__*/react.createElement(ResponsiveCanvas, (0,esm_extends/* default */.A)({}, getContainerProps(), {
    ref: canvasRef,
    deviceWidth: deviceStyles.width,
    deviceHeight: deviceStyles.height,
    deviceType: deviceType,
    scale: zoom,
    onClick: function onClick() {
      return onSelectComponent && onSelectComponent(null);
    }
  }),  false && /*#__PURE__*/0, /*#__PURE__*/react.createElement("div", getSpacerProps().before), visibleComponents.length > 0 ? visibleComponents.map(function (componentData, index) {
    return renderComponent(componentData, index);
  }) : components.length === 0 ? /*#__PURE__*/react.createElement(es/* Empty */.Sv, {
    description: /*#__PURE__*/react.createElement("span", null, "No components added yet.", /*#__PURE__*/react.createElement("br", null), previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'),
    style: {
      margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
      fontSize: deviceType === 'mobile' ? '14px' : '16px'
    }
  }) : null, /*#__PURE__*/react.createElement("div", getSpacerProps().after))), /*#__PURE__*/react.createElement(DropZone, {
    visible: isDragOver && !previewMode,
    isActive: isDragOver
  })));
};
/* harmony default export */ const builder_EnhancedPreviewArea = (EnhancedPreviewArea);
;// ./src/components/builder/DragVisualFeedback.js

var DragVisualFeedback_templateObject, DragVisualFeedback_templateObject2, DragVisualFeedback_templateObject3, DragVisualFeedback_templateObject4, DragVisualFeedback_templateObject5, DragVisualFeedback_templateObject6, DragVisualFeedback_templateObject7, DragVisualFeedback_templateObject8, DragVisualFeedback_templateObject9, DragVisualFeedback_templateObject0, DragVisualFeedback_templateObject1, DragVisualFeedback_templateObject10, DragVisualFeedback_templateObject11, _templateObject12;




// Keyframe animations
var pulse = (0,styled_components_browser_esm/* keyframes */.i7)(DragVisualFeedback_templateObject || (DragVisualFeedback_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n"])));
var shake = (0,styled_components_browser_esm/* keyframes */.i7)(DragVisualFeedback_templateObject2 || (DragVisualFeedback_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  0%, 100% { transform: translateX(0); }\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }\n  20%, 40%, 60%, 80% { transform: translateX(3px); }\n"])));
var bounce = (0,styled_components_browser_esm/* keyframes */.i7)(DragVisualFeedback_templateObject3 || (DragVisualFeedback_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n"])));
var fadeIn = (0,styled_components_browser_esm/* keyframes */.i7)(DragVisualFeedback_templateObject4 || (DragVisualFeedback_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n"])));
var glow = (0,styled_components_browser_esm/* keyframes */.i7)(DragVisualFeedback_templateObject5 || (DragVisualFeedback_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);\n  }\n"])));

// Styled components
var DropIndicator = styled_components_browser_esm/* default */.Ay.div(DragVisualFeedback_templateObject6 || (DragVisualFeedback_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  width: 100%;\n  height: 4px;\n  background: ", ";\n  border-radius: 2px;\n  z-index: 1000;\n  animation: ", " 1.5s ease-in-out infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ", ";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    right: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ", ";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n"])), function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, pulse, function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
});
var DropZoneOverlay = styled_components_browser_esm/* default */.Ay.div(DragVisualFeedback_templateObject7 || (DragVisualFeedback_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed ", ";\n  border-radius: 8px;\n  background: ", ";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  animation: ", " 0.3s ease-out;\n  \n  ", "\n  \n  ", "\n"])), function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, function (props) {
  return props.isValid ? 'rgba(82, 196, 26, 0.1)' : 'rgba(255, 77, 79, 0.1)';
}, fadeIn, function (props) {
  return props.isValid && (0,styled_components_browser_esm/* css */.AH)(DragVisualFeedback_templateObject8 || (DragVisualFeedback_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n    animation: ", " 1.5s ease-in-out infinite;\n  "])), pulse);
}, function (props) {
  return !props.isValid && (0,styled_components_browser_esm/* css */.AH)(DragVisualFeedback_templateObject9 || (DragVisualFeedback_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n    animation: ", " 0.5s ease-in-out;\n  "])), shake);
});
var DropZoneMessage = styled_components_browser_esm/* default */.Ay.div(DragVisualFeedback_templateObject0 || (DragVisualFeedback_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  font-weight: 600;\n  color: ", ";\n  animation: ", " 0.3s ease-out 0.1s both;\n"])), function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, fadeIn);
var GhostElement = styled_components_browser_esm/* default */.Ay.div(DragVisualFeedback_templateObject1 || (DragVisualFeedback_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: fixed;\n  pointer-events: none;\n  z-index: 9999;\n  opacity: 0.7;\n  transform: rotate(5deg) scale(0.9);\n  filter: blur(1px);\n  transition: all 0.1s ease-out;\n  border: 2px solid #1890ff;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.9);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n"])));
var DragPreview = styled_components_browser_esm/* default */.Ay.div(DragVisualFeedback_templateObject10 || (DragVisualFeedback_templateObject10 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 8px 12px;\n  background: white;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  color: #1890ff;\n  animation: ", " 2s ease-in-out infinite;\n"])), glow);
var HoverIndicator = styled_components_browser_esm/* default */.Ay.div(DragVisualFeedback_templateObject11 || (DragVisualFeedback_templateObject11 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  background: rgba(24, 144, 255, 0.05);\n  pointer-events: none;\n  z-index: 10;\n  animation: ", " 0.2s ease-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    border: 1px solid rgba(24, 144, 255, 0.3);\n    border-radius: 8px;\n    animation: ", " 2s ease-in-out infinite;\n  }\n"])), fadeIn, pulse);
var SuccessIndicator = styled_components_browser_esm/* default */.Ay.div(_templateObject12 || (_templateObject12 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: #52c41a;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-weight: 600;\n  z-index: 1001;\n  animation: ", " 0.6s ease-out;\n  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);\n"])), bounce);

// Component for drop indicator line
var DropIndicatorLine = function DropIndicatorLine(_ref) {
  var position = _ref.position,
    _ref$isValid = _ref.isValid,
    isValid = _ref$isValid === void 0 ? true : _ref$isValid,
    _ref$visible = _ref.visible,
    visible = _ref$visible === void 0 ? false : _ref$visible;
  if (!visible || !position) return null;
  return /*#__PURE__*/react.createElement(DropIndicator, {
    isValid: isValid,
    style: {
      top: position.y,
      left: position.x,
      width: position.width || '100%'
    }
  });
};

// Component for drop zone overlay
var DropZoneOverlayComponent = function DropZoneOverlayComponent(_ref2) {
  var _ref2$isValid = _ref2.isValid,
    isValid = _ref2$isValid === void 0 ? true : _ref2$isValid,
    _ref2$visible = _ref2.visible,
    visible = _ref2$visible === void 0 ? false : _ref2$visible,
    message = _ref2.message;
  if (!visible) return null;
  return /*#__PURE__*/react.createElement(DropZoneOverlay, {
    isValid: isValid
  }, /*#__PURE__*/react.createElement(DropZoneMessage, {
    isValid: isValid
  }, isValid ? /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null) : /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, null), message || (isValid ? 'Drop here to add component' : 'Invalid drop target')));
};

// Component for ghost element during drag
var DragGhost = function DragGhost(_ref3) {
  var _ref3$visible = _ref3.visible,
    visible = _ref3$visible === void 0 ? false : _ref3$visible,
    position = _ref3.position,
    children = _ref3.children,
    componentType = _ref3.componentType;
  var ghostRef = (0,react.useRef)(null);
  (0,react.useEffect)(function () {
    if (ghostRef.current && visible && position) {
      ghostRef.current.style.left = "".concat(position.x, "px");
      ghostRef.current.style.top = "".concat(position.y, "px");
    }
  }, [visible, position]);
  if (!visible) return null;
  return /*#__PURE__*/react.createElement(GhostElement, {
    ref: ghostRef
  }, children || /*#__PURE__*/react.createElement(DragPreview, null, /*#__PURE__*/react.createElement(icons_es/* DragOutlined */.duJ, null), componentType || 'Component'));
};

// Component for hover indicator
var HoverIndicatorComponent = function HoverIndicatorComponent(_ref4) {
  var _ref4$visible = _ref4.visible,
    visible = _ref4$visible === void 0 ? false : _ref4$visible,
    targetRef = _ref4.targetRef;
  var indicatorRef = (0,react.useRef)(null);
  (0,react.useEffect)(function () {
    if (indicatorRef.current && targetRef !== null && targetRef !== void 0 && targetRef.current && visible) {
      var rect = targetRef.current.getBoundingClientRect();
      var indicator = indicatorRef.current;
      indicator.style.position = 'fixed';
      indicator.style.top = "".concat(rect.top, "px");
      indicator.style.left = "".concat(rect.left, "px");
      indicator.style.width = "".concat(rect.width, "px");
      indicator.style.height = "".concat(rect.height, "px");
    }
  }, [visible, targetRef]);
  if (!visible) return null;
  return /*#__PURE__*/react.createElement(HoverIndicator, {
    ref: indicatorRef
  });
};

// Component for success indicator
var SuccessIndicatorComponent = function SuccessIndicatorComponent(_ref5) {
  var _ref5$visible = _ref5.visible,
    visible = _ref5$visible === void 0 ? false : _ref5$visible,
    _ref5$message = _ref5.message,
    message = _ref5$message === void 0 ? 'Component added!' : _ref5$message;
  if (!visible) return null;
  return /*#__PURE__*/react.createElement(SuccessIndicator, null, /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null), message);
};

// Main visual feedback component
var DragVisualFeedback = function DragVisualFeedback(_ref6) {
  var _ref6$isDragging = _ref6.isDragging,
    isDragging = _ref6$isDragging === void 0 ? false : _ref6$isDragging,
    _ref6$isOver = _ref6.isOver,
    isOver = _ref6$isOver === void 0 ? false : _ref6$isOver,
    _ref6$isValid = _ref6.isValid,
    isValid = _ref6$isValid === void 0 ? true : _ref6$isValid,
    dropPosition = _ref6.dropPosition,
    ghostPosition = _ref6.ghostPosition,
    hoveredElement = _ref6.hoveredElement,
    draggedComponent = _ref6.draggedComponent,
    _ref6$showSuccess = _ref6.showSuccess,
    showSuccess = _ref6$showSuccess === void 0 ? false : _ref6$showSuccess,
    successMessage = _ref6.successMessage,
    dropMessage = _ref6.dropMessage,
    children = _ref6.children;
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(DropIndicatorLine, {
    position: dropPosition,
    isValid: isValid,
    visible: isDragging && isOver && dropPosition
  }), /*#__PURE__*/react.createElement(DropZoneOverlayComponent, {
    isValid: isValid,
    visible: isDragging && isOver,
    message: dropMessage
  }), /*#__PURE__*/react.createElement(DragGhost, {
    visible: isDragging,
    position: ghostPosition,
    componentType: draggedComponent === null || draggedComponent === void 0 ? void 0 : draggedComponent.type
  }), /*#__PURE__*/react.createElement(HoverIndicatorComponent, {
    visible: !isDragging && hoveredElement,
    targetRef: hoveredElement
  }), /*#__PURE__*/react.createElement(SuccessIndicatorComponent, {
    visible: showSuccess,
    message: successMessage
  }), children);
};
/* harmony default export */ const builder_DragVisualFeedback = (DragVisualFeedback);
;// ./src/components/builder/ContextualMenu.js

var ContextualMenu_templateObject, ContextualMenu_templateObject2, ContextualMenu_templateObject3, ContextualMenu_templateObject4;




var ContextMenuContainer = styled_components_browser_esm/* default */.Ay.div(ContextualMenu_templateObject || (ContextualMenu_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: fixed;\n  z-index: 10000;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border: 1px solid #e8e8e8;\n  min-width: 180px;\n  overflow: hidden;\n  \n  .ant-menu {\n    border: none;\n    box-shadow: none;\n  }\n  \n  .ant-menu-item {\n    margin: 0;\n    padding: 8px 16px;\n    height: auto;\n    line-height: 1.4;\n    \n    &:hover {\n      background: #f0f2f5;\n    }\n    \n    &.ant-menu-item-disabled {\n      color: #bfbfbf;\n      cursor: not-allowed;\n      \n      &:hover {\n        background: transparent;\n      }\n    }\n  }\n  \n  .ant-menu-item-icon {\n    margin-right: 8px;\n    font-size: 14px;\n  }\n"])));
var MenuSection = styled_components_browser_esm/* default */.Ay.div(ContextualMenu_templateObject2 || (ContextualMenu_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 4px 0;\n  \n  &:not(:last-child) {\n    border-bottom: 1px solid #f0f0f0;\n  }\n"])));
var MenuItemContent = styled_components_browser_esm/* default */.Ay.div(ContextualMenu_templateObject3 || (ContextualMenu_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n"])));
var MenuItemShortcut = styled_components_browser_esm/* default */.Ay.span(ContextualMenu_templateObject4 || (ContextualMenu_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 11px;\n  color: #999;\n  margin-left: 16px;\n"])));
var ContextualMenu = function ContextualMenu(_ref) {
  var visible = _ref.visible,
    x = _ref.x,
    y = _ref.y,
    onClose = _ref.onClose,
    selectedComponent = _ref.selectedComponent,
    _ref$selectedComponen = _ref.selectedComponents,
    selectedComponents = _ref$selectedComponen === void 0 ? [] : _ref$selectedComponen,
    onCopy = _ref.onCopy,
    onPaste = _ref.onPaste,
    onDelete = _ref.onDelete,
    onEdit = _ref.onEdit,
    onDuplicate = _ref.onDuplicate,
    onMoveUp = _ref.onMoveUp,
    onMoveDown = _ref.onMoveDown,
    onToggleVisibility = _ref.onToggleVisibility,
    onToggleLock = _ref.onToggleLock,
    onGroup = _ref.onGroup,
    onUngroup = _ref.onUngroup,
    onCopyStyle = _ref.onCopyStyle,
    onPasteStyle = _ref.onPasteStyle,
    onProperties = _ref.onProperties,
    _ref$clipboardHasData = _ref.clipboardHasData,
    clipboardHasData = _ref$clipboardHasData === void 0 ? false : _ref$clipboardHasData,
    _ref$canMoveUp = _ref.canMoveUp,
    canMoveUp = _ref$canMoveUp === void 0 ? true : _ref$canMoveUp,
    _ref$canMoveDown = _ref.canMoveDown,
    canMoveDown = _ref$canMoveDown === void 0 ? true : _ref$canMoveDown,
    _ref$canGroup = _ref.canGroup,
    canGroup = _ref$canGroup === void 0 ? false : _ref$canGroup,
    _ref$canUngroup = _ref.canUngroup,
    canUngroup = _ref$canUngroup === void 0 ? false : _ref$canUngroup;
  var menuRef = (0,react.useRef)(null);

  // Position menu and handle viewport boundaries
  (0,react.useEffect)(function () {
    if (visible && menuRef.current) {
      var menu = menuRef.current;
      var rect = menu.getBoundingClientRect();
      var viewportWidth = window.innerWidth;
      var viewportHeight = window.innerHeight;
      var adjustedX = x;
      var adjustedY = y;

      // Adjust horizontal position if menu would overflow
      if (x + rect.width > viewportWidth) {
        adjustedX = viewportWidth - rect.width - 10;
      }

      // Adjust vertical position if menu would overflow
      if (y + rect.height > viewportHeight) {
        adjustedY = viewportHeight - rect.height - 10;
      }
      menu.style.left = "".concat(Math.max(10, adjustedX), "px");
      menu.style.top = "".concat(Math.max(10, adjustedY), "px");
    }
  }, [visible, x, y]);

  // Close menu on escape key
  (0,react.useEffect)(function () {
    var handleKeyDown = function handleKeyDown(e) {
      if (e.key === 'Escape' && visible) {
        onClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      return document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, onClose]);
  if (!visible) return null;
  var isMultipleSelection = selectedComponents.length > 1;
  var hasSelection = selectedComponent || selectedComponents.length > 0;
  var menuItems = [
  // Edit section
  {
    section: 'edit',
    items: [{
      key: 'edit',
      icon: /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null),
      label: 'Edit Properties',
      shortcut: 'Enter',
      disabled: !hasSelection || isMultipleSelection,
      onClick: function onClick() {
        onEdit === null || onEdit === void 0 || onEdit(selectedComponent);
        onClose();
      }
    }, {
      key: 'copy',
      icon: /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null),
      label: isMultipleSelection ? "Copy ".concat(selectedComponents.length, " Components") : 'Copy',
      shortcut: 'Ctrl+C',
      disabled: !hasSelection,
      onClick: function onClick() {
        onCopy === null || onCopy === void 0 || onCopy(isMultipleSelection ? selectedComponents : selectedComponent);
        onClose();
      }
    }, {
      key: 'paste',
      icon: /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, {
        style: {
          transform: 'scaleX(-1)'
        }
      }),
      label: 'Paste',
      shortcut: 'Ctrl+V',
      disabled: !clipboardHasData,
      onClick: function onClick() {
        onPaste === null || onPaste === void 0 || onPaste();
        onClose();
      }
    }, {
      key: 'duplicate',
      icon: /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null),
      label: isMultipleSelection ? 'Duplicate Selection' : 'Duplicate',
      shortcut: 'Ctrl+D',
      disabled: !hasSelection,
      onClick: function onClick() {
        onDuplicate === null || onDuplicate === void 0 || onDuplicate(isMultipleSelection ? selectedComponents : selectedComponent);
        onClose();
      }
    }]
  },
  // Arrange section
  {
    section: 'arrange',
    items: [{
      key: 'move-up',
      icon: /*#__PURE__*/react.createElement(icons_es/* ArrowUpOutlined */.lu9, null),
      label: 'Move Up',
      shortcut: 'Ctrl+↑',
      disabled: !hasSelection || !canMoveUp,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onMoveUp === null || onMoveUp === void 0 ? void 0 : onMoveUp(comp);
          });
        } else {
          onMoveUp === null || onMoveUp === void 0 || onMoveUp(selectedComponent);
        }
        onClose();
      }
    }, {
      key: 'move-down',
      icon: /*#__PURE__*/react.createElement(icons_es/* ArrowDownOutlined */.Axk, null),
      label: 'Move Down',
      shortcut: 'Ctrl+↓',
      disabled: !hasSelection || !canMoveDown,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onMoveDown === null || onMoveDown === void 0 ? void 0 : onMoveDown(comp);
          });
        } else {
          onMoveDown === null || onMoveDown === void 0 || onMoveDown(selectedComponent);
        }
        onClose();
      }
    }]
  },
  // Visibility section
  {
    section: 'visibility',
    items: [{
      key: 'toggle-visibility',
      icon: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.visible) !== false ? /*#__PURE__*/react.createElement(icons_es/* EyeInvisibleOutlined */.LCF, null) : /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
      label: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.visible) !== false ? 'Hide' : 'Show',
      shortcut: 'Ctrl+H',
      disabled: !hasSelection,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onToggleVisibility === null || onToggleVisibility === void 0 ? void 0 : onToggleVisibility(comp);
          });
        } else {
          onToggleVisibility === null || onToggleVisibility === void 0 || onToggleVisibility(selectedComponent);
        }
        onClose();
      }
    }, {
      key: 'toggle-lock',
      icon: selectedComponent !== null && selectedComponent !== void 0 && selectedComponent.locked ? /*#__PURE__*/react.createElement(icons_es/* UnlockOutlined */.Rrh, null) : /*#__PURE__*/react.createElement(icons_es/* LockOutlined */.sXv, null),
      label: selectedComponent !== null && selectedComponent !== void 0 && selectedComponent.locked ? 'Unlock' : 'Lock',
      shortcut: 'Ctrl+L',
      disabled: !hasSelection,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onToggleLock === null || onToggleLock === void 0 ? void 0 : onToggleLock(comp);
          });
        } else {
          onToggleLock === null || onToggleLock === void 0 || onToggleLock(selectedComponent);
        }
        onClose();
      }
    }]
  },
  // Group section
  {
    section: 'group',
    items: [{
      key: 'group',
      icon: /*#__PURE__*/react.createElement(icons_es/* GroupOutlined */.WT5, null),
      label: 'Group',
      shortcut: 'Ctrl+G',
      disabled: !canGroup || selectedComponents.length < 2,
      onClick: function onClick() {
        onGroup === null || onGroup === void 0 || onGroup(selectedComponents);
        onClose();
      }
    }, {
      key: 'ungroup',
      icon: /*#__PURE__*/react.createElement(icons_es/* UngroupOutlined */.T6x, null),
      label: 'Ungroup',
      shortcut: 'Ctrl+Shift+G',
      disabled: !canUngroup,
      onClick: function onClick() {
        onUngroup === null || onUngroup === void 0 || onUngroup(selectedComponent);
        onClose();
      }
    }]
  },
  // Style section
  {
    section: 'style',
    items: [{
      key: 'copy-style',
      icon: /*#__PURE__*/react.createElement(icons_es/* FormatPainterOutlined */.tBh, null),
      label: 'Copy Style',
      disabled: !hasSelection || isMultipleSelection,
      onClick: function onClick() {
        onCopyStyle === null || onCopyStyle === void 0 || onCopyStyle(selectedComponent);
        onClose();
      }
    }, {
      key: 'paste-style',
      icon: /*#__PURE__*/react.createElement(icons_es/* FormatPainterOutlined */.tBh, {
        style: {
          transform: 'scaleX(-1)'
        }
      }),
      label: 'Paste Style',
      disabled: !hasSelection,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onPasteStyle === null || onPasteStyle === void 0 ? void 0 : onPasteStyle(comp);
          });
        } else {
          onPasteStyle === null || onPasteStyle === void 0 || onPasteStyle(selectedComponent);
        }
        onClose();
      }
    }]
  },
  // Actions section
  {
    section: 'actions',
    items: [{
      key: 'properties',
      icon: /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null),
      label: 'Properties',
      shortcut: 'F4',
      disabled: !hasSelection || isMultipleSelection,
      onClick: function onClick() {
        onProperties === null || onProperties === void 0 || onProperties(selectedComponent);
        onClose();
      }
    }, {
      key: 'delete',
      icon: /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null),
      label: isMultipleSelection ? "Delete ".concat(selectedComponents.length, " Components") : 'Delete',
      shortcut: 'Delete',
      disabled: !hasSelection,
      danger: true,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onDelete === null || onDelete === void 0 ? void 0 : onDelete(comp);
          });
        } else {
          onDelete === null || onDelete === void 0 || onDelete(selectedComponent);
        }
        onClose();
      }
    }]
  }];
  return /*#__PURE__*/react.createElement(ContextMenuContainer, {
    ref: menuRef,
    style: {
      left: x,
      top: y
    },
    onClick: function onClick(e) {
      return e.stopPropagation();
    }
  }, /*#__PURE__*/react.createElement(es/* Menu */.W1, {
    mode: "vertical",
    selectable: false
  }, menuItems.map(function (section, sectionIndex) {
    return /*#__PURE__*/react.createElement(MenuSection, {
      key: section.section
    }, section.items.map(function (item) {
      return /*#__PURE__*/react.createElement(es/* Menu */.W1.Item, {
        key: item.key,
        icon: item.icon,
        disabled: item.disabled,
        danger: item.danger,
        onClick: item.onClick
      }, /*#__PURE__*/react.createElement(MenuItemContent, null, /*#__PURE__*/react.createElement("span", null, item.label), item.shortcut && /*#__PURE__*/react.createElement(MenuItemShortcut, null, item.shortcut)));
    }));
  })));
};
/* harmony default export */ const builder_ContextualMenu = (ContextualMenu);
// EXTERNAL MODULE: ./src/hooks/useUndoRedo.js
var useUndoRedo = __webpack_require__(94588);
// EXTERNAL MODULE: ./src/hooks/useEnhancedDragDrop.js
var useEnhancedDragDrop = __webpack_require__(47119);
// EXTERNAL MODULE: ./src/redux/minimal-store.js
var minimal_store = __webpack_require__(34816);
;// ./src/components/builder/EnhancedComponentBuilder.js



var EnhancedComponentBuilder_templateObject, EnhancedComponentBuilder_templateObject2, EnhancedComponentBuilder_templateObject3, EnhancedComponentBuilder_templateObject4, EnhancedComponentBuilder_templateObject5, EnhancedComponentBuilder_templateObject6;
function EnhancedComponentBuilder_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function EnhancedComponentBuilder_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? EnhancedComponentBuilder_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : EnhancedComponentBuilder_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






// Import enhanced components





// Import hooks



// Import Redux actions

var Sider = es/* Layout */.PE.Sider,
  Content = es/* Layout */.PE.Content;

// Styled components
var BuilderContainer = (0,styled_components_browser_esm/* default */.Ay)(es/* Layout */.PE)(EnhancedComponentBuilder_templateObject || (EnhancedComponentBuilder_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  height: 100vh;\n  background: #f5f5f5;\n"])));
var Toolbar = styled_components_browser_esm/* default */.Ay.div(EnhancedComponentBuilder_templateObject2 || (EnhancedComponentBuilder_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 100;\n"])));
var ToolbarSection = styled_components_browser_esm/* default */.Ay.div(EnhancedComponentBuilder_templateObject3 || (EnhancedComponentBuilder_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var EnhancedComponentBuilder_PaletteContainer = (0,styled_components_browser_esm/* default */.Ay)(Sider)(EnhancedComponentBuilder_templateObject4 || (EnhancedComponentBuilder_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  background: white;\n  border-right: 1px solid #e8e8e8;\n  overflow: auto;\n  \n  .ant-layout-sider-children {\n    padding: 16px;\n  }\n"])));
var EnhancedComponentBuilder_PreviewContainer = (0,styled_components_browser_esm/* default */.Ay)(Content)(EnhancedComponentBuilder_templateObject5 || (EnhancedComponentBuilder_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n  position: relative;\n"])));
var LoadingOverlay = styled_components_browser_esm/* default */.Ay.div(EnhancedComponentBuilder_templateObject6 || (EnhancedComponentBuilder_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(2px);\n"])));
var EnhancedComponentBuilder = function EnhancedComponentBuilder() {
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var components = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$app;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || [];
  });

  // State management
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    previewMode = _useState2[0],
    setPreviewMode = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    fullscreen = _useState4[0],
    setFullscreen = _useState4[1];
  var _useState5 = (0,react.useState)(null),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    draggedComponent = _useState6[0],
    setDraggedComponent = _useState6[1];
  var _useState7 = (0,react.useState)(false),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    showSuccess = _useState8[0],
    setShowSuccess = _useState8[1];

  // Hooks
  var _useUndoRedo = (0,useUndoRedo/* useUndoRedo */.aD)(components),
    componentHistory = _useUndoRedo.state,
    pushState = _useUndoRedo.pushState,
    undo = _useUndoRedo.undo,
    redo = _useUndoRedo.redo,
    canUndo = _useUndoRedo.canUndo,
    canRedo = _useUndoRedo.canRedo;
  var _useContextMenu = (0,useUndoRedo/* useContextMenu */.EF)(),
    contextMenu = _useContextMenu.contextMenu,
    showContextMenu = _useContextMenu.showContextMenu,
    hideContextMenu = _useContextMenu.hideContextMenu;
  var _useLoadingState = (0,useUndoRedo/* useLoadingState */.R2)(),
    isLoading = _useLoadingState.isLoading,
    loadingMessage = _useLoadingState.loadingMessage,
    startLoading = _useLoadingState.startLoading,
    stopLoading = _useLoadingState.stopLoading;
  var _useSelection = (0,useUndoRedo/* useSelection */.Cd)(components),
    selectedItems = _useSelection.selectedItems,
    selectItem = _useSelection.selectItem,
    clearSelection = _useSelection.clearSelection,
    isSelected = _useSelection.isSelected;
  var _useClipboard = (0,useUndoRedo/* useClipboard */.iD)(),
    copy = _useClipboard.copy,
    paste = _useClipboard.paste,
    clipboardHasData = _useClipboard.hasData;

  // Refs
  var builderRef = (0,react.useRef)(null);

  // Handle component drop
  var handleComponentDrop = (0,react.useCallback)(function (e, dragData, position) {
    if (!dragData) return;
    startLoading('Adding component...');
    try {
      var newComponent = {
        id: "".concat(dragData.type, "-").concat(Date.now()),
        type: dragData.type,
        props: EnhancedComponentBuilder_objectSpread({
          x: position.x,
          y: position.y
        }, getDefaultProps(dragData.type)),
        createdAt: new Date().toISOString()
      };
      dispatch((0,minimal_store.addComponent)(newComponent));

      // Show success feedback
      setShowSuccess(true);
      setTimeout(function () {
        return setShowSuccess(false);
      }, 2000);
      es/* message */.iU.success("".concat(dragData.label || dragData.type, " component added"));
    } catch (error) {
      es/* message */.iU.error('Failed to add component');
      console.error('Error adding component:', error);
    } finally {
      stopLoading();
    }
  }, [dispatch, startLoading, stopLoading]);

  // Drag and drop with proper callback
  var _useEnhancedDragDrop = (0,useEnhancedDragDrop/* useEnhancedDragDrop */.$l)({
      onDrop: handleComponentDrop,
      snapToGrid: true,
      gridSize: 20
    }),
    isDragging = _useEnhancedDragDrop.isDragging,
    isOver = _useEnhancedDragDrop.isOver,
    validDropZone = _useEnhancedDragDrop.validDropZone,
    dropPosition = _useEnhancedDragDrop.dropPosition,
    dropZoneRef = _useEnhancedDragDrop.dropZoneRef,
    handleDragStart = _useEnhancedDragDrop.handleDragStart,
    handleDragEnd = _useEnhancedDragDrop.handleDragEnd;

  // Update history when components change
  (0,react.useEffect)(function () {
    if (JSON.stringify(components) !== JSON.stringify(componentHistory)) {
      pushState(components);
    }
  }, [components, componentHistory, pushState]);

  // Get default props for component type
  var getDefaultProps = function getDefaultProps(type) {
    var defaults = {
      text: {
        content: 'Sample text',
        fontSize: 14
      },
      button: {
        text: 'Button',
        type: 'default'
      },
      header: {
        text: 'Header',
        level: 2
      },
      card: {
        title: 'Card Title',
        content: 'Card content'
      },
      image: {
        src: 'https://via.placeholder.com/150',
        alt: 'Image'
      },
      input: {
        placeholder: 'Enter text'
      },
      form: {
        layout: 'vertical'
      }
    };
    return defaults[type] || {};
  };

  // Handle component selection
  var handleComponentSelect = (0,react.useCallback)(function (component) {
    if (component) {
      selectItem(component);
    } else {
      clearSelection();
    }
  }, [selectItem, clearSelection]);

  // Handle component deletion
  var handleComponentDelete = (0,react.useCallback)(function (componentId) {
    startLoading('Deleting component...');
    try {
      dispatch((0,minimal_store.removeComponent)(componentId));
      clearSelection();
      es/* message */.iU.success('Component deleted');
    } catch (error) {
      es/* message */.iU.error('Failed to delete component');
    } finally {
      stopLoading();
    }
  }, [dispatch, clearSelection, startLoading, stopLoading]);

  // Handle component update
  var handleComponentUpdate = (0,react.useCallback)(function (componentId, updates) {
    startLoading('Updating component...');
    try {
      dispatch((0,minimal_store.updateComponent)(componentId, updates));
      es/* message */.iU.success('Component updated');
    } catch (error) {
      es/* message */.iU.error('Failed to update component');
    } finally {
      stopLoading();
    }
  }, [dispatch, startLoading, stopLoading]);

  // Handle undo
  var handleUndo = (0,react.useCallback)(function () {
    var previousState = undo();
    if (previousState) {
      // Update Redux store with previous state
      // This would need to be implemented based on your Redux structure
      es/* message */.iU.success('Undone');
    }
  }, [undo]);

  // Handle redo
  var handleRedo = (0,react.useCallback)(function () {
    var nextState = redo();
    if (nextState) {
      // Update Redux store with next state
      // This would need to be implemented based on your Redux structure
      es/* message */.iU.success('Redone');
    }
  }, [redo]);

  // Handle copy
  var handleCopy = (0,react.useCallback)(function (component) {
    copy(component);
    es/* message */.iU.success('Component copied');
  }, [copy]);

  // Handle paste
  var handlePaste = (0,react.useCallback)(function () {
    var copiedComponent = paste();
    if (copiedComponent) {
      var newComponent = EnhancedComponentBuilder_objectSpread(EnhancedComponentBuilder_objectSpread({}, copiedComponent), {}, {
        id: "".concat(copiedComponent.type, "-").concat(Date.now()),
        props: EnhancedComponentBuilder_objectSpread(EnhancedComponentBuilder_objectSpread({}, copiedComponent.props), {}, {
          x: (copiedComponent.props.x || 0) + 20,
          y: (copiedComponent.props.y || 0) + 20
        })
      });
      dispatch((0,minimal_store.addComponent)(newComponent));
      es/* message */.iU.success('Component pasted');
    }
  }, [paste, dispatch]);

  // Handle context menu
  var handleContextMenu = (0,react.useCallback)(function (e, component) {
    e.preventDefault();
    var menuItems = [{
      key: 'edit',
      label: 'Edit',
      icon: 'edit'
    }, {
      key: 'copy',
      label: 'Copy',
      icon: 'copy'
    }, {
      key: 'delete',
      label: 'Delete',
      icon: 'delete',
      danger: true
    }];
    showContextMenu(e, menuItems);
  }, [showContextMenu]);

  // Keyboard shortcuts
  (0,useUndoRedo/* useKeyboardShortcuts */.KW)({
    'ctrl+z': handleUndo,
    'ctrl+y': handleRedo,
    'ctrl+c': function ctrlC() {
      if (selectedItems.length > 0) {
        handleCopy(components.find(function (c) {
          return c.id === selectedItems[0];
        }));
      }
    },
    'ctrl+v': handlePaste,
    'delete': function _delete() {
      if (selectedItems.length > 0) {
        selectedItems.forEach(function (id) {
          return handleComponentDelete(id);
        });
      }
    },
    'escape': function escape() {
      clearSelection();
      hideContextMenu();
    },
    'f11': function f11(e) {
      e.preventDefault();
      setFullscreen(!fullscreen);
    }
  }, [selectedItems, components, handleUndo, handleRedo, handleCopy, handlePaste, handleComponentDelete, clearSelection, hideContextMenu, fullscreen]);
  return /*#__PURE__*/react.createElement(BuilderContainer, {
    ref: builderRef,
    className: fullscreen ? 'fullscreen' : ''
  }, /*#__PURE__*/react.createElement(Toolbar, null, /*#__PURE__*/react.createElement(ToolbarSection, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Undo (Ctrl+Z)"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* UndoOutlined */.Xrf, null),
    disabled: !canUndo,
    onClick: handleUndo
  })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Redo (Ctrl+Y)"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* RedoOutlined */.zYO, null),
    disabled: !canRedo,
    onClick: handleRedo
  })), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* SaveOutlined */.ylI, null)
  }, "Save")), /*#__PURE__*/react.createElement(ToolbarSection, null, /*#__PURE__*/react.createElement("span", {
    style: {
      fontSize: 16,
      fontWeight: 600
    }
  }, "Enhanced Component Builder")), /*#__PURE__*/react.createElement(ToolbarSection, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Toggle Preview Mode"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
    type: previewMode ? 'primary' : 'default',
    onClick: function onClick() {
      return setPreviewMode(!previewMode);
    }
  }, "Preview")), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Settings"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null)
  })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Fullscreen (F11)"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: fullscreen ? /*#__PURE__*/react.createElement(icons_es/* CompressOutlined */.J5k, null) : /*#__PURE__*/react.createElement(icons_es/* FullscreenOutlined */.KrH, null),
    onClick: function onClick() {
      return setFullscreen(!fullscreen);
    }
  })))), /*#__PURE__*/react.createElement(es/* Layout */.PE, null, !previewMode && /*#__PURE__*/react.createElement(EnhancedComponentBuilder_PaletteContainer, {
    width: 300,
    theme: "light"
  }, /*#__PURE__*/react.createElement(builder_EnhancedComponentPalette, {
    onAddComponent: function onAddComponent(type) {
      var newComponent = {
        id: "".concat(type, "-").concat(Date.now()),
        type: type,
        props: getDefaultProps(type),
        createdAt: new Date().toISOString()
      };
      dispatch((0,minimal_store.addComponent)(newComponent));
      es/* message */.iU.success("".concat(type, " component added"));
    },
    onDragStart: function onDragStart(component) {
      setDraggedComponent(component);
      handleDragStart(null, component);
    },
    onDragEnd: function onDragEnd() {
      setDraggedComponent(null);
      handleDragEnd();
    }
  })), /*#__PURE__*/react.createElement(EnhancedComponentBuilder_PreviewContainer, {
    ref: dropZoneRef
  }, /*#__PURE__*/react.createElement(builder_EnhancedPreviewArea, {
    components: components,
    onSelectComponent: handleComponentSelect,
    onDeleteComponent: handleComponentDelete,
    onUpdateComponent: handleComponentUpdate,
    previewMode: previewMode,
    selectedComponentId: selectedItems[0],
    onDrop: handleComponentDrop
  }), isLoading && /*#__PURE__*/react.createElement(LoadingOverlay, null, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    align: "center"
  }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    size: "large"
  }), /*#__PURE__*/react.createElement("span", null, loadingMessage))))), /*#__PURE__*/react.createElement(builder_DragVisualFeedback, {
    isDragging: isDragging,
    isOver: isOver,
    isValid: validDropZone,
    dropPosition: dropPosition,
    draggedComponent: draggedComponent,
    showSuccess: showSuccess,
    successMessage: "Component added successfully!"
  }), /*#__PURE__*/react.createElement(builder_ContextualMenu, {
    visible: contextMenu.visible,
    x: contextMenu.x,
    y: contextMenu.y,
    onClose: hideContextMenu,
    selectedComponent: components.find(function (c) {
      return c.id === selectedItems[0];
    }),
    selectedComponents: components.filter(function (c) {
      return selectedItems.includes(c.id);
    }),
    onCopy: handleCopy,
    onPaste: handlePaste,
    onDelete: handleComponentDelete,
    clipboardHasData: clipboardHasData
  }));
};
/* harmony default export */ const builder_EnhancedComponentBuilder = (EnhancedComponentBuilder);

/***/ })

}]);