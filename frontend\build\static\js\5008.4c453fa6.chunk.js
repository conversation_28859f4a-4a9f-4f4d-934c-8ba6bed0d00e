"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5008],{

/***/ 75008:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(71468);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(86020);


var _templateObject, _templateObject2, _templateObject3, _templateObject4;






var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU.TabPane;
var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;
var ExporterContainer = _design_system__WEBPACK_IMPORTED_MODULE_6__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].spacing */ .Ay.spacing[3]);
var CodePreview = _design_system__WEBPACK_IMPORTED_MODULE_6__.styled.pre(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  background-color: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  padding: ", ";\n  overflow: auto;\n  max-height: 500px;\n  font-family: ", ";\n  font-size: ", ";\n  line-height: 1.5;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].colors */ .Ay.colors.neutral[100], _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].colors */ .Ay.colors.neutral[300], _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].borderRadius */ .Ay.borderRadius.md, _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].spacing */ .Ay.spacing[3], _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].typography */ .Ay.typography.fontFamily.code, _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].typography */ .Ay.typography.fontSize.sm);
var OptionsContainer = _design_system__WEBPACK_IMPORTED_MODULE_6__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].spacing */ .Ay.spacing[4]);
var OptionGroup = _design_system__WEBPACK_IMPORTED_MODULE_6__.styled.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].spacing */ .Ay.spacing[3]);

/**
 * CodeExporter component
 * Exports components and layouts as code in various formats
 */
var CodeExporter = function CodeExporter() {
  var components = (0,react_redux__WEBPACK_IMPORTED_MODULE_3__/* .useSelector */ .d4)(function (state) {
    var _state$app;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || [];
  });
  var layouts = (0,react_redux__WEBPACK_IMPORTED_MODULE_3__/* .useSelector */ .d4)(function (state) {
    var _state$app2;
    return ((_state$app2 = state.app) === null || _state$app2 === void 0 ? void 0 : _state$app2.layouts) || [];
  });
  var activeTheme = (0,react_redux__WEBPACK_IMPORTED_MODULE_3__/* .useSelector */ .d4)(function (state) {
    var _state$themes;
    return ((_state$themes = state.themes) === null || _state$themes === void 0 ? void 0 : _state$themes.activeTheme) || 'default';
  });
  var themes = (0,react_redux__WEBPACK_IMPORTED_MODULE_3__/* .useSelector */ .d4)(function (state) {
    var _state$themes2;
    return ((_state$themes2 = state.themes) === null || _state$themes2 === void 0 ? void 0 : _state$themes2.themes) || [];
  });
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('react'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    exportFormat = _useState2[0],
    setExportFormat = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    includeStyles = _useState4[0],
    setIncludeStyles = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    includeTheme = _useState6[0],
    setIncludeTheme = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('components'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState7, 2),
    bundleType = _useState8[0],
    setBundleType = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState9, 2),
    selectedComponents = _useState0[0],
    setSelectedComponents = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState1, 2),
    selectedLayouts = _useState10[0],
    setSelectedLayouts = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState11, 2),
    codePreview = _useState12[0],
    setCodePreview = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState13, 2),
    copied = _useState14[0],
    setCopied = _useState14[1];

  // Update selected components and layouts when they change
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (components.length > 0 && selectedComponents.length === 0) {
      setSelectedComponents(components.map(function (c) {
        return c.id;
      }));
    }
    if (layouts.length > 0 && selectedLayouts.length === 0) {
      setSelectedLayouts(layouts.map(function (l) {
        return l.id;
      }));
    }
  }, [components, layouts, selectedComponents.length, selectedLayouts.length]);

  // Generate code preview when options change
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    generateCodePreview();
  }, [exportFormat, includeStyles, includeTheme, bundleType, selectedComponents, selectedLayouts]);

  // Generate code preview based on selected options
  var generateCodePreview = function generateCodePreview() {
    // Get selected components and layouts
    var filteredComponents = components.filter(function (c) {
      return selectedComponents.includes(c.id);
    });
    var filteredLayouts = layouts.filter(function (l) {
      return selectedLayouts.includes(l.id);
    });

    // Get active theme
    var theme = themes.find(function (t) {
      return t.id === activeTheme;
    }) || themes[0];
    var code = '';
    switch (exportFormat) {
      case 'react':
        code = generateReactCode(filteredComponents, filteredLayouts, theme);
        break;
      case 'vue':
        code = generateVueCode(filteredComponents, filteredLayouts, theme);
        break;
      case 'angular':
        code = generateAngularCode(filteredComponents, filteredLayouts, theme);
        break;
      case 'html':
        code = generateHtmlCode(filteredComponents, filteredLayouts, theme);
        break;
      default:
        code = generateReactCode(filteredComponents, filteredLayouts, theme);
    }
    setCodePreview(code);
  };

  // Generate React code
  var generateReactCode = function generateReactCode(components, layouts, theme) {
    var code = '';

    // Import statements
    code += "import React from 'react';\n";
    if (includeStyles) {
      code += "import styled from 'styled-components';\n";
    }
    code += "\n";

    // Theme definition
    if (includeTheme && theme) {
      code += "// Theme definition\n";
      code += "const theme = ".concat(JSON.stringify(theme, null, 2), ";\n\n");
    }

    // Component definitions
    if (bundleType === 'components' || bundleType === 'all') {
      components.forEach(function (component) {
        code += "// ".concat(component.name, " component\n");
        if (includeStyles && component.props.customStyles) {
          code += "const Styled".concat(component.name.replace(/\s/g, ''), " = styled.div`\n");
          code += "  ".concat(component.props.customStyles, "\n");
          code += "`;\n\n";
        }
        code += "const ".concat(component.name.replace(/\s/g, ''), " = (props) => {\n");
        code += "  return (\n";
        if (includeStyles && component.props.customStyles) {
          code += "    <Styled".concat(component.name.replace(/\s/g, ''), ">\n");
          code += "      <div>".concat(component.name, "</div>\n");
          code += "    </Styled".concat(component.name.replace(/\s/g, ''), ">\n");
        } else {
          code += "    <div className=\"".concat(component.name.toLowerCase().replace(/\s/g, '-'), "\">\n");
          code += "      <div>".concat(component.name, "</div>\n");
          code += "    </div>\n";
        }
        code += "  );\n";
        code += "};\n\n";
      });
    }

    // Layout definitions
    if (bundleType === 'layouts' || bundleType === 'all') {
      layouts.forEach(function (layout) {
        var _layout$name2;
        code += "// ".concat(layout.name || 'Layout', " layout\n");
        if (includeStyles) {
          var _layout$name;
          code += "const ".concat(((_layout$name = layout.name) === null || _layout$name === void 0 ? void 0 : _layout$name.replace(/\s/g, '')) || 'Layout', "Container = styled.div`\n");
          code += "  display: grid;\n";
          code += "  grid-template-columns: repeat(12, 1fr);\n";
          code += "  gap: 16px;\n";
          code += "`;\n\n";
        }
        code += "const ".concat(((_layout$name2 = layout.name) === null || _layout$name2 === void 0 ? void 0 : _layout$name2.replace(/\s/g, '')) || 'Layout', " = () => {\n");
        code += "  return (\n";
        if (includeStyles) {
          var _layout$name3, _layout$name4;
          code += "    <".concat(((_layout$name3 = layout.name) === null || _layout$name3 === void 0 ? void 0 : _layout$name3.replace(/\s/g, '')) || 'Layout', "Container>\n");

          // Add components to layout
          if (layout.components && layout.components.length > 0) {
            layout.components.forEach(function (componentId) {
              var component = components.find(function (c) {
                return c.id === componentId;
              });
              if (component) {
                code += "      <".concat(component.name.replace(/\s/g, ''), " />\n");
              }
            });
          } else {
            code += "      {/* Add components here */}\n";
          }
          code += "    </".concat(((_layout$name4 = layout.name) === null || _layout$name4 === void 0 ? void 0 : _layout$name4.replace(/\s/g, '')) || 'Layout', "Container>\n");
        } else {
          code += "    <div className=\"".concat((layout.name || 'layout').toLowerCase().replace(/\s/g, '-'), "-container\">\n");

          // Add components to layout
          if (layout.components && layout.components.length > 0) {
            layout.components.forEach(function (componentId) {
              var component = components.find(function (c) {
                return c.id === componentId;
              });
              if (component) {
                code += "      <".concat(component.name.replace(/\s/g, ''), " />\n");
              }
            });
          } else {
            code += "      {/* Add components here */}\n";
          }
          code += "    </div>\n";
        }
        code += "  );\n";
        code += "};\n\n";
      });
    }

    // Export statements
    code += "// Exports\n";
    if (bundleType === 'components' || bundleType === 'all') {
      components.forEach(function (component) {
        code += "export { ".concat(component.name.replace(/\s/g, ''), " };\n");
      });
    }
    if (bundleType === 'layouts' || bundleType === 'all') {
      layouts.forEach(function (layout) {
        var _layout$name5;
        code += "export { ".concat(((_layout$name5 = layout.name) === null || _layout$name5 === void 0 ? void 0 : _layout$name5.replace(/\s/g, '')) || 'Layout', " };\n");
      });
    }
    return code;
  };

  // Generate Vue code (simplified for demo)
  var generateVueCode = function generateVueCode(components, layouts, theme) {
    return "// Vue.js code export is coming soon\n// Selected ".concat(components.length, " components and ").concat(layouts.length, " layouts\n// Export format: Vue.js\n// Include styles: ").concat(includeStyles ? 'Yes' : 'No', "\n// Include theme: ").concat(includeTheme ? 'Yes' : 'No', "\n// Bundle type: ").concat(bundleType);
  };

  // Generate Angular code (simplified for demo)
  var generateAngularCode = function generateAngularCode(components, layouts, theme) {
    return "// Angular code export is coming soon\n// Selected ".concat(components.length, " components and ").concat(layouts.length, " layouts\n// Export format: Angular\n// Include styles: ").concat(includeStyles ? 'Yes' : 'No', "\n// Include theme: ").concat(includeTheme ? 'Yes' : 'No', "\n// Bundle type: ").concat(bundleType);
  };

  // Generate HTML code (simplified for demo)
  var generateHtmlCode = function generateHtmlCode(components, layouts, theme) {
    return "<!-- HTML code export is coming soon -->\n<!-- Selected ".concat(components.length, " components and ").concat(layouts.length, " layouts -->\n<!-- Export format: HTML -->\n<!-- Include styles: ").concat(includeStyles ? 'Yes' : 'No', " -->\n<!-- Include theme: ").concat(includeTheme ? 'Yes' : 'No', " -->\n<!-- Bundle type: ").concat(bundleType, " -->");
  };

  // Copy code to clipboard
  var handleCopyCode = function handleCopyCode() {
    navigator.clipboard.writeText(codePreview).then(function () {
      setCopied(true);
      antd__WEBPACK_IMPORTED_MODULE_4__/* .message */ .iU.success('Code copied to clipboard');

      // Reset copied state after 2 seconds
      setTimeout(function () {
        setCopied(false);
      }, 2000);
    })["catch"](function (error) {
      console.error('Failed to copy code:', error);
      antd__WEBPACK_IMPORTED_MODULE_4__/* .message */ .iU.error('Failed to copy code');
    });
  };

  // Download code as file
  var handleDownloadCode = function handleDownloadCode() {
    var fileExtension = exportFormat === 'html' ? 'html' : 'js';
    var fileName = "app-builder-export.".concat(fileExtension);
    var blob = new Blob([codePreview], {
      type: 'text/plain'
    });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    antd__WEBPACK_IMPORTED_MODULE_4__/* .message */ .iU.success("Code downloaded as ".concat(fileName));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ExporterContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
    level: 4
  }, "Export Code"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Paragraph, null, "Export your components and layouts as code in various formats."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(OptionsContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(OptionGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    strong: true
  }, "Export Format"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6, {
    value: exportFormat,
    onChange: setExportFormat,
    style: {
      width: '100%',
      marginTop: _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].spacing */ .Ay.spacing[1]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "react"
  }, "React"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "vue"
  }, "Vue.js"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "angular"
  }, "Angular"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "html"
  }, "HTML"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(OptionGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    strong: true
  }, "Bundle Type"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Radio */ .sx.Group, {
    value: bundleType,
    onChange: function onChange(e) {
      return setBundleType(e.target.value);
    },
    style: {
      display: 'block',
      marginTop: _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].spacing */ .Ay.spacing[1]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Radio */ .sx, {
    value: "components"
  }, "Components Only"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Radio */ .sx, {
    value: "layouts"
  }, "Layouts Only"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Radio */ .sx, {
    value: "all"
  }, "Components & Layouts"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(OptionGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
    direction: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Checkbox */ .Sc, {
    checked: includeStyles,
    onChange: function onChange(e) {
      return setIncludeStyles(e.target.checked);
    }
  }, "Include Styles"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Checkbox */ .Sc, {
    checked: includeTheme,
    onChange: function onChange(e) {
      return setIncludeTheme(e.target.checked);
    }
  }, "Include Theme")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Alert */ .Fc, {
    message: "Export Preview",
    description: "This is a preview of the exported code. You can copy it to clipboard or download it as a file.",
    type: "info",
    showIcon: true,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .InfoCircleOutlined */ .rUN, null),
    style: {
      marginBottom: _design_system_theme__WEBPACK_IMPORTED_MODULE_7__/* ["default"].spacing */ .Ay.spacing[3]
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CodePreview, null, codePreview), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CopyOutlined */ .wq3, null),
    onClick: handleCopyCode
  }, copied ? 'Copied!' : 'Copy Code'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DownloadOutlined */ .jsW, null),
    onClick: handleDownloadCode
  }, "Download")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CodeExporter);

/***/ })

}]);