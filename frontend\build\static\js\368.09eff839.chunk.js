"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[368],{

/***/ 90368:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(11080);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(32389);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);

var _templateObject;






// Styled components
var UnauthorizedContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: calc(100vh - 200px);\n  padding: 24px;\n"])));

/**
 * UnauthorizedPage component
 * Displayed when a user tries to access a page they don't have permission to view
 */
var UnauthorizedPage = function UnauthorizedPage() {
  var _useTranslation = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__/* .useTranslation */ .Bd)(),
    t = _useTranslation.t;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(UnauthorizedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Result */ .Q7, {
    status: "403",
    title: t('auth.unauthorized.title'),
    subTitle: t('auth.unauthorized.subtitle'),
    extra: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
      type: "primary",
      key: "home"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_3__/* .Link */ .N_, {
      to: "/"
    }, t('auth.unauthorized.backHome'))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
      key: "login"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_3__/* .Link */ .N_, {
      to: "/login"
    }, t('auth.unauthorized.login')))]
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UnauthorizedPage);

/***/ })

}]);