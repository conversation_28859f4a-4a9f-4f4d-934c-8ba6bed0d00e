"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6685],{

/***/ 57683:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(16918);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82569);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8;

// Optimized Ant Design imports for better tree-shaking





// Animations
var rotate = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"])));
var fadeIn = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"])));

// Styled components
var ToggleContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var ToggleButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 1px solid var(--color-border);\n  background-color: var(--color-surface);\n  color: var(--color-text);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure WCAG AA contrast compliance */\n  &:hover, &:focus {\n    border-color: var(--color-primary);\n    color: var(--color-primary);\n    background-color: var(--color-background-secondary);\n    transform: scale(1.05);\n    box-shadow: var(--shadow-md);\n  }\n\n  &:focus-visible {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n\n  .anticon {\n    font-size: 18px;\n    animation: ", " 0.3s ease;\n\n    /* Ensure icon has sufficient contrast */\n    filter: contrast(1.1);\n  }\n\n  &.rotating .anticon {\n    animation: ", " 0.5s ease;\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n\n    &:hover, &:focus {\n      border-width: 3px;\n    }\n\n    .anticon {\n      filter: contrast(1.3);\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n\n    &:active {\n      transform: none;\n    }\n\n    .anticon {\n      animation: none;\n    }\n\n    &.rotating .anticon {\n      animation: none;\n    }\n  }\n"])), fadeIn, rotate);
var DropdownContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  min-width: 180px;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: 8px;\n  box-shadow: var(--shadow-lg);\n  padding: 4px;\n"])));
var ThemeOption = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-radius: 6px;\n  margin: 2px 0;\n  color: var(--color-text);\n\n  &:hover {\n    background-color: var(--color-background-secondary);\n    transform: translateX(2px);\n  }\n\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 1px;\n  }\n\n  &.active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-sm);\n  }\n\n  .option-content {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n  }\n\n  .anticon {\n    font-size: 16px;\n\n    /* Ensure icon contrast in active state */\n    filter: ", ";\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 1px solid var(--color-border);\n\n    &:hover {\n      border-color: var(--color-primary);\n    }\n\n    &.active {\n      border: 2px solid white;\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: background-color 0.2s ease;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"])), function (props) {
  var _props$className;
  return (_props$className = props.className) !== null && _props$className !== void 0 && _props$className.includes('active') ? 'none' : 'contrast(1.1)';
});
var ThemeLabel = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.span(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n"])));
var ThemeDescription = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: ", ";\n  margin-top: 2px;\n  line-height: 1.2;\n\n  /* Ensure description text meets contrast requirements */\n  opacity: ", ";\n"])), function (props) {
  return props.active ? 'rgba(255, 255, 255, 0.8)' : 'var(--color-text-secondary)';
}, function (props) {
  return props.active ? 0.9 : 0.8;
});
var DarkModeToggle = function DarkModeToggle(_ref) {
  var _ref$showDropdown = _ref.showDropdown,
    showDropdown = _ref$showDropdown === void 0 ? true : _ref$showDropdown,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'default' : _ref$size;
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    themeMode = _useEnhancedTheme.themeMode,
    toggleDarkMode = _useEnhancedTheme.toggleDarkMode,
    setThemeMode = _useEnhancedTheme.setThemeMode,
    systemPrefersDark = _useEnhancedTheme.systemPrefersDark;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    isRotating = _useState2[0],
    setIsRotating = _useState2[1];
  var handleToggle = function handleToggle() {
    setIsRotating(true);
    toggleDarkMode();
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var handleThemeChange = function handleThemeChange(mode) {
    setIsRotating(true);
    setThemeMode(mode);
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var getIcon = function getIcon() {
    if (themeMode === 'system') {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null);
    }
    return isDarkMode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null);
  };
  var getTooltipTitle = function getTooltipTitle() {
    switch (themeMode) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return "System mode (".concat(systemPrefersDark ? 'dark' : 'light', ")");
      default:
        return 'Toggle theme';
    }
  };
  var themeOptions = [{
    key: 'light',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null),
    label: 'Light',
    description: 'Light theme'
  }, {
    key: 'dark',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null),
    label: 'Dark',
    description: 'Dark theme'
  }, {
    key: 'system',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null),
    label: 'System',
    description: 'Follow system preference'
  }];
  var dropdownMenu = {
    items: themeOptions.map(function (option) {
      return {
        key: option.key,
        label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeOption, {
          className: themeMode === option.key ? 'active' : '',
          onClick: function onClick() {
            return handleThemeChange(option.key);
          },
          role: "menuitem",
          tabIndex: 0,
          "aria-selected": themeMode === option.key,
          onKeyDown: function onKeyDown(e) {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleThemeChange(option.key);
            }
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
          className: "option-content"
        }, option.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeLabel, null, option.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeDescription, {
          active: themeMode === option.key
        }, option.description))), themeMode === option.key && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckOutlined */ .JIb, {
          "aria-label": "Selected"
        }))
      };
    })
  };
  if (!showDropdown) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: getTooltipTitle(),
      placement: "bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
      type: "text",
      size: size,
      className: isRotating ? 'rotating' : '',
      onClick: handleToggle,
      "aria-label": "Switch to ".concat(isDarkMode ? 'light' : 'dark', " mode. Current theme: ").concat(getTooltipTitle()),
      "aria-pressed": isDarkMode,
      role: "switch"
    }, getIcon())));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Dropdown */ .ms, {
    menu: dropdownMenu,
    trigger: ['click'],
    placement: "bottomRight",
    arrow: true,
    dropdownRender: function dropdownRender(menu) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DropdownContent, {
        role: "menu",
        "aria-label": "Theme selection menu"
      }, menu);
    },
    onOpenChange: function onOpenChange(open) {
      // Announce to screen readers when menu opens/closes
      if (open) {
        // Focus management for accessibility
        setTimeout(function () {
          var firstMenuItem = document.querySelector('[role="menuitem"]');
          if (firstMenuItem) {
            firstMenuItem.focus();
          }
        }, 100);
      }
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: getTooltipTitle(),
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
    type: "text",
    size: size,
    className: isRotating ? 'rotating' : '',
    "aria-label": "Theme options menu. Current theme: ".concat(getTooltipTitle()),
    "aria-haspopup": "menu",
    "aria-expanded": "false",
    role: "button"
  }, getIcon()))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DarkModeToggle);

/***/ }),

/***/ 61160:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(82569);
/* harmony import */ var _ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57683);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;






var Title = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Title,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Paragraph,
  Text = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Text;
var TestContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: var(--spacing-lg);\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"])));
var TestCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  .ant-card-head {\n    background-color: var(--color-background-secondary);\n    border-bottom: 1px solid var(--color-border-light);\n  }\n\n  .ant-card-head-title {\n    color: var(--color-text);\n  }\n"])));
var ContrastGrid = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-md);\n  margin: var(--spacing-md) 0;\n"])));
var ContrastBox = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border);\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n  }\n"])));
var StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius-sm);\n  font-size: 12px;\n  font-weight: 500;\n  margin: var(--spacing-xs);\n"])));
var ContrastTest = function ContrastTest() {
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_3__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    colors = _useEnhancedTheme.colors,
    themeMode = _useEnhancedTheme.themeMode;
  var statusVariants = [{
    type: 'success',
    color: '#52c41a',
    bg: 'rgba(82, 196, 26, 0.1)',
    text: 'Success Status'
  }, {
    type: 'warning',
    color: '#faad14',
    bg: 'rgba(250, 173, 20, 0.1)',
    text: 'Warning Status'
  }, {
    type: 'error',
    color: '#ff4d4f',
    bg: 'rgba(255, 77, 79, 0.1)',
    text: 'Error Status'
  }, {
    type: 'info',
    color: '#1890ff',
    bg: 'rgba(24, 144, 255, 0.1)',
    text: 'Info Status'
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(TestContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(TestCard, {
    title: "Text Contrast & Visibility Test"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      marginBottom: '24px',
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 3
  }, "Current Theme: ", themeMode, " mode"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Typography Hierarchy"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    size: "large",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 1,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 1"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 2,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 2"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 3,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 3"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 4,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 4"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 5,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 5")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Paragraph, {
    style: {
      color: 'var(--color-text)'
    }
  }, "This is a regular paragraph with normal text color. It should be easily readable against the current background in both light and dark modes."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "secondary"
  }, "This is secondary text that should have good contrast."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "success"
  }, "Success text should be visible and accessible."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "warning"
  }, "Warning text should stand out appropriately."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "danger"
  }, "Error text should be clearly visible."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    disabled: true
  }, "Disabled text should be distinguishable but subdued."))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Status Indicators"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, statusVariants.map(function (status, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(StatusIndicator, {
      key: index,
      style: {
        color: status.color,
        backgroundColor: status.bg,
        border: "1px solid ".concat(status.color)
      }
    }, status.type === 'success' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckCircleOutlined */ .hWy, null), status.type === 'warning' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ExclamationCircleOutlined */ .G2i, null), status.type === 'error' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseCircleOutlined */ .bBN, null), status.type === 'info' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, null), status.text);
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Interactive Elements"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    wrap: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary"
  }, "Primary Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "default"
  }, "Default Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "dashed"
  }, "Dashed Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "text"
  }, "Text Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "link"
  }, "Link Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    danger: true
  }, "Danger Button")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Tags and Badges"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    wrap: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tag */ .vw, {
    color: "blue"
  }, "Blue Tag"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tag */ .vw, {
    color: "green"
  }, "Green Tag"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tag */ .vw, {
    color: "orange"
  }, "Orange Tag"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tag */ .vw, {
    color: "red"
  }, "Red Tag"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tag */ .vw, {
    color: "purple"
  }, "Purple Tag"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Badge */ .Ex, {
    count: 5,
    style: {
      backgroundColor: '#52c41a'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      width: 40,
      height: 40,
      backgroundColor: 'var(--color-background-secondary)',
      border: '1px solid var(--color-border)',
      borderRadius: '4px'
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Alerts"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Alert */ .Fc, {
    message: "Success Alert",
    type: "success",
    showIcon: true
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Alert */ .Fc, {
    message: "Info Alert",
    type: "info",
    showIcon: true
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Alert */ .Fc, {
    message: "Warning Alert",
    type: "warning",
    showIcon: true
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Alert */ .Fc, {
    message: "Error Alert",
    type: "error",
    showIcon: true
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Progress Indicators"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Progress */ .ke, {
    percent: 30,
    status: "active"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Progress */ .ke, {
    percent: 50,
    status: "normal"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Progress */ .ke, {
    percent: 70,
    status: "exception"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Progress */ .ke, {
    percent: 100
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Background Variations"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ContrastGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-surface)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, "Surface Background")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-background-secondary)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, "Secondary Background")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-background-tertiary)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, "Tertiary Background")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-primary)',
      color: 'white'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    style: {
      color: 'white'
    }
  }, "Primary Background"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null, "Theme Information"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      backgroundColor: 'var(--color-background-secondary)',
      padding: '16px',
      borderRadius: '8px',
      border: '1px solid var(--color-border-light)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "Current Theme:"), " ", isDarkMode ? 'Dark' : 'Light', " Mode", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "Theme Mode Setting:"), " ", themeMode, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "Primary Color:"), " ", colors.primary, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "Background Color:"), " ", colors.background, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "Text Color:"), " ", colors.text))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContrastTest);

/***/ })

}]);